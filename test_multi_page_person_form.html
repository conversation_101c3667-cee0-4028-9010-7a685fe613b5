<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Multi-Page Person Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 6px; }
        .form-section h3 { margin-top: 0; color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; font-size: 14px; }
        textarea { height: 80px; resize: vertical; }
        button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin-right: 10px; }
        button:hover { background: #005a87; }
        button.secondary { background: #6c757d; }
        button.secondary:hover { background: #545b62; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .menu-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .menu-item { padding: 15px; border: 2px solid #ddd; border-radius: 8px; cursor: pointer; text-align: center; transition: all 0.3s; }
        .menu-item:hover { border-color: #007cba; background: #f8f9fa; }
        .menu-icon { font-size: 2em; margin-bottom: 10px; }
        .menu-title { font-weight: bold; margin-bottom: 5px; }
        .menu-desc { font-size: 0.9em; color: #666; }
        .hidden { display: none; }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 30px; }
        .step { padding: 10px 20px; margin: 0 5px; border-radius: 20px; background: #e9ecef; color: #6c757d; }
        .step.active { background: #007cba; color: white; }
        .step.completed { background: #28a745; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Multi-Page Person Form - Test Implementation</h1>
        <p>This demonstrates the new multi-page approach for creating person records with linked information.</p>
        
        <div class="step-indicator">
            <div class="step active" id="step1">1. Basic Info</div>
            <div class="step" id="step2">2. Additional Details</div>
        </div>

        <!-- Step 1: Basic Person Information -->
        <div id="basic-form" class="form-section">
            <h3>Step 1: Basic Person Information</h3>
            <form id="personForm">
                <div class="form-group">
                    <label for="first_name">First Name *</label>
                    <input type="text" id="first_name" name="first_name" required>
                </div>
                
                <div class="form-group">
                    <label for="last_name">Last Name *</label>
                    <input type="text" id="last_name" name="last_name" required>
                </div>
                
                <div class="form-group">
                    <label for="date_of_birth">Date of Birth</label>
                    <input type="date" id="date_of_birth" name="date_of_birth">
                </div>
                
                <div class="form-group">
                    <label for="phone">Phone</label>
                    <input type="tel" id="phone" name="phone">
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email">
                </div>
                
                <div class="form-group">
                    <label for="preferred_pronouns">Preferred Pronouns</label>
                    <input type="text" id="preferred_pronouns" name="preferred_pronouns" placeholder="e.g., he/him, she/her, they/them">
                </div>
                
                <div class="form-group">
                    <label for="primary_language">Primary Language</label>
                    <select id="primary_language" name="primary_language">
                        <option value="">Select...</option>
                        <option value="English">English</option>
                        <option value="French">French</option>
                        <option value="Spanish">Spanish</option>
                        <option value="Indigenous Language">Indigenous Language</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="housing_status">Housing Status</label>
                    <select id="housing_status" name="housing_status">
                        <option value="">Select...</option>
                        <option value="Housed">Housed</option>
                        <option value="Temporarily Housed">Temporarily Housed</option>
                        <option value="Unsheltered">Unsheltered</option>
                        <option value="Emergency Shelter">Emergency Shelter</option>
                        <option value="Transitional Housing">Transitional Housing</option>
                        <option value="Unknown">Unknown</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="income_source">Income Source</label>
                    <select id="income_source" name="income_source">
                        <option value="">Select...</option>
                        <option value="Employment">Employment</option>
                        <option value="Social Assistance">Social Assistance</option>
                        <option value="Disability Benefits">Disability Benefits</option>
                        <option value="Pension">Pension</option>
                        <option value="Employment Insurance">Employment Insurance</option>
                        <option value="None">None</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="risk_level">Risk Level</label>
                    <select id="risk_level" name="risk_level">
                        <option value="">Select...</option>
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="has_id_documents">Has ID Documents</label>
                    <select id="has_id_documents" name="has_id_documents">
                        <option value="">Select...</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="veteran_status">Veteran Status</label>
                    <select id="veteran_status" name="veteran_status">
                        <option value="">Select...</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea id="notes" name="notes" placeholder="Additional notes"></textarea>
                </div>
                
                <button type="submit">Create Person Record</button>
                <button type="button" class="secondary" onclick="window.close()">Cancel</button>
            </form>
        </div>

        <!-- Step 2: Additional Information Options -->
        <div id="additional-options" class="form-section hidden">
            <h3>Step 2: Additional Information</h3>
            <div class="success">
                ✅ Person record created successfully! You can now add additional information or finish.
            </div>
            
            <p>Select what additional information you'd like to add:</p>
            
            <div class="menu-grid">
                <div class="menu-item" data-action="medical">
                    <div class="menu-icon">🏥</div>
                    <div class="menu-title">Medical Conditions</div>
                    <div class="menu-desc">Add medical conditions, medications, or health information</div>
                </div>
                <div class="menu-item" data-action="disabilities">
                    <div class="menu-icon">♿</div>
                    <div class="menu-title">Disabilities</div>
                    <div class="menu-desc">Add disability information and accommodation needs</div>
                </div>
                <div class="menu-item" data-action="case-management">
                    <div class="menu-icon">📋</div>
                    <div class="menu-title">Case Management</div>
                    <div class="menu-desc">Add case manager and case information</div>
                </div>
                <div class="menu-item" data-action="barriers">
                    <div class="menu-icon">🚧</div>
                    <div class="menu-title">Service Barriers</div>
                    <div class="menu-desc">Add barriers to accessing services</div>
                </div>
                <div class="menu-item" data-action="support">
                    <div class="menu-icon">👥</div>
                    <div class="menu-title">Support Network</div>
                    <div class="menu-desc">Add support contacts and emergency contacts</div>
                </div>
                <div class="menu-item" data-action="pets">
                    <div class="menu-icon">🐕</div>
                    <div class="menu-title">Pets</div>
                    <div class="menu-desc">Add pet information</div>
                </div>
            </div>
            
            <button type="button" class="secondary" onclick="finishProcess()">Done - Finish</button>
        </div>

        <div id="result"></div>
    </div>
    
    <script>
        let personRecord = null;
        
        document.getElementById('personForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // Calculate age if date_of_birth is provided
            if (data.date_of_birth) {
                const birthDate = new Date(data.date_of_birth);
                const today = new Date();
                let age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();
                
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }
                
                data.age = age >= 0 ? age : null;
            }
            
            // Simulate person record creation
            personRecord = {
                id: Math.floor(Math.random() * 10000),
                ...data,
                created_at: new Date().toISOString()
            };
            
            // Move to step 2
            document.getElementById('step1').classList.remove('active');
            document.getElementById('step1').classList.add('completed');
            document.getElementById('step2').classList.add('active');
            
            document.getElementById('basic-form').classList.add('hidden');
            document.getElementById('additional-options').classList.remove('hidden');
            
            console.log('Person record created:', personRecord);
        });
        
        // Handle additional info selection
        document.addEventListener('click', function(e) {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                const action = menuItem.dataset.action;
                showAdditionalForm(action);
            }
        });
        
        function showAdditionalForm(action) {
            const actionNames = {
                'medical': 'Medical Condition',
                'disabilities': 'Disability Information',
                'case-management': 'Case Management Information',
                'barriers': 'Service Barrier',
                'support': 'Support Contact',
                'pets': 'Pet Information'
            };
            
            alert(`This would open a form to add: ${actionNames[action]}\n\nPerson ID: ${personRecord.id}\nName: ${personRecord.first_name} ${personRecord.last_name}`);
            
            // In the real implementation, this would open the appropriate linked record form
        }
        
        function finishProcess() {
            document.getElementById('result').innerHTML = `
                <div class="success">
                    <h3>Process Complete!</h3>
                    <p>Person record and any additional information has been saved successfully.</p>
                    <pre>${JSON.stringify(personRecord, null, 2)}</pre>
                </div>
            `;
            
            document.getElementById('additional-options').classList.add('hidden');
        }
    </script>
</body>
</html>
