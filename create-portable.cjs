#!/usr/bin/env node

// Create portable ZIP version of S.T.E.V.I Retro
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('📦 Creating Portable S.T.E.V.I Retro ZIP...\n');

try {
    // Step 1: Check if win-unpacked exists
    console.log('1️⃣ Checking for unpacked build...');
    const unpackedDir = './win-unpacked';
    
    if (!fs.existsSync(unpackedDir)) {
        console.log('   Building unpacked version first...');
        execSync('npx electron-builder --win --dir', { stdio: 'inherit' });
    }

    // Step 2: Create portable launcher script
    console.log('\n2️⃣ Creating portable launcher...');
    const launcherScript = `@echo off
title S.T.E.V.I Retro - Portable
echo Starting S.T.E.V.I Retro (Portable Version)...
echo.

REM Set portable mode
set STEVI_PORTABLE=1
set STEVI_DATA_DIR=%~dp0data

REM Create data directory if it doesn't exist
if not exist "%STEVI_DATA_DIR%" mkdir "%STEVI_DATA_DIR%"

REM Launch the application
"%~dp0S.T.E.V.I Retro.exe"

REM Check if app closed normally
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Application closed with error code: %ERRORLEVEL%
    echo Press any key to close this window...
    pause >nul
)
`;

    fs.writeFileSync(path.join(unpackedDir, 'Launch S.T.E.V.I Retro.bat'), launcherScript);

    // Step 3: Create README for portable version
    console.log('3️⃣ Creating portable README...');
    const portableReadme = `# S.T.E.V.I Retro - Portable Version

## Quick Start
1. Extract this ZIP to any folder
2. Double-click "Launch S.T.E.V.I Retro.bat"
3. The application will start in portable mode

## Portable Mode Features
- No installation required
- All data stored in local "data" folder
- Can run from USB drive or any location
- Settings and reports saved locally

## System Requirements
- Windows 10 or 11 (64-bit)
- No additional software required

## Files Included
- S.T.E.V.I Retro.exe - Main application
- Launch S.T.E.V.I Retro.bat - Portable launcher
- resources/ - Application resources
- All required DLLs and dependencies

## Support
For support, visit: https://github.com/iharc-jordan/stevi_retro

Version: 1.0.9
Build Date: ${new Date().toLocaleDateString()}
`;

    fs.writeFileSync(path.join(unpackedDir, 'README.txt'), portableReadme);

    // Step 4: Create the ZIP file
    console.log('4️⃣ Creating ZIP archive...');
    const zipName = 'S.T.E.V.I-Retro-Portable-v1.0.9.zip';
    
    // Use PowerShell to create ZIP (built into Windows 10+)
    const powershellCommand = `Compress-Archive -Path "${unpackedDir}\\*" -DestinationPath "${zipName}" -Force`;
    execSync(`powershell -Command "${powershellCommand}"`, { stdio: 'inherit' });

    // Step 5: Verify the ZIP was created
    console.log('\n5️⃣ Verifying portable package...');
    
    if (fs.existsSync(zipName)) {
        const stats = fs.statSync(zipName);
        const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
        
        console.log('✅ Portable ZIP created successfully!');
        console.log(`📁 Location: ${path.resolve(zipName)}`);
        console.log(`📊 Size: ${fileSizeMB} MB`);
        console.log(`📅 Created: ${stats.mtime.toLocaleString()}`);
        
        console.log('\n🎯 Portable Installation Instructions:');
        console.log('1. Download and extract the ZIP file');
        console.log('2. Double-click "Launch S.T.E.V.I Retro.bat"');
        console.log('3. Application runs without installation');
        console.log('4. All data stored in local "data" folder');
        
        console.log('\n✨ Portable Features:');
        console.log('• No installation required');
        console.log('• Runs from any location (USB, network drive, etc.)');
        console.log('• Self-contained with all dependencies');
        console.log('• Local data storage');
        console.log('• Easy to backup and move');
        
    } else {
        throw new Error('ZIP file was not created');
    }

} catch (error) {
    console.error('\n❌ Portable build failed:', error.message);
    process.exit(1);
}

console.log('\n🎉 Portable version created successfully!');
