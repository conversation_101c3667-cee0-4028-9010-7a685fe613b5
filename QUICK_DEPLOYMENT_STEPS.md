# Quick Edge Function Deployment Steps

## 🚀 Step-by-Step Deployment

### 1. Create Edge Function in Supabase Dashboard

1. **Go to Supabase Dashboard**
   - Visit: https://supabase.com/dashboard
   - Select your IHARC project

2. **Navigate to Edge Functions**
   - Click "Edge Functions" in the left sidebar
   - Click "Create a new function"

3. **Create Function**
   - Function name: `admin-user-management`
   - Click "Create function"

4. **Copy Function Code**
   - Open the file `EDGE_FUNCTION_CODE.ts` from this project
   - Copy the ENTIRE contents
   - Paste into the Supabase function editor (replace all existing code)
   - Click "Deploy"

### 2. Add Service Role Key Environment Variable

1. **Go to Project Settings**
   - Click "Settings" in the left sidebar
   - Go to "Edge Functions" section

2. **Add Environment Variable**
   - Click "Add new variable"
   - Name: `SUPABASE_SERVICE_ROLE_KEY`
   - Value: Your service role key (get from Settings > API > service_role key)
   - Click "Save"

### 3. Test the Function

1. **Get Function URL**
   ```
   https://vfavknkfiiclzgpjpntj.supabase.co/functions/v1/admin-user-management
   ```

2. **Test with Admin User**
   - Log into S.T.E.V.I Retro as an admin user
   - Go to Admin tab → User Management
   - Should see list of users

## ✅ Verification Checklist

- [ ] Edge Function created with name `admin-user-management`
- [ ] Function code copied from `EDGE_FUNCTION_CODE.ts`
- [ ] Function deployed successfully (no errors)
- [ ] Environment variable `SUPABASE_SERVICE_ROLE_KEY` added
- [ ] Service role key value is correct
- [ ] Admin user can access User Management tab
- [ ] Users list loads successfully
- [ ] Can create new users
- [ ] Can promote/demote user roles
- [ ] Can reset passwords
- [ ] Can delete users
- [ ] All actions appear in audit logs

## 🚨 If Something Goes Wrong

### Function Won't Deploy
- Make sure you copied the ENTIRE code from `EDGE_FUNCTION_CODE.ts`
- Check there are no syntax errors
- Ensure function name is exactly `admin-user-management`

### "Service role key not found" Error
- Verify environment variable name is exactly `SUPABASE_SERVICE_ROLE_KEY`
- Check the service role key value is correct
- Get fresh key from Settings > API if needed

### "Admin privileges required" Error
- Ensure your user has `iharc_admin` role in user_metadata
- Check you're logged in as admin user
- Verify JWT token is valid

### Users Not Loading
- Check function logs in Supabase Dashboard
- Verify service role key has admin permissions
- Test function URL directly with curl

## 📞 Support

If you encounter issues:
1. Check function logs in Supabase Dashboard
2. Verify all environment variables are set
3. Test with a simple GET request first
4. Ensure user has proper admin role

The Edge Function approach is much simpler than the separate server - just one function to deploy and one environment variable to set!
