// Secure Session Manager for S.T.E.V.I DOS Electron App
// Handles secure session storage and management

export class SecureSessionManager {
    constructor() {
        this.sessionKey = 'stevidos_secure_session';
        this.sessionTimeout = 3600000; // 1 hour default
        this.refreshThreshold = 300000; // 5 minutes before expiry
        this.refreshTimer = null;
        this.onSessionExpired = null;
        this.onSessionRefreshed = null;
    }

    /**
     * Store session data securely
     * @param {Object} sessionData - Session data to store
     * @param {number} expiryMinutes - Session expiry in minutes (default: 60)
     */
    storeSession(sessionData, expiryMinutes = 60) {
        try {
            const expiryTime = Date.now() + (expiryMinutes * 60 * 1000);
            
            const secureSession = {
                data: sessionData,
                expiresAt: expiryTime,
                createdAt: Date.now(),
                lastActivity: Date.now()
            };

            // Store in localStorage (in Electron, this is isolated per app)
            localStorage.setItem(this.session<PERSON>ey, JSON.stringify(secureSession));
            
            // Set up automatic refresh
            this.scheduleRefresh(expiryTime);
            
            console.log('✅ Session stored securely');
            return true;

        } catch (error) {
            console.error('❌ Failed to store session:', error);
            return false;
        }
    }

    /**
     * Retrieve session data if valid
     * @returns {Object|null} Session data or null if invalid/expired
     */
    getSession() {
        try {
            const storedSession = localStorage.getItem(this.sessionKey);
            if (!storedSession) {
                return null;
            }

            const session = JSON.parse(storedSession);
            const now = Date.now();

            // Check if session has expired
            if (session.expiresAt <= now) {
                console.warn('⚠️ Session expired, clearing...');
                this.clearSession();
                return null;
            }

            // Update last activity
            session.lastActivity = now;
            localStorage.setItem(this.sessionKey, JSON.stringify(session));

            return session.data;

        } catch (error) {
            console.error('❌ Failed to retrieve session:', error);
            this.clearSession();
            return null;
        }
    }

    /**
     * Check if session is valid and not expired
     * @returns {boolean} True if session is valid
     */
    isSessionValid() {
        const session = this.getSession();
        return session !== null;
    }

    /**
     * Update session data
     * @param {Object} updates - Updates to apply to session data
     * @returns {boolean} Success status
     */
    updateSession(updates) {
        try {
            const storedSession = localStorage.getItem(this.sessionKey);
            if (!storedSession) {
                return false;
            }

            const session = JSON.parse(storedSession);
            
            // Check if session is still valid
            if (session.expiresAt <= Date.now()) {
                this.clearSession();
                return false;
            }

            // Update session data
            session.data = { ...session.data, ...updates };
            session.lastActivity = Date.now();

            localStorage.setItem(this.sessionKey, JSON.stringify(session));
            
            console.log('✅ Session updated');
            return true;

        } catch (error) {
            console.error('❌ Failed to update session:', error);
            return false;
        }
    }

    /**
     * Extend session expiry
     * @param {number} additionalMinutes - Additional minutes to extend
     * @returns {boolean} Success status
     */
    extendSession(additionalMinutes = 60) {
        try {
            const storedSession = localStorage.getItem(this.sessionKey);
            if (!storedSession) {
                return false;
            }

            const session = JSON.parse(storedSession);
            const now = Date.now();
            
            // Check if session is still valid
            if (session.expiresAt <= now) {
                this.clearSession();
                return false;
            }

            // Extend expiry
            session.expiresAt = now + (additionalMinutes * 60 * 1000);
            session.lastActivity = now;

            localStorage.setItem(this.sessionKey, JSON.stringify(session));
            
            // Reschedule refresh
            this.scheduleRefresh(session.expiresAt);
            
            console.log('✅ Session extended');
            return true;

        } catch (error) {
            console.error('❌ Failed to extend session:', error);
            return false;
        }
    }

    /**
     * Clear session data
     */
    clearSession() {
        try {
            localStorage.removeItem(this.sessionKey);
            
            // Clear refresh timer
            if (this.refreshTimer) {
                clearTimeout(this.refreshTimer);
                this.refreshTimer = null;
            }
            
            console.log('✅ Session cleared');

        } catch (error) {
            console.error('❌ Failed to clear session:', error);
        }
    }

    /**
     * Get session metadata (without sensitive data)
     * @returns {Object|null} Session metadata
     */
    getSessionMetadata() {
        try {
            const storedSession = localStorage.getItem(this.sessionKey);
            if (!storedSession) {
                return null;
            }

            const session = JSON.parse(storedSession);
            
            return {
                createdAt: session.createdAt,
                expiresAt: session.expiresAt,
                lastActivity: session.lastActivity,
                timeRemaining: Math.max(0, session.expiresAt - Date.now()),
                isExpired: session.expiresAt <= Date.now()
            };

        } catch (error) {
            console.error('❌ Failed to get session metadata:', error);
            return null;
        }
    }

    /**
     * Schedule automatic session refresh
     * @param {number} expiryTime - Session expiry timestamp
     */
    scheduleRefresh(expiryTime) {
        // Clear existing timer
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
        }

        const timeUntilRefresh = expiryTime - Date.now() - this.refreshThreshold;
        
        if (timeUntilRefresh > 0) {
            this.refreshTimer = setTimeout(() => {
                this.attemptRefresh();
            }, timeUntilRefresh);
            
            console.log(`🔄 Session refresh scheduled in ${Math.round(timeUntilRefresh / 1000)} seconds`);
        }
    }

    /**
     * Attempt to refresh the session
     */
    async attemptRefresh() {
        try {
            console.log('🔄 Attempting session refresh...');
            
            if (this.onSessionRefreshed && typeof this.onSessionRefreshed === 'function') {
                const success = await this.onSessionRefreshed();
                if (success) {
                    console.log('✅ Session refreshed successfully');
                } else {
                    console.warn('⚠️ Session refresh failed');
                    this.handleSessionExpiry();
                }
            } else {
                console.warn('⚠️ No session refresh handler configured');
                this.handleSessionExpiry();
            }

        } catch (error) {
            console.error('❌ Session refresh error:', error);
            this.handleSessionExpiry();
        }
    }

    /**
     * Handle session expiry
     */
    handleSessionExpiry() {
        console.warn('⚠️ Session expired');
        this.clearSession();
        
        if (this.onSessionExpired && typeof this.onSessionExpired === 'function') {
            this.onSessionExpired();
        }
    }

    /**
     * Set session expiry callback
     * @param {Function} callback - Function to call when session expires
     */
    setSessionExpiredCallback(callback) {
        this.onSessionExpired = callback;
    }

    /**
     * Set session refresh callback
     * @param {Function} callback - Function to call to refresh session
     */
    setSessionRefreshCallback(callback) {
        this.onSessionRefreshed = callback;
    }

    /**
     * Check for session activity and extend if needed
     */
    updateActivity() {
        const metadata = this.getSessionMetadata();
        if (metadata && !metadata.isExpired) {
            // If less than 30 minutes remaining, extend session
            if (metadata.timeRemaining < 1800000) { // 30 minutes
                this.extendSession(60); // Extend by 1 hour
            }
        }
    }
}
