-- Complete Core Schema Migration for S.T.E.V.I Retro
-- This script properly migrates all data from public schema to core schema
-- and creates the missing functions following Supabase best practices

-- Step 1: Migrate data from public schema to core schema
INSERT INTO core.roles (id, name, display_name, description, is_system_role, created_at, updated_at)
SELECT id, name, display_name, description, is_system_role, created_at, updated_at
FROM public.roles
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description,
    is_system_role = EXCLUDED.is_system_role,
    updated_at = EXCLUDED.updated_at;

INSERT INTO core.permissions (id, name, description, category, created_at, updated_at)
SELECT id, name, description, category, created_at, updated_at
FROM public.permissions
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    category = EXCLUDED.category,
    updated_at = EXCLUDED.updated_at;

INSERT INTO core.role_permissions (id, role_id, permission_id, granted_by, granted_at)
SELECT id, role_id, permission_id, granted_by, granted_at
FROM public.role_permissions
ON CONFLICT (id) DO UPDATE SET
    role_id = EXCLUDED.role_id,
    permission_id = EXCLUDED.permission_id,
    granted_by = EXCLUDED.granted_by,
    granted_at = EXCLUDED.granted_at;

INSERT INTO core.user_roles (id, user_id, role_id, assigned_by, assigned_at, expires_at, created_at)
SELECT id, user_id, role_id, assigned_by, assigned_at, expires_at, created_at
FROM public.user_roles
ON CONFLICT (id) DO UPDATE SET
    user_id = EXCLUDED.user_id,
    role_id = EXCLUDED.role_id,
    assigned_by = EXCLUDED.assigned_by,
    assigned_at = EXCLUDED.assigned_at,
    expires_at = EXCLUDED.expires_at;

-- Step 2: Create missing functions in public schema that call core schema functions
-- This ensures backward compatibility while using core schema data

-- Function to get user roles (calls core schema)
CREATE OR REPLACE FUNCTION public.get_user_roles(user_uuid UUID DEFAULT auth.uid())
RETURNS TABLE(role_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RETURN QUERY
        SELECT r.name
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = user_uuid
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW());
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Function to get user permissions (calls core schema)
CREATE OR REPLACE FUNCTION public.get_user_permissions(user_uuid UUID DEFAULT auth.uid())
RETURNS TABLE(permission_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RETURN QUERY
        SELECT DISTINCT p.name
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        JOIN core.role_permissions rp ON r.id = rp.role_id
        JOIN core.permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = user_uuid
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW());
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Update has_permission function to use core schema
CREATE OR REPLACE FUNCTION public.has_permission(permission_name TEXT, user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN (
        user_uuid IS NOT NULL AND 
        EXISTS (
            SELECT 1 
            FROM core.user_roles ur
            JOIN core.roles r ON ur.role_id = r.id
            JOIN core.role_permissions rp ON r.id = rp.role_id
            JOIN core.permissions p ON rp.permission_id = p.id
            WHERE ur.user_id = user_uuid 
            AND p.name = permission_name
            AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
        )
    );
END;
$$;

-- Function to refresh user permissions (calls core schema)
CREATE OR REPLACE FUNCTION public.refresh_user_permissions(user_uuid UUID DEFAULT auth.uid())
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSONB;
BEGIN
    -- Build the permissions and roles JSON
    SELECT jsonb_build_object(
        'roles', COALESCE(roles_array.roles, '[]'::jsonb),
        'permissions', COALESCE(permissions_array.permissions, '[]'::jsonb)
    ) INTO result
    FROM (
        SELECT jsonb_agg(DISTINCT r.name) as roles
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = user_uuid
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    ) roles_array
    CROSS JOIN (
        SELECT jsonb_agg(DISTINCT p.name) as permissions
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        JOIN core.role_permissions rp ON r.id = rp.role_id
        JOIN core.permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = user_uuid
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    ) permissions_array;
    
    -- Update user's app_metadata with roles and permissions
    UPDATE auth.users 
    SET raw_app_meta_data = jsonb_set(
        COALESCE(raw_app_meta_data, '{}'::jsonb),
        '{claims}',
        result
    )
    WHERE id = user_uuid;
    
    RETURN result;
END;
$$;

-- Function to assign role to user (admin only, uses core schema)
CREATE OR REPLACE FUNCTION public.assign_user_role(
    target_user_id UUID,
    role_name TEXT,
    assigned_by UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    role_id UUID;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Get the role ID
    SELECT id INTO role_id FROM core.roles WHERE name = role_name;
    IF role_id IS NULL THEN
        RAISE EXCEPTION 'Role not found: %', role_name;
    END IF;
    
    -- Insert or update the role assignment
    INSERT INTO core.user_roles (user_id, role_id, assigned_by)
    VALUES (target_user_id, role_id, assigned_by)
    ON CONFLICT (user_id, role_id) 
    DO UPDATE SET 
        assigned_by = EXCLUDED.assigned_by,
        assigned_at = NOW();
    
    -- Refresh user permissions
    PERFORM public.refresh_user_permissions(target_user_id);
    
    RETURN TRUE;
END;
$$;

-- Function to remove user role (admin only, uses core schema)
CREATE OR REPLACE FUNCTION public.remove_user_role(
    target_user_id UUID,
    role_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    role_id UUID;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Get the role ID
    SELECT id INTO role_id FROM core.roles WHERE name = role_name;
    IF role_id IS NULL THEN
        RAISE EXCEPTION 'Role not found: %', role_name;
    END IF;
    
    -- Remove the role assignment
    DELETE FROM core.user_roles 
    WHERE user_id = target_user_id AND role_id = role_id;
    
    -- Refresh user permissions
    PERFORM public.refresh_user_permissions(target_user_id);
    
    RETURN TRUE;
END;
$$;

-- Step 3: Grant proper permissions
GRANT EXECUTE ON FUNCTION public.get_user_roles(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.has_permission(TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.assign_user_role(UUID, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.remove_user_role(UUID, TEXT) TO authenticated;

-- Step 4: Refresh permissions for existing admin users
DO $$
DECLARE
    admin_user RECORD;
BEGIN
    FOR admin_user IN 
        SELECT DISTINCT ur.user_id
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE r.name = 'iharc_admin'
    LOOP
        PERFORM public.refresh_user_permissions(admin_user.user_id);
    END LOOP;
END $$;

-- Step 5: Verify the migration worked
SELECT 'Migration completed successfully. Core schema now has:' as status;
SELECT 'Roles: ' || count(*) as roles_count FROM core.roles;
SELECT 'Permissions: ' || count(*) as permissions_count FROM core.permissions;
SELECT 'Role Permissions: ' || count(*) as role_permissions_count FROM core.role_permissions;
SELECT 'User Roles: ' || count(*) as user_roles_count FROM core.user_roles;
