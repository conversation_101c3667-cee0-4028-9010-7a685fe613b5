-- Update People Table - Remove full_name and add homeless/addiction service fields
-- This migration removes the full_name field and adds comprehensive fields for homeless and addiction services

-- Drop the full_name column as it's redundant with first_name + last_name
ALTER TABLE core.people DROP COLUMN IF EXISTS full_name;

-- Add new fields for homeless and addiction services
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS preferred_pronouns TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS mental_health_concerns TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS medications TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS income_source TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS has_id_documents BOOLEAN;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS primary_language TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS disabilities TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS veteran_status BOOLEAN;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS chronic_conditions TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS case_manager TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS service_barriers TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS support_network TEXT;
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS risk_level TEXT CHECK (risk_level IN ('Low', 'Medium', 'High'));
ALTER TABLE core.people ADD COLUMN IF NOT EXISTS last_service_date DATE;

-- Create function to calculate age from date of birth
CREATE OR REPLACE FUNCTION calculate_age(birth_date DATE)
RETURNS INTEGER AS $$
BEGIN
    IF birth_date IS NULL THEN
        RETURN NULL;
    END IF;
    
    RETURN EXTRACT(YEAR FROM AGE(birth_date));
END;
$$ LANGUAGE plpgsql;

-- Create function to update age automatically
CREATE OR REPLACE FUNCTION update_person_age()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate age from date_of_birth if it exists
    IF NEW.date_of_birth IS NOT NULL THEN
        NEW.age := calculate_age(NEW.date_of_birth);
    ELSE
        NEW.age := NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update age on insert/update
DROP TRIGGER IF EXISTS trigger_update_person_age ON core.people;
CREATE TRIGGER trigger_update_person_age
    BEFORE INSERT OR UPDATE ON core.people
    FOR EACH ROW
    EXECUTE FUNCTION update_person_age();

-- Update existing records to calculate age where date_of_birth exists
UPDATE core.people 
SET age = calculate_age(date_of_birth) 
WHERE date_of_birth IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN core.people.preferred_pronouns IS 'Preferred pronouns for respectful communication';
COMMENT ON COLUMN core.people.mental_health_concerns IS 'Brief notes about mental health status or concerns';
COMMENT ON COLUMN core.people.medications IS 'Current medications being taken';
COMMENT ON COLUMN core.people.income_source IS 'Primary source of income (employment, benefits, etc.)';
COMMENT ON COLUMN core.people.has_id_documents IS 'Whether the person has valid identification documents';
COMMENT ON COLUMN core.people.primary_language IS 'Primary language for communication';
COMMENT ON COLUMN core.people.disabilities IS 'Physical or cognitive disabilities';
COMMENT ON COLUMN core.people.veteran_status IS 'Whether the person is a military veteran';
COMMENT ON COLUMN core.people.chronic_conditions IS 'Ongoing medical conditions';
COMMENT ON COLUMN core.people.case_manager IS 'Assigned case manager or social worker';
COMMENT ON COLUMN core.people.service_barriers IS 'Barriers to accessing services';
COMMENT ON COLUMN core.people.support_network IS 'Family, friends, or other support people';
COMMENT ON COLUMN core.people.risk_level IS 'Risk assessment level (Low, Medium, High)';
COMMENT ON COLUMN core.people.last_service_date IS 'Date when services were last provided';
