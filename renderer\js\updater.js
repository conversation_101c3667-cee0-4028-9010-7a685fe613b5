// Update UI Manager for S.T.E.V.I Retro Renderer Process
export class UpdateUIManager {
    constructor() {
        this.updateAvailable = false;
        this.updateInfo = null;
        this.downloadInProgress = false;
        this.setupEventListeners();
    }

    /**
     * Set up event listeners for update progress
     */
    setupEventListeners() {
        // Listen for update progress from main process
        if (window.electronAPI) {
            window.electronAPI.onUpdateProgress((progress) => {
                this.updateDownloadProgress(progress);
            });
        }
    }

    /**
     * Check for updates and show notification if available
     */
    async checkForUpdates(showNoUpdateMessage = false) {
        try {
            console.log('Checking for updates...');

            // Check if updates are allowed
            const updatesAllowed = await window.electronAPI.invoke('are-updates-allowed');
            const isDevelopment = await window.electronAPI.invoke('is-development-environment');

            if (!updatesAllowed) {
                if (showNoUpdateMessage) {
                    this.showDevelopmentModeDialog(isDevelopment);
                }
                return { available: false, blocked: true, reason: 'Development environment' };
            }

            const updateInfo = await window.electronAPI.invoke('check-for-updates');

            if (updateInfo.available) {
                this.updateAvailable = true;
                this.updateInfo = updateInfo;
                this.showUpdateNotification(updateInfo);
            } else if (showNoUpdateMessage) {
                this.showNoUpdateDialog();
            }

            return updateInfo;
        } catch (error) {
            console.error('Error checking for updates:', error);

            // Check if it's a development environment error
            if (error.message.includes('development environment')) {
                if (showNoUpdateMessage) {
                    this.showDevelopmentModeDialog(true);
                }
                return { available: false, blocked: true, reason: 'Development environment' };
            }

            this.showUpdateError('Failed to check for updates', error.message);
            throw error;
        }
    }

    /**
     * Show update notification dialog
     */
    showUpdateNotification(updateInfo) {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay update-notification';
        
        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>🚀 Update Available</h3>
                </div>
                <div class="modal-body">
                    <p><strong>A new version of S.T.E.V.I Retro is available!</strong></p>
                    <div class="update-details">
                        <p><strong>Current Version:</strong> ${updateInfo.currentVersion}</p>
                        <p><strong>Latest Version:</strong> ${updateInfo.latestVersion}</p>
                        <p><strong>Released:</strong> ${new Date(updateInfo.publishedAt).toLocaleDateString()}</p>
                    </div>
                    <div class="release-notes">
                        <h4>Release Notes:</h4>
                        <div class="release-notes-content">${this.formatReleaseNotes(updateInfo.releaseNotes)}</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="primary-button" id="download-update-btn">Download & Install</button>
                    <button class="secondary-button" id="view-releases-btn">View Releases</button>
                    <button class="secondary-button" id="remind-later-btn">Remind Later</button>
                    <button class="secondary-button" id="skip-update-btn">Skip This Version</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // Set up button handlers
        dialog.querySelector('#download-update-btn').onclick = () => {
            dialog.remove();
            this.startUpdateDownload(updateInfo);
        };
        
        dialog.querySelector('#view-releases-btn').onclick = () => {
            window.electronAPI.invoke('open-releases-page');
        };
        
        dialog.querySelector('#remind-later-btn').onclick = () => {
            dialog.remove();
        };
        
        dialog.querySelector('#skip-update-btn').onclick = () => {
            this.skipVersion(updateInfo.latestVersion);
            dialog.remove();
        };
    }

    /**
     * Start the update download process
     */
    async startUpdateDownload(updateInfo) {
        if (this.downloadInProgress) {
            return;
        }

        this.downloadInProgress = true;
        
        try {
            // Show download progress dialog
            this.showDownloadProgress();
            
            // Start download with checksum verification
            const installerPath = await window.electronAPI.invoke('download-update', updateInfo.downloadUrl, updateInfo.checksum);
            
            // Download completed, show install confirmation
            this.showInstallConfirmation(installerPath);
            
        } catch (error) {
            console.error('Error downloading update:', error);
            this.showUpdateError('Download Failed', error.message);
        } finally {
            this.downloadInProgress = false;
            this.hideDownloadProgress();
        }
    }

    /**
     * Show download progress dialog
     */
    showDownloadProgress() {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay download-progress';
        dialog.id = 'download-progress-dialog';
        
        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>📥 Downloading Update</h3>
                </div>
                <div class="modal-body">
                    <p>Downloading the latest version of S.T.E.V.I Retro...</p>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="download-progress-fill"></div>
                        </div>
                        <div class="progress-text" id="download-progress-text">0%</div>
                    </div>
                    <div class="download-details" id="download-details">
                        <span id="download-size">Preparing download...</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" id="cancel-download-btn">Cancel</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // Set up cancel handler
        dialog.querySelector('#cancel-download-btn').onclick = () => {
            // TODO: Implement download cancellation
            dialog.remove();
            this.downloadInProgress = false;
        };
    }

    /**
     * Update download progress
     */
    updateDownloadProgress(progress) {
        const progressFill = document.getElementById('download-progress-fill');
        const progressText = document.getElementById('download-progress-text');
        const downloadDetails = document.getElementById('download-details');
        
        if (progressFill && progressText) {
            const percentage = Math.round(progress.progress);
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = `${percentage}%`;
            
            if (downloadDetails && progress.totalSize > 0) {
                const downloadedMB = (progress.downloadedSize / 1024 / 1024).toFixed(1);
                const totalMB = (progress.totalSize / 1024 / 1024).toFixed(1);
                downloadDetails.innerHTML = `<span id="download-size">${downloadedMB} MB / ${totalMB} MB</span>`;
            }
        }
    }

    /**
     * Hide download progress dialog
     */
    hideDownloadProgress() {
        const dialog = document.getElementById('download-progress-dialog');
        if (dialog) {
            dialog.remove();
        }
    }

    /**
     * Show install confirmation dialog
     */
    showInstallConfirmation(installerPath) {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay install-confirmation';
        
        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>✅ Download Complete</h3>
                </div>
                <div class="modal-body">
                    <p><strong>Update downloaded successfully!</strong></p>
                    <p>The application will close and the installer will start.</p>
                    <p><em>Note: You may need to manually complete the installation process.</em></p>
                </div>
                <div class="modal-footer">
                    <button class="primary-button" id="install-now-btn">Install Now</button>
                    <button class="secondary-button" id="install-later-btn">Install Later</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // Set up button handlers
        dialog.querySelector('#install-now-btn').onclick = async () => {
            dialog.remove();
            try {
                await window.electronAPI.invoke('install-update', installerPath);
            } catch (error) {
                this.showUpdateError('Installation Failed', error.message);
            }
        };
        
        dialog.querySelector('#install-later-btn').onclick = () => {
            dialog.remove();
        };
    }

    /**
     * Show no update available dialog
     */
    showNoUpdateDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay no-update';
        
        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>✅ Up to Date</h3>
                </div>
                <div class="modal-body">
                    <p>You're running the latest version of S.T.E.V.I Retro!</p>
                </div>
                <div class="modal-footer">
                    <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">OK</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (dialog.parentNode) {
                dialog.remove();
            }
        }, 3000);
    }

    /**
     * Show update error dialog
     */
    showUpdateError(title, message) {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay update-error';
        
        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>❌ ${title}</h3>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                    <p><em>Please try again later or check your internet connection.</em></p>
                </div>
                <div class="modal-footer">
                    <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">OK</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
    }

    /**
     * Format release notes for display
     */
    formatReleaseNotes(notes) {
        if (!notes) return 'No release notes available.';
        
        // Convert markdown-style formatting to HTML
        return notes
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>');
    }

    /**
     * Skip a specific version
     */
    skipVersion(version) {
        localStorage.setItem('skipped-version', version);
        console.log(`Skipped version: ${version}`);
    }

    /**
     * Check if a version should be skipped
     */
    isVersionSkipped(version) {
        return localStorage.getItem('skipped-version') === version;
    }

    /**
     * Clear skipped version
     */
    clearSkippedVersion() {
        localStorage.removeItem('skipped-version');
    }

    /**
     * Show development mode dialog
     */
    showDevelopmentModeDialog(isDevelopment) {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay development-mode';

        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>🛠️ Development Environment</h3>
                </div>
                <div class="modal-body">
                    <p><strong>Updates are disabled in development mode</strong></p>
                    <p>This protects your local development changes from being overwritten.</p>
                    <div class="dev-info">
                        <p><strong>Development indicators detected:</strong></p>
                        <ul>
                            <li>Running from source code</li>
                            <li>Package.json and node_modules present</li>
                            <li>Development environment flags</li>
                        </ul>
                    </div>
                    <p><em>To enable updates, build and install the production version.</em></p>
                </div>
                <div class="modal-footer">
                    <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">OK</button>
                    <button class="secondary-button" id="force-enable-btn">Force Enable (Advanced)</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Set up force enable button (for advanced users)
        dialog.querySelector('#force-enable-btn').onclick = () => {
            this.showForceEnableDialog();
            dialog.remove();
        };

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (dialog.parentNode) {
                dialog.remove();
            }
        }, 10000);
    }

    /**
     * Show force enable updates dialog
     */
    showForceEnableDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay force-enable';

        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>⚠️ Force Enable Updates</h3>
                </div>
                <div class="modal-body">
                    <p><strong>WARNING: This will enable updates in development mode!</strong></p>
                    <p>This could overwrite your local changes and development environment.</p>
                    <p>Only proceed if you understand the risks and have backed up your work.</p>
                    <div class="warning-box">
                        <p>🚨 <strong>Risk:</strong> You may lose uncommitted changes</p>
                        <p>💾 <strong>Recommendation:</strong> Commit and push your changes first</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="danger-button" id="confirm-force-btn">I Understand - Force Enable</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Set up confirm button
        dialog.querySelector('#confirm-force-btn').onclick = async () => {
            try {
                await window.electronAPI.invoke('set-development-mode', false);
                dialog.remove();
                this.showDialog('Updates Enabled', 'Development mode has been disabled. Updates are now enabled.', 'success');
            } catch (error) {
                console.error('Error enabling updates:', error);
                this.showUpdateError('Failed to Enable Updates', error.message);
            }
        };
    }

    /**
     * Show a generic dialog
     */
    showDialog(title, message, type = 'info') {
        const dialog = document.createElement('div');
        dialog.className = `modal-overlay ${type}-dialog`;

        const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';

        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>${icon} ${title}</h3>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">OK</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (dialog.parentNode) {
                dialog.remove();
            }
        }, 5000);
    }
}
