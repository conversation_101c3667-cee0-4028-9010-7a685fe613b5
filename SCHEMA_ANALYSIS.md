# Schema Mismatch Analysis - S.T.E.V.I Retro

## Overview
This document analyzes the mismatches between the current Supabase database schema and the application's expectations based on the fallback schemas defined in `renderer/js/schema.js`.

## Critical Issues Identified

### 1. ID Type Inconsistencies

**Problem**: The application expects text-based IDs but the database uses different types:

| Table | Current DB Type | Expected Type | Impact |
|-------|----------------|---------------|---------|
| core.people | bigint | text | High - Core entity |
| core.pets | bigint | text | High - Foreign key issues |
| core.addresses | bigint | text | High - Core entity |
| case_mgmt.incidents | bigint | text | High - Core entity |
| core.bikes | uuid | text | Medium - Existing system |

**Root Cause**: The application has two ID generation strategies:
- Online mode: Relies on Supabase BIGSERIAL auto-increment
- Offline mode: Generates text IDs using `Date.now().toString(36) + Math.random().toString(36).substr(2)`

### 2. Column Name Issues

#### core.people Table
**Duplicate Columns**:
- "First Name" AND "first_name" 
- "Last Name" AND "last_name"

**Problematic Column Names** (contain spaces):
- "First Name"
- "Last Name" 
- "Active Homelessness"
- "Active Addictions?"
- "Full Name"

**Missing Expected Columns**:
- updated_at (exists but may not be properly configured)
- created_by
- updated_by

### 3. Foreign Key Mismatches

#### core.pets Table
- `person_id` is bigint but should reference text IDs from people table
- This breaks referential integrity when app generates text IDs

### 4. Missing Columns in case_mgmt.incidents

**Current columns**: id, created_at, reporter_id, location, narrative, tags, is_urgent, updated_at, incident_number

**Missing expected columns**:
- incident_type
- status
- priority
- assigned_to
- resolved_at
- resolution_notes
- created_by
- updated_by

### 5. Schema Mapping Issues

The application's `getSchemaForTable()` function maps tables to schemas, but some tables may not exist in expected schemas:

**Expected Schema Mappings**:
- Core: people, pets, medical_issues, addresses, organizations, bikes, etc.
- Case Management: incidents, incident_links, property_records, etc.
- Audit: activity_logs, found_bike_reports, recovery_logs

## Recommended Fix Strategy

### Phase 1: Standardize ID Types
1. Decide on consistent ID strategy (recommend UUID for new records, maintain existing for compatibility)
2. Update application to handle both bigint and text IDs during transition
3. Add proper ID generation logic

### Phase 2: Fix Column Issues
1. Remove duplicate columns in people table
2. Rename columns with spaces to use underscores
3. Add missing columns with proper defaults

### Phase 3: Fix Foreign Keys
1. Update foreign key columns to match parent table ID types
2. Add proper constraints and indexes

### Phase 4: Application Updates
1. Update schema.js fallback schemas to match actual database
2. Modify data.js to handle ID type variations
3. Update form generation to use correct column names

## Impact Assessment

**High Priority** (Breaks core functionality):
- ID type mismatches preventing proper CRUD operations
- Duplicate/invalid column names causing query failures
- Foreign key mismatches breaking relationships

**Medium Priority** (Affects user experience):
- Missing columns causing form/display issues
- Schema mapping inconsistencies

**Low Priority** (Future improvements):
- RLS policy updates
- Performance optimizations
