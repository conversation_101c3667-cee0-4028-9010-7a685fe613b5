# Admin RLS Policy Updates for S.T.E.V.I Retro

This document contains the SQL statements needed to update Supabase Row Level Security (RLS) policies to allow the `iharc_admin` role full access to all application data.

## Overview

The current RLS policies only allow access to users with the `iharc_staff` role. These policies need to be updated to also allow users with the `iharc_admin` role to have full access to all tables.

## Required Policy Updates

### 1. People Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."people" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."people" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."people" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."people" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

### 2. Items Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."items" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."items" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."items" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."items" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

### 3. Supply Provisions Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."supply_provisions" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."supply_provisions" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."supply_provisions" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."supply_provisions" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

### 4. Incidents Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."incidents" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."incidents" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."incidents" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."incidents" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

### 5. People Activities Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."people_activities" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."people_activities" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."people_activities" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."people_activities" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

### 6. Addresses Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."addresses" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."addresses" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."addresses" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."addresses" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

### 7. License Plates Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."license_plates" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."license_plates" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."license_plates" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."license_plates" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

### 8. Bikes Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."bikes" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."bikes" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."bikes" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."bikes" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

### 9. Pets Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."pets" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."pets" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."pets" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."pets" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

### 10. Medical Issues Table

```sql
-- Update existing policies to include admin role
ALTER POLICY "Enable read access for iharc_staff" ON "public"."medical_issues" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable insert for iharc_staff" ON "public"."medical_issues" 
TO authenticated 
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable update for iharc_staff" ON "public"."medical_issues" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

ALTER POLICY "Enable delete for iharc_staff" ON "public"."medical_issues" 
TO authenticated 
USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
```

## Implementation Instructions

1. **Access Supabase Dashboard**: Log into your Supabase project dashboard
2. **Navigate to SQL Editor**: Go to the SQL Editor section
3. **Execute Statements**: Run each of the above SQL statements for all relevant tables
4. **Verify Changes**: Check that policies have been updated correctly in the Authentication > Policies section

## Important Notes

- **Backup First**: Always backup your database before making policy changes
- **Test Thoroughly**: Test admin functionality after implementing these changes
- **Security**: Admin users will have full access to all data - ensure admin role is only assigned to trusted users
- **Policy Names**: The exact policy names may vary - adjust the statements to match your actual policy names
- **Additional Tables**: If you have additional tables not listed here, apply similar policy updates to them

## Verification

After implementing these changes, verify that:
1. Admin users can access the admin tab in the application
2. Admin users can create, read, update, and delete items
3. Staff users still have their existing access levels
4. No unauthorized access is possible

## Troubleshooting

If you encounter issues:
1. Check the Supabase logs for policy violations
2. Verify that user roles are correctly set in the auth metadata
3. Ensure all policy names match your actual database policies
4. Test with both admin and staff user accounts
