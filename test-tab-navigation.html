<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Navigation Test - 1024x768</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Courier New', monospace;
            background: #000000;
            color: #ff0000;
        }

        /* Tab Bar */
        .tab-bar {
            display: flex;
            background: #000000;
            border-bottom: 1px solid #ff0000;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            scrollbar-width: thin;
            scrollbar-color: #ff0000 #000000;
        }

        .tab-bar::-webkit-scrollbar {
            height: 4px;
        }

        .tab-bar::-webkit-scrollbar-track {
            background: #000000;
        }

        .tab-bar::-webkit-scrollbar-thumb {
            background: #ff0000;
            border-radius: 2px;
        }

        .tab-bar::-webkit-scrollbar-thumb:hover {
            background: #ff4444;
        }

        .tab {
            padding: 12px 24px;
            border-right: 1px solid #ff0000;
            cursor: pointer;
            color: #ff4444;
            font-weight: bold;
            transition: all 0.2s ease;
            flex-shrink: 0;
            min-width: fit-content;
        }

        .tab:hover {
            background: #330000;
            color: #ff0000;
        }

        .tab.active {
            background: #ff0000;
            color: #000000;
        }

        .admin-tab {
            background: #330000;
            color: #ffaa00;
            font-weight: bold;
        }

        .admin-tab:hover {
            background: #440000;
            color: #ffcc00;
        }

        .admin-tab.active {
            background: #ffaa00;
            color: #000000;
        }

        .logout-tab {
            margin-left: auto;
            border-left: 1px solid #ff0000;
            border-right: none;
        }

        .content {
            padding: 20px;
            height: calc(100vh - 60px);
            overflow-y: auto;
        }

        .test-info {
            background: #111111;
            border: 1px solid #ff0000;
            padding: 15px;
            margin-bottom: 20px;
        }

        /* Responsive adjustments for testing */
        @media (max-width: 1024px) {
            .tab {
                padding: 8px 10px;
                font-size: 11px;
                flex-shrink: 0;
                min-width: fit-content;
            }

            .tab-bar {
                padding-right: 10px;
            }
        }

        /* Specific optimizations for 1024x768 */
        @media (width: 1024px) and (height: 768px) {
            .tab {
                padding: 6px 8px;
                font-size: 10px;
                flex-shrink: 0;
                min-width: fit-content;
            }

            .tab-bar {
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="tab-bar" id="tab-bar">
        <div class="tab active" data-tab="dashboard">DASHBOARD</div>
        <div class="tab" data-tab="incidents">INCIDENTS</div>
        <div class="tab" data-tab="records">RECORDS</div>
        <div class="tab" data-tab="property">PROPERTY</div>
        <div class="tab" data-tab="encampments">ENCAMPMENTS</div>
        <div class="tab" data-tab="reports">REPORTS</div>
        <div class="tab" data-tab="system">SYSTEM</div>
        <div class="tab admin-tab" data-tab="admin">ADMIN</div>
        <div class="tab logout-tab" data-tab="logout">LOGOUT</div>
    </div>

    <div class="content">
        <div class="test-info">
            <h2>Tab Navigation Test - 1024x768 Resolution</h2>
            <p><strong>Instructions:</strong></p>
            <ol>
                <li>Resize your browser window to 1024x768 pixels</li>
                <li>Check if all tabs are visible and accessible</li>
                <li>If tabs overflow, you should be able to scroll horizontally</li>
                <li>Test clicking on each tab to ensure they work</li>
            </ol>
            <p><strong>Current window size:</strong> <span id="window-size">Loading...</span></p>
            <p><strong>Tab bar scroll width:</strong> <span id="scroll-width">Loading...</span></p>
            <p><strong>Tab bar client width:</strong> <span id="client-width">Loading...</span></p>
        </div>

        <div id="tab-content">
            <h3>Dashboard Content</h3>
            <p>This is the dashboard tab content. Click other tabs to test navigation.</p>
        </div>
    </div>

    <script>
        // Update window size info
        function updateSizeInfo() {
            document.getElementById('window-size').textContent = 
                `${window.innerWidth} x ${window.innerHeight}`;
            
            const tabBar = document.getElementById('tab-bar');
            document.getElementById('scroll-width').textContent = tabBar.scrollWidth + 'px';
            document.getElementById('client-width').textContent = tabBar.clientWidth + 'px';
        }

        // Tab navigation
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked tab
                tab.classList.add('active');
                
                // Update content
                const tabName = tab.dataset.tab;
                document.getElementById('tab-content').innerHTML = `
                    <h3>${tabName.charAt(0).toUpperCase() + tabName.slice(1)} Content</h3>
                    <p>This is the ${tabName} tab content.</p>
                `;
            });
        });

        // Update size info on load and resize
        updateSizeInfo();
        window.addEventListener('resize', updateSizeInfo);
        
        // Update every second to catch any changes
        setInterval(updateSizeInfo, 1000);
    </script>
</body>
</html>
