import { app, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync, existsSync, mkdirSync, writeFileSync } from 'fs';
import { UpdateManager } from './updater.js';
import { SecureSettingsManager } from './settings.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Keep a global reference of the window object
let mainWindow;

// Initialize update manager and settings
let updateManager;
let settingsManager;

// Enable live reload for development
const isDev = process.argv.includes('--dev');

// User data directories
let userDataPaths = {};

// Azure configuration setup
async function setupAzureConfigIfNeeded() {
  try {
    if (!settingsManager.isAzureConfigured()) {
      console.log('Setting up Azure configuration...');

      // Azure configuration from setup
      const azureConfig = {
        tenantId: '760c5d63-f924-4812-8337-05f4efae48ce',
        clientId: '212c48b0-40d2-448e-8fea-e65c4dd55a32',
        clientSecret: '****************************************',
        storageAccountName: 'stevistorage',
        containerName: 'incident-attachments',
        blobEndpoint: 'https://stevistorage.blob.core.windows.net/'
      };

      await settingsManager.setAzureConfig(azureConfig);
      console.log('✅ Azure configuration set up successfully');
    } else {
      console.log('Azure configuration already exists');
    }
  } catch (error) {
    console.error('Error setting up Azure configuration:', error);
  }
}

// Initialize user data directories
function initializeUserDataDirectories() {
  const userDataDir = app.getPath('userData');

  userDataPaths = {
    userData: userDataDir,
    data: join(userDataDir, 'data'),
    cache: join(userDataDir, 'cache'),
    reports: join(userDataDir, 'reports'),
    templates: join(userDataDir, 'templates'),
    media: join(userDataDir, 'media'),
    config: join(userDataDir, 'config.json')
  };

  // Create directories if they don't exist
  Object.values(userDataPaths).forEach(path => {
    if (path.endsWith('.json')) return; // Skip config file
    if (!existsSync(path)) {
      mkdirSync(path, { recursive: true });
      console.log(`Created directory: ${path}`);
    }
  });

  // Create default config file if it doesn't exist
  if (!existsSync(userDataPaths.config)) {
    const defaultConfig = {
      dataPath: userDataPaths.data,
      cachePath: userDataPaths.cache,
      reportsPath: userDataPaths.reports,
      templatesPath: userDataPaths.templates,
      mediaPath: userDataPaths.media,
      version: app.getVersion(),
      firstRun: true,
      created: new Date().toISOString()
    };

    writeFileSync(userDataPaths.config, JSON.stringify(defaultConfig, null, 2));
    console.log(`Created config file: ${userDataPaths.config}`);
  }

  console.log('User data directories initialized:', userDataPaths);
}

function createWindow() {
  // Create the browser window with retro terminal styling
  mainWindow = new BrowserWindow({
    width: 1024,
    height: 768,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false // Allow loading external resources like Supabase
    },
    // Retro terminal window styling
    backgroundColor: '#000000',
    titleBarStyle: 'default',
    icon: join(__dirname, '../assets/icon.png'),
    show: false, // Don't show until ready
    frame: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true
  });

  // Load the renderer
  const rendererPath = join(__dirname, '../renderer/index.html');
  console.log('🔧 Electron Debug Info:');
  console.log('  __dirname:', __dirname);
  console.log('  process.cwd():', process.cwd());
  console.log('  rendererPath:', rendererPath);

  mainWindow.loadFile(rendererPath);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.maximize();
    mainWindow.show();

    // Focus the window
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Set window title
  mainWindow.setTitle('S.T.E.V.I DOS - IHARC Field Staff Terminal');
}

// App event handlers
app.whenReady().then(async () => {
  // Initialize secure settings manager first
  settingsManager = new SecureSettingsManager();

  // Set up Azure configuration if not already configured
  await setupAzureConfigIfNeeded();

  // Initialize user data directories
  initializeUserDataDirectories();

  createWindow();

  // Initialize update manager
  updateManager = new UpdateManager();

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
  });
});

// IPC handlers for communication with renderer
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-error-dialog', async (event, title, content) => {
  const result = await dialog.showErrorBox(title, content);
  return result;
});

ipcMain.handle('show-message-dialog', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

// Handle app closing
ipcMain.handle('quit-app', () => {
  app.quit();
});

// Azure configuration handler - securely provides Azure credentials
ipcMain.handle('get-azure-config', async () => {
  try {
    // Get Azure configuration from secure settings
    const azureConfig = await settingsManager.getAzureConfig();

    if (!azureConfig) {
      throw new Error('Azure configuration not found');
    }

    return azureConfig;
  } catch (error) {
    console.error('Error getting Azure configuration:', error);
    throw error;
  }
});

// File upload handler - handles Azure blob storage uploads from main process
ipcMain.handle('upload-files-to-azure', async (event, files, incidentId) => {
  try {
    // Import Azure modules in main process
    const { BlobServiceClient } = await import('@azure/storage-blob');
    const { ClientSecretCredential } = await import('@azure/identity');

    // Get Azure configuration
    const azureConfig = await settingsManager.getAzureConfig();
    if (!azureConfig) {
      throw new Error('Azure configuration not found');
    }

    // Create credential and blob service client
    const credential = new ClientSecretCredential(
      azureConfig.tenantId,
      azureConfig.clientId,
      azureConfig.clientSecret
    );

    const blobServiceClient = new BlobServiceClient(
      azureConfig.blobEndpoint,
      credential
    );

    const containerClient = blobServiceClient.getContainerClient(azureConfig.containerName);

    // Upload files
    const uploadResults = {
      successful: [],
      failed: [],
      totalUploaded: 0,
      totalFailed: 0
    };

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      try {
        const fileName = `${incidentId}/${Date.now()}_${file.name}`;
        const blockBlobClient = containerClient.getBlockBlobClient(fileName);

        // Convert file data to buffer
        const buffer = Buffer.from(file.data);

        // Upload with metadata
        const uploadResponse = await blockBlobClient.upload(buffer, buffer.length, {
          blobHTTPHeaders: {
            blobContentType: file.type
          },
          metadata: {
            incidentId: incidentId.toString(),
            originalName: file.name,
            uploadedAt: new Date().toISOString()
          }
        });

        uploadResults.successful.push({
          fileName: file.name,
          url: blockBlobClient.url,
          fileSize: file.size,
          contentType: file.type,
          uploadedAt: new Date().toISOString()
        });
        uploadResults.totalUploaded++;

        // Send progress update
        event.sender.send('upload-progress', {
          fileIndex: i,
          percentage: Math.round(((i + 1) / files.length) * 100)
        });

      } catch (error) {
        console.error(`Failed to upload file ${file.name}:`, error);
        uploadResults.failed.push({
          fileName: file.name,
          error: error.message
        });
        uploadResults.totalFailed++;
      }
    }

    return uploadResults;

  } catch (error) {
    console.error('Error uploading files to Azure:', error);
    throw error;
  }
});

// Update-related IPC handlers
ipcMain.handle('check-for-updates', async () => {
  try {
    if (!updateManager) {
      throw new Error('Update manager not initialized');
    }
    return await updateManager.checkForUpdates();
  } catch (error) {
    console.error('Error checking for updates:', error);
    throw error;
  }
});

ipcMain.handle('download-update', async (event, downloadUrl, expectedChecksum) => {
  try {
    if (!updateManager) {
      throw new Error('Update manager not initialized');
    }

    return await updateManager.downloadUpdate(downloadUrl, (progress) => {
      // Send progress updates to renderer
      mainWindow?.webContents.send('update-progress', progress);
    }, expectedChecksum);
  } catch (error) {
    console.error('Error downloading update:', error);
    throw error;
  }
});

// Get app version
ipcMain.handle('get-app-version', async () => {
  return app.getVersion();
});

ipcMain.handle('install-update', async (event, installerPath) => {
  try {
    if (!updateManager) {
      throw new Error('Update manager not initialized');
    }

    await updateManager.installUpdate(installerPath);
  } catch (error) {
    console.error('Error installing update:', error);
    throw error;
  }
});

ipcMain.handle('get-update-progress', () => {
  if (!updateManager) {
    return 0;
  }
  return updateManager.getProgress();
});

ipcMain.handle('is-update-in-progress', () => {
  if (!updateManager) {
    return false;
  }
  return updateManager.isUpdateInProgress();
});

ipcMain.handle('open-releases-page', () => {
  if (updateManager) {
    updateManager.openReleasesPage();
  }
});

ipcMain.handle('cleanup-updates', async () => {
  if (updateManager) {
    await updateManager.cleanup();
  }
});

ipcMain.handle('get-update-storage-usage', async () => {
  if (updateManager) {
    return await updateManager.getStorageUsage();
  }
  return { totalFiles: 0, totalSize: 0, formattedSize: '0 B', files: [] };
});

ipcMain.handle('is-development-environment', () => {
  if (!updateManager) {
    return false;
  }
  return updateManager.isDevelopmentEnvironment();
});

ipcMain.handle('are-updates-allowed', () => {
  if (!updateManager) {
    return false;
  }
  return updateManager.areUpdatesAllowed();
});

ipcMain.handle('set-development-mode', (event, isDev) => {
  if (updateManager) {
    updateManager.setDevelopmentMode(isDev);
  }
});

// Settings-related IPC handlers
ipcMain.handle('get-setting', (event, key, defaultValue) => {
  if (!settingsManager) {
    return defaultValue;
  }
  return settingsManager.get(key, defaultValue);
});

ipcMain.handle('set-setting', (event, key, value) => {
  if (settingsManager) {
    settingsManager.set(key, value);
    return true;
  }
  return false;
});

ipcMain.handle('get-all-settings', () => {
  if (!settingsManager) {
    return {};
  }
  return settingsManager.getAll();
});

ipcMain.handle('reset-settings', () => {
  if (settingsManager) {
    settingsManager.reset();
    return true;
  }
  return false;
});

ipcMain.handle('export-settings', () => {
  if (!settingsManager) {
    return null;
  }
  return settingsManager.export();
});

ipcMain.handle('import-settings', (event, jsonData) => {
  if (!settingsManager) {
    return false;
  }
  return settingsManager.import(jsonData);
});

ipcMain.handle('get-app-paths', () => {
  if (!settingsManager) {
    return {};
  }
  return settingsManager.getPaths();
});

// Secure configuration handler - provides app configuration without exposing sensitive data
ipcMain.handle('get-config', () => {
  if (!settingsManager) {
    return null;
  }

  // Return only necessary configuration for renderer process
  return {
    supabase: {
      url: settingsManager.get('supabase.url'),
      anonKey: settingsManager.get('supabase.anonKey')
    },
    app: {
      debug: isDev || settingsManager.get('app.debug', false),
      version: app.getVersion()
    }
  };
});

// Development helpers
if (isDev) {
  // Enable live reload
  try {
    require('electron-reload')(__dirname, {
      electron: require('electron'),
      hardResetMethod: 'exit'
    });
  } catch (e) {
    console.log('electron-reload not available');
  }
}
