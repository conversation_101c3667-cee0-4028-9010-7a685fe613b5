# People Management Display Issue - Debugging

## Issue Description
The People Management section (Records → Manage People) is not displaying people records even though the console shows 2 records exist in the cache.

## Debugging Added

### 1. Enhanced Data Search Debugging (`renderer/js/data.js`)
Added comprehensive logging to the `search()` method to track:
- Which data source is being used (online vs cached)
- How many records are retrieved from each source
- Query filtering process
- Final result count

**Key logs to watch for:**
```
🔍 search() called for table: people, query: {}
🌐 Online status: true, Test mode: false
📡 Getting fresh data via getAll() for people...
📊 getAll() returned X records for people
📋 No query filters, returning all X records
```

### 2. Enhanced People Management Loading (`renderer/js/app.js`)
Added logging to `loadPeopleManagementContent()` to track:
- When the people management content is loaded
- How many people records are found
- Details of each person record

**Key logs to watch for:**
```
🔍 Loading people management content...
📊 Found X people records for display: [names and IDs]
```

### 3. Enhanced People List Refresh Debugging
Added detailed logging to `refreshPeopleList()` to track:
- When refresh is called and current screen state
- Whether refresh is skipped due to screen mismatch
- Data retrieval and DOM element availability
- HTML rendering process

**Key logs to watch for:**
```
🔄 refreshPeopleList called. Current screen: people-management
🔄 Refreshing people list...
📊 Retrieved X people for refresh: [names and IDs]
🎯 DOM elements found: { peopleList: true, peopleCount: true }
📝 Rendered HTML length: X
✅ People list refreshed with X records
```

### 4. Enhanced People List Rendering
Added logging to `renderPeopleList()` to track:
- Input data received for rendering
- Whether no-records message is shown
- Final HTML output details

**Key logs to watch for:**
```
🎨 renderPeopleList called with X people: [person objects]
📝 Rendering people cards...
📝 Final rendered HTML length: X characters
📝 First 200 characters of rendered HTML: [HTML preview]
```

## Diagnostic Process

### Step 1: Check Data Flow
1. Navigate to Records → Manage People
2. Check console for the sequence:
   - `🔍 Loading people management content...`
   - `🔍 search() called for table: people`
   - `📊 Found X people records for display`

### Step 2: Check Rendering
3. Look for rendering logs:
   - `🎨 renderPeopleList called with X people`
   - `📝 Final rendered HTML length: X characters`

### Step 3: Check Real-time Updates
4. If records exist but aren't showing, check:
   - `🔄 refreshPeopleList called. Current screen: people-management`
   - Whether refresh is being skipped

## Potential Issues to Look For

### Issue 1: Data Not Retrieved
If you see:
```
📊 getAll() returned 0 records for people
```
But cache shows 2 records, then `getAll()` isn't returning cached data properly.

### Issue 2: Data Retrieved But Not Rendered
If you see records found but no HTML rendered:
```
📊 Found 2 people records for display: [...]
🎨 renderPeopleList called with 0 people
```
Then there's a data passing issue between loading and rendering.

### Issue 3: HTML Rendered But Not Displayed
If you see:
```
📝 Final rendered HTML length: 1500 characters
🎯 DOM elements found: { peopleList: false, peopleCount: false }
```
Then the DOM elements aren't found, indicating a timing or element ID issue.

### Issue 4: Screen State Mismatch
If you see:
```
🔄 refreshPeopleList called. Current screen: null
⏭️ Skipping people list refresh - not on people-management screen
```
Then real-time updates are being blocked by incorrect screen state.

## Files Modified
- `renderer/js/data.js` - Enhanced search method debugging
- `renderer/js/app.js` - Enhanced people management and rendering debugging

## Next Steps
Deploy these changes and navigate to People Management. The enhanced logging will show exactly where the data flow breaks down, allowing us to identify and fix the specific issue preventing people records from displaying.
