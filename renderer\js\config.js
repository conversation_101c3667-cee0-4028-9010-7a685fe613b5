// Configuration Manager for S.T.E.V.I DOS Electron App
import { VaultManager } from './vault-manager.js';

export class ConfigManager {
    constructor() {
        this.config = {
            supabase: {
                url: 'https://vfavknkfiiclzgpjpntj.supabase.co',
                anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZmYXZrbmtmaWljbHpncGpwbnRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMjQzMzEsImV4cCI6MjA2NjgwMDMzMX0.ywwytraoAsGCEWcxj3U8MHs_E1xpbeKP9LDSYsEziJU'
            },
            google: {
                // API key now stored securely in Supabase Vault
                apiKey: null // No fallback - must use vault
            },
            cache: {
                ttl: 3600, // 1 hour
                maxSize: 1000
            },
            app: {
                name: 'S.T.E.V.I DOS',
                version: '1.0.0',
                debug: process.env.NODE_ENV === 'development'
            },
            paths: {
                data: this.getDataPath(),
                templates: this.getTemplatesPath(),
                reports: this.getReportsPath(),
                media: this.getMediaPath()
            }
        };

        // VaultManager will be initialized when Supabase client is available
        this.vaultManager = null;
        this.supabaseClient = null;

        this.loadUserConfig();
    }

    /**
     * Initialize VaultManager with Supabase client
     * @param {Object} supabaseClient - The Supabase client instance
     */
    initializeVault(supabaseClient) {
        this.supabaseClient = supabaseClient;
        this.vaultManager = new VaultManager(supabaseClient);
        console.log('VaultManager initialized for secure API key management');
    }

    getDataPath() {
        // Fallback for renderer process - use localStorage or default
        return localStorage.getItem('stevidos_data_path') || './data';
    }

    getTemplatesPath() {
        return `${this.getDataPath()}/templates`;
    }

    getReportsPath() {
        return `${this.getDataPath()}/reports`;
    }

    getMediaPath() {
        return `${this.getDataPath()}/media`;
    }

    loadUserConfig() {
        try {
            const userConfig = localStorage.getItem('stevidos_config');
            if (userConfig) {
                const parsed = JSON.parse(userConfig);
                this.config = { ...this.config, ...parsed };
            }
        } catch (error) {
            console.warn('Could not load user config:', error);
        }
    }

    saveUserConfig() {
        try {
            localStorage.setItem('stevidos_config', JSON.stringify(this.config));
        } catch (error) {
            console.warn('Could not save user config:', error);
        }
    }

    get(key) {
        const keys = key.split('.');
        let value = this.config;

        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return undefined;
            }
        }

        return value;
    }

    set(key, value) {
        const keys = key.split('.');
        let target = this.config;

        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!(k in target) || typeof target[k] !== 'object') {
                target[k] = {};
            }
            target = target[k];
        }

        target[keys[keys.length - 1]] = value;
        this.saveUserConfig();
    }

    getSupabaseUrl() {
        return this.get('supabase.url');
    }

    getSupabaseAnonKey() {
        return this.get('supabase.anonKey');
    }

    /**
     * Get Google API key from Vault
     * @returns {Promise<string|null>} The API key or null if not found
     */
    async getGoogleApiKey() {
        // Ensure VaultManager is initialized
        if (!this.vaultManager) {
            console.warn('VaultManager not initialized. Cannot retrieve Google API key.');
            return null;
        }

        try {
            console.log('Retrieving Google API key from Vault...');
            const apiKey = await this.vaultManager.getSecret('google_api_key');
            
            if (apiKey) {
                console.log('Successfully retrieved Google API key from Vault');
                return apiKey;
            } else {
                console.warn('Google API key not found in Vault');
                return null;
            }
        } catch (error) {
            console.error('Error retrieving Google API key from Vault:', error);
            return null;
        }
    }

    /**
     * Get Google API key synchronously (fallback only)
     * @returns {string} The fallback Google API key
     */
    getGoogleApiKeySync() {
        return this.get('google.apiKey');
    }

    getCacheTTL() {
        return this.get('cache.ttl');
    }

    isDebugMode() {
        return this.get('app.debug');
    }

    getAppVersion() {
        return this.get('app.version');
    }

    // Environment-specific settings
    isDevelopment() {
        return process.env.NODE_ENV === 'development';
    }

    isProduction() {
        return process.env.NODE_ENV === 'production';
    }

    // Test mode for development
    isTestMode() {
        // FORCE PRODUCTION MODE - disable test mode completely
        console.log('Test mode check: FORCED TO FALSE (production mode)');
        return false;
    }
}