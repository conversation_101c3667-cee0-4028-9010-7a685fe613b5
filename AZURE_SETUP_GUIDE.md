# Azure Blob Storage Setup for S.T.E.V.I Retro Updates

## Current Configuration

Your Azure Blob Storage is already set up with:
- **Storage Account**: `iharcpublicappblob`
- **Container**: `stevi`
- **Path**: `stevi retro`
- **Public Access**: ✅ Anonymous read access enabled

## Required: GitHub Actions Write Access

For the GitHub Actions workflow to upload files, you need to provide write access credentials.

### Option 1: Connection String (Recommended)

**Step 1: Get Connection String from Azure Portal**
1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Storage accounts** → **iharcpublicappblob**
3. In the left sidebar, click **Access keys**
4. Under **key1**, click **Show** next to "Connection string"
5. Copy the entire connection string (starts with `DefaultEndpointsProtocol=https;AccountName=iharcpublicappblob;AccountKey=...`)

**Step 2: Add to GitHub Repository Secrets**
1. Go to your GitHub repository: `https://github.com/iharc-jordan/stevi_dos`
2. Click **Settings** (top menu)
3. In left sidebar: **Secrets and variables** → **Actions**
4. Click **New repository secret**
5. **Name**: `AZURE_STORAGE_CONNECTION_STRING`
6. **Value**: Paste the connection string
7. Click **Add secret**

### Option 2: Service Principal (More Secure)

If you prefer more granular permissions:

**Step 1: Create Service Principal**
```bash
# Install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli
# Login to Azure
az login

# Create service principal with Storage Blob Data Contributor role
az ad sp create-for-rbac \
  --name "github-actions-stevi-retro" \
  --role "Storage Blob Data Contributor" \
  --scopes "/subscriptions/YOUR_SUBSCRIPTION_ID/resourceGroups/YOUR_RESOURCE_GROUP/providers/Microsoft.Storage/storageAccounts/iharcpublicappblob"
```

**Step 2: Add Service Principal Secrets**
Add these secrets to your GitHub repository:
- `AZURE_CLIENT_ID`: From the service principal output
- `AZURE_CLIENT_SECRET`: From the service principal output  
- `AZURE_TENANT_ID`: From the service principal output
- `AZURE_SUBSCRIPTION_ID`: Your Azure subscription ID

## Verification

### Test Current Access
Run this to verify read access is working:
```bash
curl "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/"
```

### Test After Setup
After adding the GitHub secret, trigger the workflow:

**Option A: Create a Tag (Automatic)**
```bash
# First update package.json version to 1.0.0
git tag stable
git push stevi_dos stable --force
```

**Option B: Manual Trigger**
1. Go to GitHub → Actions → "Build and Deploy S.T.E.V.I Retro"
2. Click "Run workflow"
3. Enter version "1.0.0"
4. Click "Run workflow"

### Verify Deployment
After the workflow completes, test the endpoints:
```bash
# Test from your local machine
node test-azure-endpoints.js

# Or test manually
curl "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/latest-version.txt"
curl "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/latest-metadata.json"
```

## Security Notes

### What's Secure ✅
- **Read Access**: Public, no credentials needed by end users
- **Write Access**: Only GitHub Actions has write permissions
- **Scoped Permissions**: Connection string/service principal only has access to this storage account
- **No Client Exposure**: Write credentials never reach end-user devices

### Best Practices ✅
- **Principle of Least Privilege**: Only necessary permissions granted
- **Separation of Concerns**: Read (public) vs Write (authenticated) access
- **Secure Storage**: Credentials stored in GitHub Secrets (encrypted)
- **Audit Trail**: All uploads logged in GitHub Actions

## Troubleshooting

### Common Issues

**"Authentication failed" in GitHub Actions**
- Verify the connection string is correct
- Check that the storage account name matches
- Ensure the secret name is exactly `AZURE_STORAGE_CONNECTION_STRING`

**"Container not found"**
- Verify the container `stevi` exists
- Check that the container allows public read access
- Ensure the path `stevi retro` is accessible

**"Permission denied"**
- Verify the connection string has write permissions
- Check that the storage account key is valid
- Ensure the GitHub secret is properly set

### Testing Commands

```bash
# Test container access
curl -I "https://iharcpublicappblob.blob.core.windows.net/stevi/"

# Test specific path
curl -I "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/"

# Test with Azure CLI (if you have it installed)
az storage blob list --container-name stevi --account-name iharcpublicappblob --prefix "stevi retro/"
```

## Next Steps

1. ✅ **Add GitHub Secret**: `AZURE_STORAGE_CONNECTION_STRING`
2. ✅ **Test Workflow**: Create a tag or run manually
3. ✅ **Verify Deployment**: Check that files are accessible
4. ✅ **Test Update System**: Try the update functionality in the app

Once the GitHub secret is added, the automated build and deployment system will be fully functional! 🚀
