-- Fix Schema Permissions and Ensure Functions Work
-- This script fixes permission issues between public functions and core schema tables

-- Step 1: Grant proper permissions to authenticated users on core schema
GRANT USAGE ON SCHEMA core TO authenticated;

-- Step 2: Grant permissions on core tables
GRANT SELECT ON ALL TABLES IN SCHEMA core TO authenticated;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA core TO authenticated;

-- Step 3: Grant permissions on future tables in core schema
ALTER DEFAULT PRIVILEGES IN SCHEMA core GRANT SELECT ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES IN SCHEMA core GRANT INSERT, UPDATE, DELETE ON TABLES TO authenticated;

-- Step 4: Check if the functions exist and recreate them if needed
DO $$
BEGIN
    -- Check if get_user_roles function exists
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_user_roles' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
        RAISE NOTICE 'Creating get_user_roles function...';
        
        CREATE OR REPLACE FUNCTION public.get_user_roles(user_uuid UUID DEFAULT auth.uid())
        RETURNS TABLE(role_name TEXT)
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
            -- Check if the requesting user is the same as the target user or is an admin
            IF auth.uid() = user_uuid OR EXISTS (
                SELECT 1 FROM core.user_roles ur
                JOIN core.roles r ON ur.role_id = r.id
                WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
            ) THEN
                RETURN QUERY
                SELECT r.name
                FROM core.user_roles ur
                JOIN core.roles r ON ur.role_id = r.id
                WHERE ur.user_id = user_uuid;
            ELSE
                -- Return empty result for unauthorized access
                RETURN;
            END IF;
        END;
        $$;
    END IF;
    
    -- Check if get_user_permissions function exists
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_user_permissions' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
        RAISE NOTICE 'Creating get_user_permissions function...';
        
        CREATE OR REPLACE FUNCTION public.get_user_permissions(user_uuid UUID DEFAULT auth.uid())
        RETURNS TABLE(permission_name TEXT)
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
            -- Check if the requesting user is the same as the target user or is an admin
            IF auth.uid() = user_uuid OR EXISTS (
                SELECT 1 FROM core.user_roles ur
                JOIN core.roles r ON ur.role_id = r.id
                WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
            ) THEN
                RETURN QUERY
                SELECT DISTINCT p.name
                FROM core.user_roles ur
                JOIN core.roles r ON ur.role_id = r.id
                JOIN core.role_permissions rp ON r.id = rp.role_id
                JOIN core.permissions p ON rp.permission_id = p.id
                WHERE ur.user_id = user_uuid;
            ELSE
                -- Return empty result for unauthorized access
                RETURN;
            END IF;
        END;
        $$;
    END IF;
    
    -- Check if has_permission function exists
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'has_permission' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
        RAISE NOTICE 'Creating has_permission function...';
        
        CREATE OR REPLACE FUNCTION public.has_permission(permission_name TEXT)
        RETURNS BOOLEAN
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
            RETURN EXISTS (
                SELECT 1
                FROM core.user_roles ur
                JOIN core.roles r ON ur.role_id = r.id
                JOIN core.role_permissions rp ON r.id = rp.role_id
                JOIN core.permissions p ON rp.permission_id = p.id
                WHERE ur.user_id = auth.uid() AND p.name = permission_name
            );
        END;
        $$;
    END IF;
    
    -- Check if refresh_user_permissions function exists
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'refresh_user_permissions' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
        RAISE NOTICE 'Creating refresh_user_permissions function...';
        
        CREATE OR REPLACE FUNCTION public.refresh_user_permissions(user_uuid UUID DEFAULT auth.uid())
        RETURNS JSON
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
            user_roles TEXT[];
            user_permissions TEXT[];
            result JSON;
        BEGIN
            -- Get user roles
            SELECT ARRAY_AGG(role_name) INTO user_roles
            FROM public.get_user_roles(user_uuid);
            
            -- Get user permissions
            SELECT ARRAY_AGG(permission_name) INTO user_permissions
            FROM public.get_user_permissions(user_uuid);
            
            -- Build result
            result := json_build_object(
                'roles', COALESCE(user_roles, ARRAY[]::TEXT[]),
                'permissions', COALESCE(user_permissions, ARRAY[]::TEXT[])
            );
            
            -- Update user's app_metadata with roles and permissions (not in claims)
            UPDATE auth.users 
            SET raw_app_meta_data = jsonb_set(
                COALESCE(raw_app_meta_data, '{}'::jsonb),
                '{roles,permissions}',
                result::jsonb
            )
            WHERE id = user_uuid;
            
            RETURN result;
        END;
        $$;
    END IF;
    
    -- Check if get_users_with_roles function exists
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_users_with_roles' AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')) THEN
        RAISE NOTICE 'Creating get_users_with_roles function...';
        
        CREATE OR REPLACE FUNCTION public.get_users_with_roles()
        RETURNS TABLE(
            user_id UUID,
            email TEXT,
            full_name TEXT,
            roles TEXT[],
            permissions TEXT[]
        )
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
            -- Check if the current user is an admin
            IF NOT EXISTS (
                SELECT 1 FROM core.user_roles ur
                JOIN core.roles r ON ur.role_id = r.id
                WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
            ) THEN
                RAISE EXCEPTION 'Permission denied: admin access required';
            END IF;
            
            RETURN QUERY
            SELECT 
                u.id as user_id,
                u.email,
                COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name') as full_name,
                ARRAY_AGG(DISTINCT r.name) as roles,
                ARRAY_AGG(DISTINCT p.name) as permissions
            FROM auth.users u
            LEFT JOIN core.user_roles ur ON u.id = ur.user_id
            LEFT JOIN core.roles r ON ur.role_id = r.id
            LEFT JOIN core.role_permissions rp ON r.id = rp.role_id
            LEFT JOIN core.permissions p ON rp.permission_id = p.id
            WHERE u.email LIKE '%@iharc.ca'
            GROUP BY u.id, u.email, u.raw_user_meta_data
            ORDER BY u.email;
        END;
        $$;
    END IF;
    
END $$;

-- Step 5: Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.get_user_roles(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.has_permission(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_users_with_roles() TO authenticated;

-- Step 6: Test the functions
SELECT 'Testing functions...' as info;

-- Test get_user_<NAME_EMAIL>
SELECT 'Testing get_user_roles:' as test;
SELECT public.get_user_roles(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Test get_user_<NAME_EMAIL>
SELECT 'Testing get_user_permissions:' as test;
SELECT public.get_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Test has_<NAME_EMAIL>
SELECT 'Testing has_permission:' as test;
SELECT public.has_permission('admin.access');

-- Step 7: Refresh <NAME_EMAIL>
SELECT 'Refreshing user permissions...' as info;
SELECT public.refresh_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Step 8: Verify the fix
SELECT 'Verification - User metadata after refresh:' as info;
SELECT 
    id,
    email,
    raw_app_meta_data
FROM auth.users 
WHERE email = '<EMAIL>'; 