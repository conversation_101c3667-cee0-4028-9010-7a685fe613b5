-- Fix Admin Check Function
-- This script updates the admin check to also recognize Supabase account owners

-- Step 1: Update the get_users_with_roles function to check for Supabase admin
CREATE OR REPLACE FUNCTION core.get_users_with_roles()
RETURNS TABLE(
    user_id UUID,
    email TEXT,
    full_name TEXT,
    roles TEXT[],
    permissions TEXT[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the current user is an admin (either iharc_admin role or Supabase account owner)
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) AND NOT (
        -- Check if user is the Supabase account owner (has service_role permissions)
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_app_meta_data->>'provider' = 'email'
            AND raw_app_meta_data->>'providers' ? 'email'
        )
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.email,
        COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name') as full_name,
        ARRAY_AGG(DISTINCT r.name) as roles,
        ARRAY_AGG(DISTINCT p.name) as permissions
    FROM auth.users u
    LEFT JOIN core.user_roles ur ON u.id = ur.user_id
    LEFT JOIN core.roles r ON ur.role_id = r.id
    LEFT JOIN core.role_permissions rp ON r.id = rp.role_id
    LEFT JOIN core.permissions p ON rp.permission_id = p.id
    WHERE u.email LIKE '%@iharc.ca'
    GROUP BY u.id, u.email, u.raw_user_meta_data
    ORDER BY u.email;
END;
$$;

-- Step 2: Update the has_permission function to also check for Supabase admin
CREATE OR REPLACE FUNCTION core.has_permission(permission_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission through roles OR is Supabase account owner
    RETURN EXISTS (
        SELECT 1
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        JOIN core.role_permissions rp ON r.id = rp.role_id
        JOIN core.permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = auth.uid() AND p.name = permission_name
    ) OR (
        -- Supabase account owners have all permissions
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_app_meta_data->>'provider' = 'email'
            AND raw_app_meta_data->>'providers' ? 'email'
        )
    );
END;
$$;

-- Step 3: Update the get_user_roles function
CREATE OR REPLACE FUNCTION core.get_user_roles(user_uuid UUID DEFAULT auth.uid())
RETURNS TABLE(role_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) OR (
        -- Supabase account owners can view any user's roles
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_app_meta_data->>'provider' = 'email'
            AND raw_app_meta_data->>'providers' ? 'email'
        )
    ) THEN
        RETURN QUERY
        SELECT r.name
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = user_uuid;
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Step 4: Update the get_user_permissions function
CREATE OR REPLACE FUNCTION core.get_user_permissions(user_uuid UUID DEFAULT auth.uid())
RETURNS TABLE(permission_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) OR (
        -- Supabase account owners can view any user's permissions
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_app_meta_data->>'provider' = 'email'
            AND raw_app_meta_data->>'providers' ? 'email'
        )
    ) THEN
        RETURN QUERY
        SELECT DISTINCT p.name
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        JOIN core.role_permissions rp ON r.id = rp.role_id
        JOIN core.permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = user_uuid;
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Step 5: Test the updated functions
SELECT 'Testing updated functions...' as info;

-- Test has_permission function
SELECT 'Testing has_permission function:' as test;
SELECT 
    'admin.access' as permission,
    core.has_permission('admin.access') as has_permission;

-- Test get_users_with_roles function
SELECT 'Testing get_users_with_roles function:' as test;
SELECT * FROM core.get_users_with_roles();

-- Step 6: Show current user info for debugging
SELECT 'Current user info:' as info;
SELECT 
    id,
    email,
    raw_app_meta_data,
    raw_user_meta_data
FROM auth.users 
WHERE id = auth.uid(); 