#!/usr/bin/env node

// Build script for standalone S.T.E.V.I Retro installer
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building Standalone S.T.E.V.I Retro Installer...\n');

try {
    // Step 1: Clean previous builds
    console.log('1️⃣ Cleaning previous builds...');
    try {
        execSync('npm run clean', { stdio: 'inherit' });
    } catch (error) {
        console.log('   No previous builds to clean');
    }

    // Step 2: Check dependencies
    console.log('\n2️⃣ Checking system dependencies...');
    execSync('npm run check-deps', { stdio: 'inherit' });

    // Step 3: Build the installer
    console.log('\n3️⃣ Building Windows installer...');
    execSync('npx electron-builder --win', { stdio: 'inherit' });

    // Step 4: Verify the installer was created
    console.log('\n4️⃣ Verifying installer...');
    const installerPath = './dist/S.T.E.V.I-Retro-Installer.exe';
    
    if (fs.existsSync(installerPath)) {
        const stats = fs.statSync(installerPath);
        const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
        
        console.log('✅ Standalone installer created successfully!');
        console.log(`📁 Location: ${path.resolve(installerPath)}`);
        console.log(`📊 Size: ${fileSizeMB} MB`);
        console.log(`📅 Created: ${stats.mtime.toLocaleString()}`);
        
        console.log('\n🎯 Installation Instructions:');
        console.log('1. Download S.T.E.V.I-Retro-Installer.exe to any Windows machine');
        console.log('2. Right-click and "Run as administrator" (recommended)');
        console.log('3. Follow the installation wizard');
        console.log('4. The app will be installed with auto-update capability');
        
        console.log('\n✨ Features included:');
        console.log('• Professional Windows installer');
        console.log('• Automatic dependency detection');
        console.log('• Desktop and Start Menu shortcuts');
        console.log('• Secure user data directories');
        console.log('• Self-update capability');
        console.log('• Works on fresh Windows installations');
        
    } else {
        throw new Error('Installer file was not created');
    }

} catch (error) {
    console.error('\n❌ Build failed:', error.message);
    process.exit(1);
}

console.log('\n🎉 Build completed successfully!');
