@echo off
echo ========================================
echo S.T.E.V.I Retro Installer Builder
echo ========================================
echo.

echo Checking prerequisites...
if not exist "package.json" (
    echo ERROR: package.json not found. Please run this script from the project root.
    pause
    exit /b 1
)

echo Building installer...
echo This may take several minutes...
echo.

npm run build-win

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✅ Installer built successfully!
    echo ========================================
    echo.
    echo Installer location: dist\S.T.E.V.I-Retro-Installer.exe
    echo Size: 
    for %%A in ("dist\S.T.E.V.I-Retro-Installer.exe") do echo   %%~zA bytes
    echo.
    echo Standalone app location: dist\win-unpacked\S.T.E.V.I Retro.exe
    echo.
    echo Ready for distribution!
) else (
    echo.
    echo ========================================
    echo ❌ Build failed!
    echo ========================================
    echo.
    echo Please check the error messages above.
)

pause 