#!/usr/bin/env node

// <PERSON>ript to create GitHub release with installer
const fs = require('fs');
const path = require('path');

console.log('🚀 GitHub Release Instructions for S.T.E.V.I Retro v1.0.9\n');

// Check if installer exists
const installerPath = './S.T.E.V.I-Retro-Installer.exe';
const blockMapPath = './S.T.E.V.I-Retro-Installer.exe.blockmap';

if (fs.existsSync(installerPath)) {
    const stats = fs.statSync(installerPath);
    const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
    
    console.log('✅ Installer found and ready for release:');
    console.log(`📁 File: ${path.resolve(installerPath)}`);
    console.log(`📊 Size: ${fileSizeMB} MB`);
    console.log(`📅 Created: ${stats.mtime.toLocaleString()}`);
    
    console.log('\n📋 Manual GitHub Release Steps:');
    console.log('1. Go to: https://github.com/iharc-jordan/stevi_retro/releases');
    console.log('2. Click "Create a new release"');
    console.log('3. Tag: v1.0.9');
    console.log('4. Title: "S.T.E.V.I Retro v1.0.9 - Standalone Windows Installer"');
    console.log('5. Description:');
    
    const releaseNotes = `# S.T.E.V.I Retro v1.0.9 - Standalone Windows Installer

## 🎯 Ready for Production Use!

### Download & Install
1. Download \`S.T.E.V.I-Retro-Installer.exe\` (${fileSizeMB} MB)
2. Right-click and "Run as administrator"
3. Follow the installation wizard
4. Launch from desktop shortcut

### ✅ What's New in v1.0.9
- **Standalone EXE installer** - No external dependencies required
- **Fresh Windows support** - Works on brand new Windows 10/11 installations
- **Professional installation** - NSIS wizard with shortcuts and proper integration
- **Self-update capability** - Automatic updates from Azure Blob Storage
- **Secure settings** - AES-256 encrypted user data with automatic backups

### 🔧 Technical Details
- **Size**: ${fileSizeMB} MB (self-contained)
- **Requirements**: Windows 10/11 (64-bit)
- **Dependencies**: All included (Electron 37.2.1, Node.js 24+)
- **Installation**: Professional NSIS installer
- **Updates**: Automatic via Azure Blob Storage

### 🛡️ Security Features
- AES-256 encryption for all user settings
- Secure user data directories in %APPDATA%
- Automatic backup system with rotation
- User-only file permissions

### 📊 System Requirements
- Windows 10 or Windows 11 (64-bit)
- 100 MB free disk space
- Internet connection (for updates and weather data)

### 🎯 Perfect for:
- Fresh Windows installations
- Enterprise deployment
- End-user distribution
- Client testing

**This is a production-ready installer suitable for distribution to end users.**`;

    console.log('\n' + releaseNotes);
    
    console.log('\n6. Upload files:');
    console.log(`   - ${path.basename(installerPath)} (${fileSizeMB} MB)`);
    if (fs.existsSync(blockMapPath)) {
        const blockMapStats = fs.statSync(blockMapPath);
        const blockMapSizeKB = (blockMapStats.size / 1024).toFixed(1);
        console.log(`   - ${path.basename(blockMapPath)} (${blockMapSizeKB} KB) [Optional - for verification]`);
    }
    console.log('7. Check "Set as the latest release"');
    console.log('8. Click "Publish release"');
    
    console.log('\n🎉 After publishing, users can download from:');
    console.log('https://github.com/iharc-jordan/stevi_retro/releases/latest');
    
} else {
    console.log('❌ Installer not found. Run the build first:');
    console.log('node build-standalone.cjs');
}

console.log('\n💡 Why use GitHub Releases instead of repository?');
console.log('• Large binary files (88MB) are better suited for releases');
console.log('• Keeps repository clean and fast to clone');
console.log('• Provides download statistics');
console.log('• Better for end-user distribution');
console.log('• Follows GitHub best practices');
