// Security Error Handler for S.T.E.V.I DOS Electron App
// Handles authentication failures, security violations, and suspicious activities

export class SecurityErrorHandler {
    constructor() {
        this.errorCounts = new Map();
        this.suspiciousActivities = [];
        this.maxRetries = 3;
        this.lockoutDuration = 300000; // 5 minutes
        this.initialized = false;
    }

    /**
     * Initialize security error handling
     */
    initialize() {
        if (this.initialized) {
            return;
        }

        this.setupGlobalErrorHandlers();
        this.setupSecurityEventListeners();
        this.initialized = true;
        
        console.log('🛡️ Security error handler initialized');
    }

    /**
     * Setup global error handlers
     */
    setupGlobalErrorHandlers() {
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('🚨 Unhandled promise rejection:', event.reason);
            this.handleSecurityError('UNHANDLED_REJECTION', event.reason);
        });

        // Handle global errors
        window.addEventListener('error', (event) => {
            console.error('🚨 Global error:', event.error);
            this.handleSecurityError('GLOBAL_ERROR', event.error);
        });

        // Handle fetch errors (network security)
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                
                // Check for security-related HTTP status codes
                if (response.status === 401) {
                    this.handleAuthenticationError('UNAUTHORIZED', args[0]);
                } else if (response.status === 403) {
                    this.handleAuthenticationError('FORBIDDEN', args[0]);
                } else if (response.status === 429) {
                    this.handleSecurityError('RATE_LIMITED', args[0]);
                }
                
                return response;
            } catch (error) {
                this.handleNetworkError(error, args[0]);
                throw error;
            }
        };
    }

    /**
     * Setup security event listeners
     */
    setupSecurityEventListeners() {
        // Listen for session expiry events
        window.addEventListener('sessionExpired', () => {
            this.handleSessionExpiry();
        });

        // Listen for authentication failures
        window.addEventListener('authenticationFailed', (event) => {
            this.handleAuthenticationError('AUTH_FAILED', event.detail);
        });

        // Listen for CSP violations (handled by SecurityHeadersManager)
        document.addEventListener('securitypolicyviolation', (event) => {
            this.handleCspViolation(event);
        });
    }

    /**
     * Handle authentication errors
     */
    handleAuthenticationError(type, details) {
        console.warn(`🔐 Authentication error: ${type}`, details);

        const errorKey = `auth_${type}`;
        const count = this.incrementErrorCount(errorKey);

        // Log the security event
        this.logSecurityEvent('AUTHENTICATION_ERROR', {
            type,
            details,
            count,
            timestamp: new Date().toISOString()
        });

        // Handle based on error type
        switch (type) {
            case 'UNAUTHORIZED':
                this.handleUnauthorizedAccess(count);
                break;
            case 'FORBIDDEN':
                this.handleForbiddenAccess(count);
                break;
            case 'AUTH_FAILED':
                this.handleAuthenticationFailure(count, details);
                break;
            default:
                this.handleGenericAuthError(type, count);
        }
    }

    /**
     * Handle unauthorized access (401)
     */
    handleUnauthorizedAccess(count) {
        if (count >= this.maxRetries) {
            this.triggerSecurityLockout('UNAUTHORIZED_ACCESS');
        } else {
            // Try to refresh session
            if (window.app && window.app.auth) {
                window.app.auth.refreshSession().catch(() => {
                    // If refresh fails, redirect to login
                    this.redirectToLogin('Session expired');
                });
            }
        }
    }

    /**
     * Handle forbidden access (403)
     */
    handleForbiddenAccess(count) {
        if (count >= this.maxRetries) {
            this.triggerSecurityLockout('FORBIDDEN_ACCESS');
        } else {
            this.showSecurityWarning(
                'Access Denied',
                'You do not have permission to access this resource. Please contact an administrator if you believe this is an error.'
            );
        }
    }

    /**
     * Handle authentication failures
     */
    handleAuthenticationFailure(count, details) {
        if (count >= this.maxRetries) {
            this.triggerSecurityLockout('AUTH_FAILURE');
        } else {
            const remainingAttempts = this.maxRetries - count;
            this.showSecurityWarning(
                'Authentication Failed',
                `Login failed. ${remainingAttempts} attempts remaining before temporary lockout.`
            );
        }
    }

    /**
     * Handle session expiry
     */
    handleSessionExpiry() {
        console.warn('🔐 Session expired');
        
        this.logSecurityEvent('SESSION_EXPIRED', {
            timestamp: new Date().toISOString()
        });

        this.redirectToLogin('Your session has expired. Please log in again.');
    }

    /**
     * Handle CSP violations
     */
    handleCspViolation(event) {
        console.warn('🚨 CSP Violation detected:', event);
        
        this.logSecurityEvent('CSP_VIOLATION', {
            directive: event.violatedDirective,
            blockedURI: event.blockedURI,
            sourceFile: event.sourceFile,
            lineNumber: event.lineNumber,
            timestamp: new Date().toISOString()
        });

        // Check for repeated violations (possible attack)
        const violationKey = `csp_${event.violatedDirective}`;
        const count = this.incrementErrorCount(violationKey);
        
        if (count >= 5) {
            this.triggerSecurityLockout('CSP_VIOLATIONS');
        }
    }

    /**
     * Handle network errors
     */
    handleNetworkError(error, url) {
        console.warn('🌐 Network error:', error, 'URL:', url);
        
        this.logSecurityEvent('NETWORK_ERROR', {
            error: error.message,
            url,
            timestamp: new Date().toISOString()
        });

        // Check if it's a security-related network error
        if (error.message.includes('CORS') || error.message.includes('blocked')) {
            this.handleSecurityError('NETWORK_SECURITY', { error: error.message, url });
        }
    }

    /**
     * Handle general security errors
     */
    handleSecurityError(type, details) {
        console.warn(`🛡️ Security error: ${type}`, details);
        
        this.logSecurityEvent('SECURITY_ERROR', {
            type,
            details: typeof details === 'object' ? JSON.stringify(details) : details,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Trigger security lockout
     */
    triggerSecurityLockout(reason) {
        console.error(`🚨 Security lockout triggered: ${reason}`);
        
        this.logSecurityEvent('SECURITY_LOCKOUT', {
            reason,
            duration: this.lockoutDuration,
            timestamp: new Date().toISOString()
        });

        // Clear all sessions and data
        if (window.app && window.app.auth) {
            window.app.auth.clearStoredSession();
        }

        // Show lockout message
        this.showSecurityLockout(reason);

        // Set lockout timer
        setTimeout(() => {
            this.clearErrorCounts();
            console.log('🔓 Security lockout expired');
        }, this.lockoutDuration);
    }

    /**
     * Show security lockout screen
     */
    showSecurityLockout(reason) {
        const lockoutScreen = `
            <div class="security-lockout-screen">
                <div class="lockout-content">
                    <h2>🔒 Security Lockout</h2>
                    <p>Access has been temporarily restricted due to: <strong>${reason}</strong></p>
                    <p>Please wait ${Math.round(this.lockoutDuration / 60000)} minutes before trying again.</p>
                    <p>If you believe this is an error, please contact your system administrator.</p>
                </div>
            </div>
        `;

        document.body.innerHTML = lockoutScreen;
    }

    /**
     * Show security warning
     */
    showSecurityWarning(title, message) {
        if (window.app && window.app.ui) {
            window.app.ui.showDialog(title, message, 'warning');
        } else {
            alert(`${title}: ${message}`);
        }
    }

    /**
     * Redirect to login
     */
    redirectToLogin(message) {
        if (window.app && window.app.showLogin) {
            if (message) {
                this.showSecurityWarning('Authentication Required', message);
            }
            window.app.showLogin();
        }
    }

    /**
     * Increment error count for a specific type
     */
    incrementErrorCount(errorType) {
        const current = this.errorCounts.get(errorType) || 0;
        const newCount = current + 1;
        this.errorCounts.set(errorType, newCount);
        return newCount;
    }

    /**
     * Clear all error counts
     */
    clearErrorCounts() {
        this.errorCounts.clear();
    }

    /**
     * Log security event
     */
    logSecurityEvent(eventType, details) {
        const event = {
            type: eventType,
            details,
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString()
        };

        // Store in local array
        this.suspiciousActivities.push(event);

        // Keep only last 100 events
        if (this.suspiciousActivities.length > 100) {
            this.suspiciousActivities.shift();
        }

        // Try to log to server if available
        if (window.app && window.app.data) {
            try {
                // This would send to audit logging system
                console.log('📝 Security event logged:', event);
            } catch (error) {
                console.warn('Failed to log security event to server:', error);
            }
        }
    }

    /**
     * Get security event history
     */
    getSecurityEvents() {
        return [...this.suspiciousActivities];
    }

    /**
     * Get current error counts
     */
    getErrorCounts() {
        return new Map(this.errorCounts);
    }
}
