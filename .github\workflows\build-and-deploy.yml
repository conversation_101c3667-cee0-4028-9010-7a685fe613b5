name: Build and Deploy S.T.E.V.I Retro

on:
  release:
    types: [published]  # Trigger when any release is published
  workflow_dispatch:  # Allow manual triggering for testing
    inputs:
      version:
        description: 'Version to build (e.g., 1.0.0)'
        required: true
        default: '1.0.0'

env:
  AZURE_STORAGE_ACCOUNT: iharcpublicappblob
  AZURE_CONTAINER: stevi
  <PERSON>_BLOB_PATH: stevi retro

jobs:
  build:
    runs-on: windows-latest
    
    outputs:
      version: ${{ steps.version.outputs.version }}
      release-notes: ${{ steps.release-notes.outputs.notes }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch full history for release notes
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Check if stable release
      id: check-stable
      run: |
        if ("${{ github.event_name }}" -eq "workflow_dispatch") {
          echo "Manual trigger - proceeding with build"
          echo "is_stable=true" >> $env:GITHUB_OUTPUT
        } elseif ("${{ github.event_name }}" -eq "release") {
          $isPrerelease = "${{ github.event.release.prerelease }}"
          $isDraft = "${{ github.event.release.draft }}"
          $tagName = "${{ github.event.release.tag_name }}"

          echo "Release tag: $tagName"
          echo "Is prerelease: $isPrerelease"
          echo "Is draft: $isDraft"

          if ($isDraft -eq "true") {
            echo "Draft release - skipping build"
            echo "is_stable=false" >> $env:GITHUB_OUTPUT
          } elseif ($isPrerelease -eq "true") {
            echo "Pre-release (beta/alpha) - skipping build"
            echo "is_stable=false" >> $env:GITHUB_OUTPUT
          } else {
            echo "Stable release detected - proceeding with build"
            echo "is_stable=true" >> $env:GITHUB_OUTPUT
          }
        } else {
          echo "Unknown trigger - skipping build"
          echo "is_stable=false" >> $env:GITHUB_OUTPUT
        }
      shell: pwsh

    - name: Extract version
      id: version
      if: steps.check-stable.outputs.is_stable == 'true'
      run: |
        if ("${{ github.event_name }}" -eq "workflow_dispatch") {
          $version = "${{ github.event.inputs.version }}"
          echo "Using manual version: $version"
        } elseif ("${{ github.event_name }}" -eq "release") {
          $tagName = "${{ github.event.release.tag_name }}"
          echo "Release tag: $tagName"

          # For 'stable' tag, use package.json version
          if ($tagName -eq "stable") {
            $packageJson = Get-Content package.json | ConvertFrom-Json
            $version = $packageJson.version
            echo "Using package.json version for stable release: $version"
          } else {
            # Extract version from tag name (standard approach)
            $version = $tagName -replace '^v', ''  # Remove 'v' prefix if present
            echo "Using version from tag: $version (tag: $tagName)"
          }
        } else {
          $version = "1.0.0"  # Fallback
          echo "Using fallback version: $version"
        }
        echo "version=$version" >> $env:GITHUB_OUTPUT
        echo "Building version: $version"
      shell: pwsh
    
    - name: Update package.json version
      if: steps.check-stable.outputs.is_stable == 'true'
      run: |
        $version = "${{ steps.version.outputs.version }}"
        $packageJson = Get-Content package.json | ConvertFrom-Json
        $packageJson.version = $version
        $packageJson | ConvertTo-Json -Depth 10 | Set-Content package.json
        echo "Updated package.json to version $version"
      shell: pwsh
    
    - name: Install dependencies
      if: steps.check-stable.outputs.is_stable == 'true'
      run: npm ci

    - name: Run security audit
      if: steps.check-stable.outputs.is_stable == 'true'
      run: npm audit --audit-level=moderate
      continue-on-error: true  # Don't fail build on audit issues, but log them

    - name: Check for outdated dependencies
      if: steps.check-stable.outputs.is_stable == 'true'
      run: npm outdated || true

    - name: Build application
      if: steps.check-stable.outputs.is_stable == 'true'
      run: npm run build-win
      env:
        NODE_ENV: production
        GH_TOKEN: ""  # Explicitly disable GitHub publishing
        GITHUB_TOKEN: ""  # Explicitly disable GitHub publishing
    
    - name: Generate release notes
      id: release-notes
      if: steps.check-stable.outputs.is_stable == 'true'
      run: |
        $version = "${{ steps.version.outputs.version }}"

        # Use GitHub release notes if available, otherwise use default
        if ("${{ github.event_name }}" -eq "release") {
          $releaseBody = @"
        ${{ github.event.release.body }}
        "@

          $releaseNotes = @"
        # S.T.E.V.I Retro v$version

        $releaseBody

        ---
        **Build Information:**
        - Built on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC')
        - Commit: ${{ github.sha }}
        - Release: ${{ github.event.release.html_url }}
        "@
        } else {
          $releaseNotes = @"
        # S.T.E.V.I Retro v$version

        ## 🚀 Features
        - Professional incident reporting system
        - Comprehensive record management (people, addresses, license plates)
        - Offline-first operation with automatic synchronization
        - Secure authentication and role-based access control
        - Retro terminal interface with modern functionality

        ## 🔧 Technical Improvements
        - Built with Electron for cross-platform compatibility
        - Supabase backend integration for real-time data
        - Secure local data caching and offline support
        - Automatic update system with Azure Blob Storage

        ## 📱 User Interface
        - Authentic retro DOS styling with red-on-black theme
        - Tabbed navigation system for easy access
        - Modal dialogs and professional forms
        - Real-time network status monitoring

        ## 🔒 Security
        - JWT token management with OS keychain storage
        - Row Level Security (RLS) policies
        - Role verification (requires iharc_staff role)
        - Secure configuration file handling

        Built on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss UTC')
        Commit: ${{ github.sha }}
        "@
        }
        
        # Save to file for upload
        $releaseNotes | Out-File -FilePath "release-notes.md" -Encoding UTF8

        # Output for next steps (simplified)
        echo "notes=Release notes generated successfully" >> $env:GITHUB_OUTPUT
      shell: pwsh
    
    - name: Create metadata file
      if: steps.check-stable.outputs.is_stable == 'true'
      run: |
        $version = "${{ steps.version.outputs.version }}"
        $metadata = @{
          version = $version
          buildDate = (Get-Date -Format 'yyyy-MM-ddTHH:mm:ssZ')
          commitSha = "${{ github.sha }}"
          buildNumber = "${{ github.run_number }}"
          platform = "windows"
          architecture = "x64"
          releaseType = "stable"
          downloadUrls = @{
            windows = "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/v$version/S.T.E.V.I-Retro-Installer.exe"
          }
          checksums = @{
            windows = ""  # Will be filled after build
          }
          minimumRequirements = @{
            os = "Windows 10 or later"
            memory = "4GB RAM"
            storage = "500MB available space"
            network = "Internet connection for initial setup and sync"
          }
        }
        
        $metadata | ConvertTo-Json -Depth 10 | Out-File -FilePath "metadata.json" -Encoding UTF8
        echo "Created metadata.json"
      shell: pwsh
    
    - name: Calculate checksums
      if: steps.check-stable.outputs.is_stable == 'true'
      run: |
        $exePath = Get-ChildItem -Path "dist" -Filter "*.exe" | Select-Object -First 1
        if ($exePath) {
          $checksum = Get-FileHash -Path $exePath.FullName -Algorithm SHA256
          echo "Windows EXE Checksum: $($checksum.Hash)"
          
          # Update metadata with checksum
          $metadata = Get-Content "metadata.json" | ConvertFrom-Json
          $metadata.checksums.windows = $checksum.Hash
          $metadata | ConvertTo-Json -Depth 10 | Set-Content "metadata.json"
          
          echo "checksum=$($checksum.Hash)" >> $env:GITHUB_OUTPUT
        }
      shell: pwsh
    
    - name: Upload build artifacts
      if: steps.check-stable.outputs.is_stable == 'true'
      uses: actions/upload-artifact@v4
      with:
        name: stevi-retro-v${{ steps.version.outputs.version }}
        path: |
          dist/*.exe
          metadata.json
          release-notes.md
        retention-days: 30

    - name: List build outputs
      if: steps.check-stable.outputs.is_stable == 'true'
      run: |
        echo "Build completed successfully!"
        echo "Version: ${{ steps.version.outputs.version }}"
        echo "Files created:"
        Get-ChildItem -Path "dist" -Recurse | Format-Table Name, Length, LastWriteTime
        echo "Metadata:"
        Get-Content "metadata.json"
      shell: pwsh

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: success() && needs.build.outputs.version != ''
    
    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: stevi-retro-v${{ needs.build.outputs.version }}
        path: ./artifacts

    - name: Debug artifacts
      run: |
        echo "=== DEBUGGING ARTIFACTS ==="
        echo "Contents of artifacts directory:"
        ls -la artifacts/ || echo "artifacts directory not found"
        echo ""
        echo "Searching for all files in artifacts:"
        find artifacts -type f || echo "No files found in artifacts"
        echo ""
        echo "Searching specifically for EXE files:"
        find artifacts -name "*.exe" -type f || echo "No EXE files found"
        echo "=========================="
    
    - name: Setup Azure CLI
      uses: azure/CLI@v1
      with:
        inlineScript: echo "Azure CLI ready"

    - name: Deploy to Azure Blob Storage
      env:
        AZURE_STORAGE_CONNECTION_STRING: ${{ secrets.AZURE_STORAGE_CONNECTION_STRING }}
      run: |
        VERSION="${{ needs.build.outputs.version }}"
        BLOB_PATH="stevi retro/v$VERSION"
        
        echo "Deploying version $VERSION to Azure Blob Storage..."
        
        # Create version-specific directory and upload files
        echo "Looking for EXE files to upload..."

        # Check both possible locations for EXE files
        if ls artifacts/dist/*.exe 1> /dev/null 2>&1; then
          echo "Found EXE files in artifacts/dist/"
          for file in artifacts/dist/*.exe; do
            if [ -f "$file" ]; then
              filename=$(basename "$file")
              echo "Uploading $filename from artifacts/dist/..."
              az storage blob upload \
                --container-name stevi \
                --name "$BLOB_PATH/$filename" \
                --file "$file" \
                --content-type "application/octet-stream" \
                --overwrite
            fi
          done
        elif ls artifacts/*.exe 1> /dev/null 2>&1; then
          echo "Found EXE files in artifacts/"
          for file in artifacts/*.exe; do
            if [ -f "$file" ]; then
              filename=$(basename "$file")
              echo "Uploading $filename from artifacts/..."
              az storage blob upload \
                --container-name stevi \
                --name "$BLOB_PATH/$filename" \
                --file "$file" \
                --content-type "application/octet-stream" \
                --overwrite
            fi
          done
        else
          echo "❌ ERROR: No EXE files found!"
          echo "Searching for EXE files in all locations:"
          find artifacts -name "*.exe" -type f || echo "No EXE files found anywhere"
          echo "Available files in artifacts:"
          ls -la artifacts/ || echo "artifacts directory is empty"
          exit 1
        fi
        
        # Upload metadata
        echo "Uploading metadata.json..."
        az storage blob upload \
          --container-name stevi \
          --name "$BLOB_PATH/metadata.json" \
          --file "artifacts/metadata.json" \
          --content-type "application/json" \
          --overwrite
        
        # Upload release notes
        echo "Uploading release-notes.md..."
        az storage blob upload \
          --container-name stevi \
          --name "$BLOB_PATH/release-notes.md" \
          --file "artifacts/release-notes.md" \
          --content-type "text/markdown" \
          --overwrite
        
        # Update latest version pointer
        echo "Updating latest version pointer..."
        echo "$VERSION" > latest-version.txt
        az storage blob upload \
          --container-name stevi \
          --name "stevi retro/latest-version.txt" \
          --file "latest-version.txt" \
          --content-type "text/plain" \
          --overwrite

        # Create and upload latest metadata for update system
        echo "Creating latest metadata for update system..."

        # Find the EXE file for metadata
        EXE_FILE=""
        if ls artifacts/dist/*.exe 1> /dev/null 2>&1; then
          EXE_FILE=$(ls artifacts/dist/*.exe | head -1)
        elif ls artifacts/*.exe 1> /dev/null 2>&1; then
          EXE_FILE=$(ls artifacts/*.exe | head -1)
        fi

        if [ -n "$EXE_FILE" ]; then
          FILE_SIZE=$(stat -c%s "$EXE_FILE")
          CHECKSUM=$(sha256sum "$EXE_FILE" | cut -d' ' -f1)
        else
          FILE_SIZE=0
          CHECKSUM=""
        fi

        cat > latest-metadata.json << EOF
        {
          "version": "$VERSION",
          "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
          "downloadUrl": "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/v$VERSION/S.T.E.V.I-Retro-Installer.exe",
          "fileSize": $FILE_SIZE,
          "checksum": "$CHECKSUM",
          "releaseNotes": "Enhanced Records Management - comprehensive person records with pets, medical issues, and organizations tracking",
          "minimumVersion": "1.0.0",
          "critical": false
        }
        EOF

        az storage blob upload \
          --container-name stevi \
          --name "stevi retro/latest-metadata.json" \
          --file "latest-metadata.json" \
          --content-type "application/json" \
          --overwrite
        
        echo "Deployment completed successfully!"
        echo "Files available at: https://iharcpublicappblob.blob.core.windows.net/stevi/$BLOB_PATH/"

    - name: Verify Deployment
      run: |
        VERSION="${{ needs.build.outputs.version }}"

        echo "Verifying deployment..."

        # Test latest-version.txt
        echo "Checking latest-version.txt..."
        curl -f "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/latest-version.txt" || echo "❌ latest-version.txt not accessible"

        # Test latest-metadata.json
        echo "Checking latest-metadata.json..."
        curl -f "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/latest-metadata.json" || echo "❌ latest-metadata.json not accessible"

        # Test version-specific metadata
        echo "Checking version-specific metadata..."
        curl -f "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/v$VERSION/metadata.json" || echo "❌ Version metadata not accessible"

        echo "✅ Deployment verification completed"
    
    - name: Create deployment summary
      run: |
        echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Version:** ${{ needs.build.outputs.version }}" >> $GITHUB_STEP_SUMMARY
        echo "**Build Date:** $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📦 Download Links" >> $GITHUB_STEP_SUMMARY
        echo "- [Windows Installer](https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/v${{ needs.build.outputs.version }}/)" >> $GITHUB_STEP_SUMMARY
        echo "- [Metadata](https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/v${{ needs.build.outputs.version }}/metadata.json)" >> $GITHUB_STEP_SUMMARY
        echo "- [Release Notes](https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/v${{ needs.build.outputs.version }}/release-notes.md)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 🔗 Latest Version Endpoints" >> $GITHUB_STEP_SUMMARY
        echo "- [Latest Version](https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/latest-version.txt)" >> $GITHUB_STEP_SUMMARY
        echo "- [Latest Metadata](https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/latest-metadata.json)" >> $GITHUB_STEP_SUMMARY
