// Data Manager for S.T.E.V.I DOS Electron App - Online-First with SQLite Caching
import { ConfigManager } from './config.js';
import { SchemaManager } from './schema.js';
import { SQLiteManager } from './sqlite-manager.js';

// Use Node.js path module if available (Electron renderer)
const path = window.require ? window.require('path') : null;

export class DataManager {
    constructor(authManager = null) {
        this.config = new ConfigManager();
        this.auth = authManager;
        this.isOnline = navigator.onLine;
        this.isTestMode = this.config.isTestMode();
        this.schema = new SchemaManager(this);
        this.sqlite = null;
        this.syncInProgress = false;
        this.syncInterval = null;
        this.realtimeSubscriptions = new Map();

        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('🌐 Online - Starting sync and real-time subscriptions');
            this.syncPendingData();
            this.startPeriodicSync();
            this.setupRealtimeSubscriptions();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('📴 Offline - Stopping sync and real-time subscriptions');
            this.stopPeriodicSync();
            this.cleanupRealtimeSubscriptions();
        });
    }

    async initialize() {
        console.log('🚀 Initializing DataManager...');

        // Clear all caches on startup to ensure fresh data
        await this.purgeAllCaches();

        // Initialize fallback schemas first (don't load from database yet)
        this.schema.loadFallbackSchemas();
        console.log('Loaded fallback schemas');

        // Always initialize memory cache
        this.cache = new Map();

        // Try to initialize SQLite database with fallback
        try {
            const dataPath = this.config.getDataPath();

            // Ensure the data directory exists
            if (window.require) {
                const fs = window.require('fs');
                if (!fs.existsSync(dataPath)) {
                    fs.mkdirSync(dataPath, { recursive: true });
                    console.log('📁 Created data directory:', dataPath);
                }
            }

            const dbPath = path ? path.join(dataPath, 'cache.db') : `${dataPath}/cache.db`;
            this.sqlite = new SQLiteManager(dbPath);
            await this.sqlite.init();

            // Migrate existing localStorage data to SQLite
            await this.migrateLocalStorageToSQLite();

            console.log('✅ SQLite caching enabled');
        } catch (error) {
            console.warn('⚠️ SQLite initialization failed, using memory cache only:', error.message);
            this.sqlite = null;
        }

        // Start online services if connected
        if (this.isOnline) {
            this.startPeriodicSync();
            this.setupRealtimeSubscriptions();
        }

        console.log('✅ DataManager initialized successfully');
    }

    /**
     * Purge all caches on startup to ensure fresh data from Supabase
     */
    async purgeAllCaches() {
        console.log('🧹 Purging all caches for fresh startup...');

        try {
            // Clear memory cache
            if (this.cache) {
                this.cache.clear();
                console.log('✅ Memory cache cleared');
            }

            // Clear SQLite cache database
            if (this.sqlite && this.sqlite.isInitialized) {
                await this.sqlite.clearAllCaches();
                console.log('✅ SQLite cache cleared');
            }

            // Clear localStorage (except saved username)
            if (typeof localStorage !== 'undefined') {
                const savedUsername = localStorage.getItem('stevidos_saved_username');
                localStorage.clear();
                if (savedUsername) {
                    localStorage.setItem('stevidos_saved_username', savedUsername);
                }
                console.log('✅ localStorage cleared (preserved username)');
            }

            // Clear any stored JWT tokens/sessions
            if (window.app && window.app.auth && window.app.auth.sessionManager) {
                window.app.auth.sessionManager.clearSession();
                console.log('✅ JWT session cleared');
            }

            // Clear session storage
            if (typeof sessionStorage !== 'undefined') {
                sessionStorage.clear();
                console.log('✅ sessionStorage cleared');
            }

            console.log('🧹 All caches purged successfully');
        } catch (error) {
            console.warn('⚠️ Error purging caches:', error);
        }
    }

    /**
     * Initialize real schemas from database after authentication
     * This should be called after user login
     */
    async initializeRealSchemas() {
        console.log('🔄 Loading real schemas from database...');
        try {
            await this.schema.reinitialize();
            console.log('✅ Real schemas loaded successfully');

            // After schemas are loaded, do initial data sync from Supabase
            console.log('🔄 Performing initial data sync from Supabase...');
            await this.performInitialDataSync();
            console.log('✅ Initial data sync completed');
        } catch (error) {
            console.warn('⚠️ Failed to load real schemas, using fallback:', error);
        }
    }

    /**
     * Perform initial data sync from Supabase after authentication
     * This ensures the cache is populated with existing data
     */
    async performInitialDataSync() {
        if (!this.isOnline || this.isTestMode) {
            console.log('Skipping initial sync - offline or test mode');
            return;
        }

        const supabase = this.getSupabaseClient();
        if (!supabase) {
            console.warn('No Supabase client available for initial sync');
            return;
        }

        // Sync core tables that are commonly used
        const coreTables = ['people', 'pets', 'incidents', 'addresses', 'organizations', 'items'];

        for (const table of coreTables) {
            try {
                console.log(`🔄 Syncing ${table} from Supabase...`);
                await this.refreshTableCache(table, supabase);
            } catch (error) {
                console.warn(`Failed to sync ${table}:`, error);
            }
        }
    }

    // Real-time subscription management
    setupRealtimeSubscriptions() {
        if (!this.isOnline || this.isTestMode) return;

        const supabase = this.getSupabaseClient();
        if (!supabase) return;

        console.log('🔄 Setting up real-time subscriptions...');

        // Subscribe to ALL tables used in the app for complete real-time sync
        const tables = [
            // Core schema tables
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'incarceration_status', 'bail_conditions',
            'court_dates', 'arrest_history', 'legal_contacts', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items', 'supply_provisions',
            'addresses', 'address_activities', 'organizations', 'media',
            'vehicle_activities', 'bikes', 'bike_activities', 'encampments',

            // Case management schema tables
            'incidents', 'incident_links', 'property_records', 'property_actions', 'license_plates',

            // Audit schema tables
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        tables.forEach(table => {
            // Capture table in closure to prevent variable reference issues
            const tableName = table;
            const schemaName = this.getSchemaName(tableName);

            const subscription = supabase
                .channel(`${tableName}_changes`)
                .on('postgres_changes',
                    {
                        event: '*',
                        schema: schemaName,
                        table: tableName  // Use just the table name, not the full name with schema
                    },
                    (payload) => {
                        console.log(`📡 Real-time event received for ${tableName}:`, payload.eventType);
                        this.handleRealtimeChange(tableName, payload);
                    }
                )
                .subscribe((status) => {
                    console.log(`📡 ${tableName} subscription status:`, status);
                    if (status === 'SUBSCRIBED') {
                        console.log(`✅ Successfully subscribed to ${schemaName}.${tableName}`);
                    }
                });

            this.realtimeSubscriptions.set(tableName, subscription);
        });
    }

    cleanupRealtimeSubscriptions() {
        console.log('🧹 Cleaning up real-time subscriptions...');

        this.realtimeSubscriptions.forEach((subscription, table) => {
            subscription.unsubscribe();
            console.log(`📡 Unsubscribed from ${table} changes`);
        });

        this.realtimeSubscriptions.clear();
    }

    handleRealtimeChange(table, payload) {
        // Add debugging to catch undefined table issues
        if (!table) {
            console.error('❌ handleRealtimeChange called with undefined table!', {
                table,
                payload: payload?.eventType,
                recordId: payload?.new?.id || payload?.old?.id,
                stack: new Error().stack
            });
            return;
        }

        console.log(`🔄 Real-time change in ${table}:`, payload.eventType, payload.new?.id || payload.old?.id);

        try {
            switch (payload.eventType) {
                case 'INSERT':
                    this.handleRealtimeInsert(table, payload.new);
                    break;
                case 'UPDATE':
                    this.handleRealtimeUpdate(table, payload.new);
                    break;
                case 'DELETE':
                    this.handleRealtimeDelete(table, payload.old);
                    break;
            }
        } catch (error) {
            console.error(`Error handling real-time change for ${table}:`, error);
        }
    }

    handleRealtimeInsert(table, record) {
        if (!table) {
            console.error('❌ handleRealtimeInsert called with undefined table!', { table, record: record?.id });
            return;
        }

        console.log(`📝 Real-time INSERT for ${table}:`, record?.id);

        // Add to SQLite cache (use upsert to handle duplicates)
        this.sqlite?.upsert(table, {
            ...record,
            last_synced: new Date().toISOString(),
            sync_status: 'synced'
        });

        // Emit event for UI updates
        this.emitDataChange(table, 'insert', record);
    }

    handleRealtimeUpdate(table, record) {
        if (!table) {
            console.error('❌ handleRealtimeUpdate called with undefined table!', { table, record: record?.id });
            return;
        }

        console.log(`📝 Real-time UPDATE for ${table}:`, record?.id);

        // Update SQLite cache with conflict resolution
        const existing = this.sqlite?.get(table, record.id);

        if (existing) {
            // Last write wins - compare timestamps
            const remoteTime = new Date(record.updated_at || record.created_at);
            const localTime = new Date(existing.updated_at || existing.created_at);

            if (remoteTime >= localTime) {
                this.sqlite?.update(table, record.id, {
                    ...record,
                    last_synced: new Date().toISOString(),
                    sync_status: 'synced'
                });

                this.emitDataChange(table, 'update', record);
            } else {
                console.log(`⏰ Local version is newer for ${table}:${record.id}, keeping local changes`);
            }
        } else {
            // Record doesn't exist locally, add it
            this.handleRealtimeInsert(table, record);
        }
    }

    handleRealtimeDelete(table, record) {
        if (!table) {
            console.error('❌ handleRealtimeDelete called with undefined table!', { table, record: record?.id });
            return;
        }

        console.log(`📝 Real-time DELETE for ${table}:`, record?.id);

        // Remove from SQLite cache
        this.sqlite?.delete(table, record.id);

        // Emit event for UI updates
        this.emitDataChange(table, 'delete', record);
    }

    emitDataChange(table, operation, record) {
        // Add debugging to catch undefined table issues
        if (!table) {
            console.error('❌ emitDataChange called with undefined table!', {
                table,
                operation,
                record: record?.id || 'no-id',
                stack: new Error().stack
            });
            return; // Don't emit event with undefined table
        }

        console.log(`📡 Emitting dataChange event: ${operation} in ${table}`, record?.id || 'no-id');

        // Emit custom event for UI components to listen to
        const event = new CustomEvent('dataChange', {
            detail: { table, operation, record }
        });
        window.dispatchEvent(event);
    }

    getSchemaName(table) {
        const coreSchema = [
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'incarceration_status', 'bail_conditions',
            'court_dates', 'arrest_history', 'legal_contacts', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items',
            'supply_provisions', 'addresses', 'address_activities',
            'organizations', 'media', 'vehicle_activities', 'bikes', 'bike_activities',
            'encampments'
        ];

        const auditSchema = [
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        const caseMgmtSchema = [
            'incidents', 'incident_links', 'property_records',
            'property_actions', 'license_plates'
        ];

        if (coreSchema.includes(table)) {
            return 'core';
        } else if (auditSchema.includes(table)) {
            return 'audit';
        } else if (caseMgmtSchema.includes(table)) {
            return 'case_mgmt';
        } else {
            return 'public';
        }
    }

    getSupabaseClient() {
        const client = this.auth?.supabase || null;
        if (client) {
            // Check if user is authenticated and log session details
            client.auth.getUser().then(({ data: { user }, error }) => {
                if (error) {
                    console.error('🔐 Auth check error:', error);
                } else if (user) {
                    console.log('🔐 Current user:', user.email, 'Role:', user.user_metadata?.role);
                    // Also check the session
                    client.auth.getSession().then(({ data: { session }, error: sessionError }) => {
                        if (sessionError) {
                            console.error('🔐 Session check error:', sessionError);
                        } else if (session) {
                            console.log('🔐 Session valid, expires at:', new Date(session.expires_at * 1000));
                            console.log('🔐 Access token preview:', session.access_token.substring(0, 50) + '...');

                            // Decode JWT to see what claims are present
                            try {
                                const payload = JSON.parse(atob(session.access_token.split('.')[1]));
                                console.log('🔐 JWT payload role claim:', payload.role);
                                console.log('🔐 JWT payload user_metadata:', payload.user_metadata);
                                console.log('🔐 JWT payload app_metadata:', payload.app_metadata);
                            } catch (e) {
                                console.error('🔐 Failed to decode JWT:', e);
                            }
                        } else {
                            console.warn('🔐 No active session found');
                        }
                    });
                } else {
                    console.warn('🔐 No authenticated user found');
                }
            });
        }
        return client;
    }

    getFullTableName(tableName) {
        // Map table names to their appropriate schemas
        const coreSchema = [
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'incarceration_status', 'bail_conditions',
            'court_dates', 'arrest_history', 'legal_contacts', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items',
            'supply_provisions', 'addresses', 'address_activities',
            'organizations', 'media', 'vehicle_activities', 'bikes', 'bike_activities',
            'encampments'
        ];

        const auditSchema = [
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        const caseMgmtSchema = [
            'incidents', 'incident_links', 'property_records',
            'property_actions', 'license_plates'
        ];

        if (coreSchema.includes(tableName)) {
            return `core.${tableName}`;
        } else if (auditSchema.includes(tableName)) {
            return `audit.${tableName}`;
        } else if (caseMgmtSchema.includes(tableName)) {
            return `case_mgmt.${tableName}`;
        } else {
            // Default to public for bikes and AI tables
            return tableName;
        }
    }

    // Online-first data operations
    async get(table, id) {
        try {
            // Always try SQLite cache first for immediate response
            const cachedData = this.sqlite?.get(table, id);

            // If online, fetch fresh data in background and update cache if changed
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    const fullTableName = this.getFullTableName(table);

                    // Use schema method for custom schemas
                    let query;
                    if (fullTableName.includes('.')) {
                        const [schema, tableName] = fullTableName.split('.');
                        query = supabase.schema(schema).from(tableName);
                    } else {
                        query = supabase.from(fullTableName);
                    }

                    const { data, error } = await query
                        .select('*')
                        .eq('id', id)
                        .single();

                    if (!error && data) {
                        // Check if data has changed using updated_at timestamp
                        const hasChanged = !cachedData ||
                            new Date(data.updated_at || data.created_at) >
                            new Date(cachedData.updated_at || cachedData.created_at);

                        if (hasChanged) {
                            // Update SQLite cache with fresh data
                            if (cachedData) {
                                this.sqlite.update(table, id, {
                                    ...data,
                                    last_synced: new Date().toISOString(),
                                    sync_status: 'synced'
                                });
                            } else {
                                this.sqlite.upsert(table, {
                                    ...data,
                                    last_synced: new Date().toISOString(),
                                    sync_status: 'synced'
                                });
                            }
                            return data;
                        }
                    }
                }
            }

            // Return cached data if available
            if (cachedData) {
                // Remove cache metadata before returning
                const { cached_at, last_synced, sync_status, cache_version, ...cleanData } = cachedData;
                return cleanData;
            }

            return null;
        } catch (error) {
            console.error('Error fetching data:', error);
            // Fallback to cached data
            const cachedData = this.sqlite?.get(table, id);
            if (cachedData) {
                const { cached_at, last_synced, sync_status, cache_version, ...cleanData } = cachedData;
                return cleanData;
            }
            return null;
        }
    }

    async insert(table, data) {
        try {
            const record = {
                ...data,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            // ONLINE-FIRST: Always try Supabase first when online
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    console.log(`📤 Inserting ${table} record to Supabase...`);
                    const fullTableName = this.getFullTableName(table);
                    console.log(`📋 Table: ${fullTableName}`);
                    console.log(`📋 Data:`, record);
                    console.log(`📋 Supabase URL:`, supabase.supabaseUrl);
                    console.log(`📋 Supabase Key:`, supabase.supabaseKey?.substring(0, 20) + '...');

                    // Try using schema method for custom schemas
                    let query;
                    if (fullTableName.includes('.')) {
                        const [schema, tableName] = fullTableName.split('.');
                        console.log(`🔍 Using schema method: schema('${schema}').from('${tableName}')`);
                        query = supabase.schema(schema).from(tableName);
                    } else {
                        console.log(`🔍 Using direct table access: from('${fullTableName}')`);
                        query = supabase.from(fullTableName);
                    }

                    const { data: result, error } = await query
                        .insert(record)
                        .select();

                    if (!error && result && result.length > 0) {
                        const savedRecord = result[0];
                        console.log(`✅ ${table} record saved to Supabase with ID: ${savedRecord.id}`);

                        // Cache in SQLite immediately (use upsert to handle duplicates)
                        if (this.sqlite) {
                            try {
                                this.sqlite.upsert(table, {
                                    ...savedRecord,
                                    last_synced: new Date().toISOString(),
                                    sync_status: 'synced'
                                });
                            } catch (cacheError) {
                                console.warn(`⚠️ Failed to cache ${table} record:`, cacheError);
                                // Don't throw - the Supabase insert succeeded, caching is secondary
                            }
                        }

                        // Emit event for UI updates
                        this.emitDataChange(table, 'insert', savedRecord);

                        return savedRecord;
                    } else {
                        console.warn(`⚠️ Supabase insert failed for ${table}:`, error);
                        console.warn(`⚠️ Error details:`, {
                            message: error?.message,
                            details: error?.details,
                            hint: error?.hint,
                            code: error?.code
                        });
                        throw new Error(error?.message || 'Supabase insert failed');
                    }
                }
            }

            // OFFLINE FALLBACK: Store locally and queue for sync
            console.log(`📴 Offline mode - storing ${table} record locally`);

            // Only add ID for tables that use TEXT PRIMARY KEY (like items, bikes)
            // Tables with INTEGER PRIMARY KEY (people, addresses, incidents) should auto-increment
            const tablesWithTextId = ['items', 'bikes', 'license_plates'];
            let recordForStorage = { ...record };
            let queueId = null;

            if (tablesWithTextId.includes(table)) {
                const tempId = this.generateId();
                recordForStorage = { ...record, id: tempId };
                queueId = tempId;
            } else {
                // For INTEGER PRIMARY KEY tables, let SQLite auto-generate the ID
                // Use a temporary ID for the sync queue only
                queueId = this.generateId();
            }

            // Store in SQLite with pending sync status (use upsert to handle duplicates)
            const insertedRecord = this.sqlite?.upsert(table, {
                ...recordForStorage,
                sync_status: 'pending'
            });

            // Add to sync queue with the queue ID
            this.sqlite?.addToSyncQueue(table, 'insert', queueId, recordForStorage);

            // Emit event for UI updates
            this.emitDataChange(table, 'insert', insertedRecord || recordForStorage);

            return insertedRecord || recordForStorage;
        } catch (error) {
            console.error(`❌ Error inserting ${table} record:`, error);

            // Final fallback - store locally and queue for sync
            const tablesWithTextId = ['items', 'bikes', 'license_plates'];
            let record = {
                ...data,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            let queueId = null;

            if (tablesWithTextId.includes(table)) {
                const id = data.id || this.generateId();
                record = { ...record, id: id };
                queueId = id;
            } else {
                // For INTEGER PRIMARY KEY tables, let SQLite auto-generate the ID
                // Use a temporary ID for the sync queue only
                queueId = this.generateId();
            }

            const insertedRecord = this.sqlite?.upsert(table, {
                ...record,
                sync_status: 'pending'
            });

            this.sqlite?.addToSyncQueue(table, 'insert', queueId, record);

            // Emit event for UI updates
            this.emitDataChange(table, 'insert', insertedRecord || record);

            return insertedRecord || record;
        }
    }

    async update(table, id, data) {
        try {
            const existing = await this.get(table, id);
            if (!existing) {
                throw new Error('Record not found');
            }

            const updateData = {
                ...data,
                updated_at: new Date().toISOString()
            };

            // ONLINE-FIRST: Always try Supabase first when online
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    console.log(`📤 Updating ${table} record ${id} in Supabase...`);
                    const fullTableName = this.getFullTableName(table);

                    // Use schema method for custom schemas
                    let query;
                    if (fullTableName.includes('.')) {
                        const [schema, tableName] = fullTableName.split('.');
                        query = supabase.schema(schema).from(tableName);
                    } else {
                        query = supabase.from(fullTableName);
                    }

                    const { data: result, error } = await query
                        .update(updateData)
                        .eq('id', id)
                        .select();

                    if (!error && result && result.length > 0) {
                        const updatedRecord = result[0];
                        console.log(`✅ ${table} record ${id} updated in Supabase`);

                        // Update SQLite cache
                        this.sqlite?.update(table, id, {
                            ...updatedRecord,
                            last_synced: new Date().toISOString(),
                            sync_status: 'synced'
                        });

                        // Emit event for UI updates
                        this.emitDataChange(table, 'update', updatedRecord);

                        return updatedRecord;
                    } else {
                        console.warn(`⚠️ Supabase update failed for ${table}:${id}:`, error);
                        throw new Error(error?.message || 'Supabase update failed');
                    }
                }
            }

            // OFFLINE FALLBACK: Store locally and queue for sync
            console.log(`📴 Offline mode - updating ${table} record ${id} locally`);
            const updated = { ...existing, ...updateData };

            // Update in SQLite with pending sync status
            this.sqlite?.update(table, id, {
                ...updated,
                sync_status: 'pending'
            });

            // Add to sync queue
            this.sqlite?.addToSyncQueue(table, 'update', id, updated);

            return updated;

            // Queue for sync if offline
            if (!this.isOnline) {
                this.queueForSync(table, 'update', updated);
            }

            return updated;
        } catch (error) {
            console.error('Error updating data:', error);
            throw error;
        }
    }

    async delete(table, id) {
        try {
            // Try Supabase first if online and available
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    const fullTableName = this.getFullTableName(table);

                    // Use schema method for custom schemas
                    let query;
                    if (fullTableName.includes('.')) {
                        const [schema, tableName] = fullTableName.split('.');
                        query = supabase.schema(schema).from(tableName);
                    } else {
                        query = supabase.from(fullTableName);
                    }

                    const { data, error } = await query
                        .delete()
                        .eq('id', id)
                        .select();

                    if (!error) {
                        console.log(`✅ Successfully deleted ${table} record ${id} from Supabase`);
                    } else {
                        console.warn(`⚠️ Supabase delete failed for ${table}:${id}:`, error.message);
                    }

                    if (!error) {
                        // Remove from SQLite cache
                        if (this.sqlite) {
                            this.sqlite.delete(table, id);
                        }

                        // Remove from memory cache
                        const cacheKey = `${table}_${id}`;
                        this.cache.delete(cacheKey);

                        // Emit event for UI updates
                        this.emitDataChange(table, 'delete', { id });

                        return true;
                    }
                    // If Supabase failed, fall through to local cache
                }
            }

            // Fallback to local cache
            if (this.sqlite) {
                this.sqlite.delete(table, id);
            }

            // Remove from memory cache
            const cacheKey = `${table}_${id}`;
            this.cache.delete(cacheKey);

            // Queue for sync if offline
            if (!this.isOnline) {
                this.queueForSync(table, 'delete', { id });
            }

            return true;
        } catch (error) {
            console.error('Error deleting data:', error);
            throw error;
        }
    }

    async getAll(table) {
        try {
            // Always return SQLite cache first for immediate response
            const cachedData = this.sqlite?.getAll(table) || [];

            // Clean cache metadata from results
            const cleanCachedData = cachedData.map(record => {
                const { cached_at, last_synced, sync_status, cache_version, ...cleanData } = record;
                return cleanData;
            });

            // If online, refresh cache in background
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    // Background refresh - don't await to keep UI responsive
                    // Ensure table parameter is preserved in async context
                    const tableToRefresh = table; // Capture table in closure
                    this.refreshTableCache(tableToRefresh, supabase).catch(error => {
                        console.warn(`Background refresh failed for ${tableToRefresh}:`, error);
                    });
                }
            }

            return cleanCachedData;
        } catch (error) {
            console.error(`Error fetching all ${table} data:`, error);
            // Return empty array as fallback
            return [];
        }
    }

    async refreshTableCache(table, supabase) {
        try {
            // Add debugging to catch undefined table issues
            if (!table) {
                console.error('❌ refreshTableCache called with undefined table!', {
                    table,
                    supabase: !!supabase,
                    stack: new Error().stack
                });
                return;
            }

            console.log(`🔄 Starting refreshTableCache for: ${table}`);
            const fullTableName = this.getFullTableName(table);

            // Use schema method for custom schemas
            let query;
            if (fullTableName.includes('.')) {
                const [schema, tableName] = fullTableName.split('.');
                query = supabase.schema(schema).from(tableName);
            } else {
                query = supabase.from(fullTableName);
            }

            const { data, error } = await query
                .select('*')
                .order('created_at', { ascending: false });

            if (!error && data) {
                console.log(`🔄 Refreshing ${table} cache with ${data.length} records`);

                // Update SQLite cache with fresh data
                data.forEach(record => {
                    const existing = this.sqlite?.get(table, record.id);

                    if (existing) {
                        // Update existing record
                        this.sqlite?.update(table, record.id, {
                            ...record,
                            last_synced: new Date().toISOString(),
                            sync_status: 'synced'
                        });
                    } else {
                        // Insert new record (use upsert to handle duplicates)
                        this.sqlite?.upsert(table, {
                            ...record,
                            last_synced: new Date().toISOString(),
                            sync_status: 'synced'
                        });
                    }
                });

                // Emit event for UI updates if data changed
                this.emitDataChange(table, 'refresh', data);
            }
        } catch (error) {
            console.error(`Error refreshing ${table} cache:`, error);
        }
    }

    async search(table, query) {
        try {
            let allRecords;

            // When online, get fresh data; when offline, use cached data
            if (this.isOnline && !this.isTestMode) {
                // Try to get fresh data from Supabase
                allRecords = await this.getAll(table);
            } else {
                // Get all records from SQLite cache or memory cache
                if (this.sqlite) {
                    allRecords = this.sqlite.getAll(table) || [];
                } else {
                    // Fallback to memory cache
                    allRecords = Array.from(this.cache.values())
                        .filter(item => item.table === table)
                        .map(item => item.data);
                }
            }

            if (!allRecords) {
                return [];
            }

            if (!query || Object.keys(query).length === 0) {
                return allRecords;
            }

            // Simple search implementation
            return allRecords.filter(record => {
                return Object.entries(query).every(([key, value]) => {
                    if (!record[key]) return false;

                    const recordValue = record[key].toString().toLowerCase();
                    const searchValue = value.toString().toLowerCase();

                    return recordValue.includes(searchValue);
                });
            });
        } catch (error) {
            console.error('Error searching data:', error);
            return [];
        }
    }

    // Legacy localStorage helpers - kept only for migration
    getAllFromLocalStorage(table) {
        const key = `stevidos_${table}`;
        const data = localStorage.getItem(key);

        if (data) {
            return JSON.parse(data);
        }

        return [];
    }

    // Sync helpers - now using SQLite for queue storage
    queueForSync(table, operation, data) {
        if (this.sqlite) {
            // Store sync queue in SQLite
            this.sqlite.insert('sync_queue', {
                id: this.generateId(),
                table,
                operation,
                data: JSON.stringify(data),
                timestamp: new Date().toISOString(),
                status: 'pending'
            });
        } else {
            // Fallback to memory queue
            if (!this.syncQueue) this.syncQueue = [];
            this.syncQueue.push({
                table,
                operation,
                data,
                timestamp: new Date().toISOString()
            });
        }
    }

    async syncPendingData() {
        if (this.syncInProgress) {
            console.log('🔄 Sync already in progress, skipping...');
            return;
        }

        if (!this.sqlite) {
            console.warn('SQLite not initialized, cannot sync');
            return;
        }

        const syncQueue = this.sqlite.getSyncQueue();

        if (syncQueue.length === 0) {
            console.log('📭 No pending sync operations');
            return;
        }

        console.log(`🔄 Syncing ${syncQueue.length} pending operations...`);
        this.syncInProgress = true;

        const supabase = this.getSupabaseClient();
        if (!supabase) {
            console.warn('No Supabase client available for sync');
            this.syncInProgress = false;
            return;
        }

        let successCount = 0;
        let failCount = 0;

        for (const syncItem of syncQueue) {
            this.sqlite.updateSyncQueueStatus(syncItem.id, 'syncing');

            try {
                const { id: queueId, table_name: table, operation, record_id, data } = syncItem;
                const recordData = JSON.parse(data);
                const fullTableName = this.getFullTableName(table);

                console.log(`🔄 Syncing ${operation} for ${table}:${record_id}`);

                switch (operation) {
                    case 'insert':
                        const { data: insertResult, error: insertError } = await supabase
                            .from(fullTableName)
                            .insert(recordData)
                            .select();

                        if (insertError) throw insertError;

                        if (insertResult && insertResult.length > 0) {
                            const serverRecord = insertResult[0];

                            // Update SQLite cache with server data
                            this.sqlite.update(table, record_id, {
                                ...serverRecord,
                                last_synced: new Date().toISOString(),
                                sync_status: 'synced'
                            });

                            console.log(`✅ ${table} insert synced: ${record_id} → ${serverRecord.id}`);
                        }
                        break;

                    case 'update':
                        // Implement last write wins conflict resolution
                        const localRecord = this.sqlite.get(table, record_id);
                        if (localRecord) {
                            const { data: updateResult, error: updateError } = await supabase
                                .from(fullTableName)
                                .update(recordData)
                                .eq('id', record_id)
                                .select();

                            if (updateError) throw updateError;

                            if (updateResult && updateResult.length > 0) {
                                const serverRecord = updateResult[0];

                                // Update SQLite cache
                                this.sqlite.update(table, record_id, {
                                    ...serverRecord,
                                    last_synced: new Date().toISOString(),
                                    sync_status: 'synced'
                                });

                                console.log(`✅ ${table} update synced: ${record_id}`);
                            }
                        }
                        break;

                    case 'delete':
                        const { error: deleteError } = await supabase
                            .from(fullTableName)
                            .delete()
                            .eq('id', record_id);

                        if (deleteError) throw deleteError;

                        // Remove from SQLite cache
                        this.sqlite.delete(table, record_id);
                        console.log(`✅ ${table} delete synced: ${record_id}`);
                        const cacheKey = `${table}_${data.id}`;
                        this.cache.delete(cacheKey);
                        break;

                    default:
                        console.warn(`Unknown sync operation: ${operation}`);
                }

                // Mark as completed in sync queue
                this.sqlite.updateSyncQueueStatus(queueId, 'completed');
                successCount++;

            } catch (error) {
                console.error(`❌ Failed to sync ${operation} for ${table}:${record_id}:`, error);

                // Mark as failed and increment retry count
                this.sqlite.updateSyncQueueStatus(queueId, 'failed', error.message);
                failCount++;
            }
        }

        // Clean up completed sync items
        this.sqlite.clearSyncQueue();

        console.log(`🔄 Sync completed: ${successCount} successful, ${failCount} failed`);
        this.syncInProgress = false;

        // If there are failed syncs, they remain in queue for retry
        if (failCount > 0) {
            console.warn(`⚠️ ${failCount} sync operations failed and will be retried later`);
        }
    }

    // Manual sync trigger
    async forceSyncPendingData() {
        console.log('Manually triggering sync...');
        await this.syncPendingData();
    }

    // Periodic sync management
    startPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }

        // Sync every 5 minutes when online
        this.syncInterval = setInterval(() => {
            if (this.isOnline && !this.isTestMode) {
                this.syncPendingData();
            }
        }, 5 * 60 * 1000); // 5 minutes

        console.log('Periodic sync started (every 5 minutes)');
    }

    stopPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
            console.log('Periodic sync stopped');
        }
    }

    // Utility methods
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    clearCache() {
        this.cache.clear();
    }

    getStats() {
        const sqliteStats = this.sqlite?.getStats() || {};
        const syncQueue = this.sqlite?.getSyncQueue() || [];

        return {
            isOnline: this.isOnline,
            isTestMode: this.isTestMode,
            sqliteInitialized: !!this.sqlite?.isInitialized,
            realtimeSubscriptions: this.realtimeSubscriptions.size,
            syncInProgress: this.syncInProgress,
            pendingSync: syncQueue.length,
            cacheStats: sqliteStats,
            lastSync: this.lastSyncTime || null
        };
    }

    // Cleanup method for app shutdown
    async cleanup() {
        console.log('🧹 Cleaning up DataManager...');

        // Stop periodic sync
        this.stopPeriodicSync();

        // Cleanup real-time subscriptions
        this.cleanupRealtimeSubscriptions();

        // Close SQLite database
        if (this.sqlite) {
            this.sqlite.close();
        }

        console.log('✅ DataManager cleanup completed');
    }

    // Force refresh all cached data
    async refreshAllCaches() {
        if (!this.isOnline || this.isTestMode) {
            console.log('Cannot refresh caches - offline or test mode');
            return;
        }

        const supabase = this.getSupabaseClient();
        if (!supabase) return;

        // Refresh cache for ALL tables used in the app
        const tables = [
            // Core schema tables
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'incarceration_status', 'bail_conditions',
            'court_dates', 'arrest_history', 'legal_contacts', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items', 'supply_provisions',
            'addresses', 'address_activities', 'organizations', 'media',
            'vehicle_activities', 'bikes', 'bike_activities', 'encampments',

            // Case management schema tables
            'incidents', 'incident_links', 'property_records', 'property_actions', 'license_plates',

            // Audit schema tables
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        console.log('🔄 Refreshing all caches...');

        for (const table of tables) {
            try {
                await this.refreshTableCache(table, supabase);
            } catch (error) {
                console.error(`Failed to refresh ${table} cache:`, error);
            }
        }

        console.log('✅ All caches refreshed');
    }

    // Migrate existing localStorage data to SQLite
    async migrateLocalStorageToSQLite() {
        if (!this.sqlite) return;

        console.log('🔄 Migrating localStorage data to SQLite...');

        // First, let's see what's actually in localStorage
        console.log('🔍 Checking localStorage contents...');
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('stevidos_')) {
                const value = localStorage.getItem(key);
                console.log(`📋 localStorage[${key}]: ${value ? value.substring(0, 100) + '...' : 'null'}`);
            }
        }

        // Migrate localStorage data for ALL tables used in the app
        const tables = [
            // Core schema tables
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'incarceration_status', 'bail_conditions',
            'court_dates', 'arrest_history', 'legal_contacts', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items', 'supply_provisions',
            'addresses', 'address_activities', 'organizations', 'media',
            'vehicle_activities', 'bikes', 'bike_activities', 'encampments',

            // Case management schema tables
            'incidents', 'incident_links', 'property_records', 'property_actions', 'license_plates',

            // Audit schema tables
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];
        let totalMigrated = 0;

        for (const table of tables) {
            try {
                // Check if SQLite table already has data
                const existingData = this.sqlite.getAll(table);
                if (existingData && existingData.length > 0) {
                    console.log(`⏭️ ${table} already has ${existingData.length} records in SQLite, skipping migration`);
                    continue;
                }

                // Try multiple localStorage key patterns
                const possibleKeys = [
                    `stevidos_${table}`,
                    `stevidos_data_${table}`,
                    `stevidos_cache_${table}`,
                    table
                ];

                let localStorageData = null;
                let usedKey = null;

                for (const key of possibleKeys) {
                    const data = localStorage.getItem(key);
                    if (data) {
                        try {
                            const parsed = JSON.parse(data);
                            if (Array.isArray(parsed) && parsed.length > 0) {
                                localStorageData = parsed;
                                usedKey = key;
                                break;
                            } else if (parsed && typeof parsed === 'object') {
                                // Single object, convert to array
                                localStorageData = [parsed];
                                usedKey = key;
                                break;
                            }
                        } catch (e) {
                            console.warn(`⚠️ Failed to parse localStorage[${key}]:`, e);
                        }
                    }
                }

                if (!localStorageData || localStorageData.length === 0) {
                    console.log(`📭 No ${table} data found in localStorage (checked keys: ${possibleKeys.join(', ')})`);
                    continue;
                }

                console.log(`📦 Migrating ${localStorageData.length} ${table} records from ${usedKey}...`);

                // Migrate each record
                for (const record of localStorageData) {
                    try {
                        this.sqlite.upsert(table, {
                            ...record,
                            last_synced: new Date().toISOString(),
                            sync_status: 'synced'
                        });
                        totalMigrated++;
                    } catch (error) {
                        console.warn(`⚠️ Failed to migrate ${table} record ${record.id}:`, error);
                        console.warn('Record data:', record);
                    }
                }

                console.log(`✅ Migrated ${localStorageData.length} ${table} records`);

                // Clear the migrated data from localStorage
                localStorage.removeItem(usedKey);
                console.log(`🧹 Cleared localStorage[${usedKey}]`);

            } catch (error) {
                console.error(`❌ Error migrating ${table} data:`, error);
            }
        }

        // Clean up data localStorage entries (preserve app settings)
        const preservedKeys = [
            'stevidos_saved_username',  // Remember email functionality
            'stevidos_session',         // Authentication session
            'stevidos_config',          // User configuration
            'stevidos_data_path',       // Data path configuration
            'stevidos_cache'            // General cache (managed by auth)
        ];

        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('stevidos_') && !preservedKeys.includes(key)) {
                keysToRemove.push(key);
            }
        }

        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
            console.log(`🧹 Cleaned up localStorage[${key}]`);
        });

        if (preservedKeys.some(key => localStorage.getItem(key))) {
            console.log(`🔒 Preserved app settings: ${preservedKeys.filter(key => localStorage.getItem(key)).join(', ')}`);
        }

        if (totalMigrated > 0) {
            console.log(`🎉 Migration completed: ${totalMigrated} total records migrated to SQLite`);
            console.log('🧹 localStorage cleanup completed');
        } else {
            console.log('📭 No data to migrate from localStorage');
        }
    }
}
