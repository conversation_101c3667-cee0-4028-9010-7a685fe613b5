-- Debug User Data and Temporarily Bypass Admin Check
-- This script will help us understand what's happening with the user data

-- Step 1: Show current user data
SELECT 'Current user data:' as info;
SELECT 
    id,
    email,
    raw_app_meta_data,
    raw_user_meta_data,
    role
FROM auth.users 
WHERE id = auth.uid();

-- Step 2: Show all users for comparison
SELECT 'All users in the system:' as info;
SELECT 
    id,
    email,
    raw_app_meta_data,
    role
FROM auth.users 
ORDER BY email;

-- Step 3: Temporarily create a function that bypasses admin check for testing
CREATE OR REPLACE FUNCTION core.get_users_with_roles_debug()
RETURNS TABLE(
    user_id UUID,
    email TEXT,
    full_name TEXT,
    roles TEXT[],
    permissions TEXT[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Temporarily bypass admin check for debugging
    -- Just return the data to see if the function works
    
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.email,
        COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name') as full_name,
        ARRAY_AGG(DISTINCT r.name) as roles,
        ARRAY_AGG(DISTINCT p.name) as permissions
    FROM auth.users u
    LEFT JOIN core.user_roles ur ON u.id = ur.user_id
    LEFT JOIN core.roles r ON ur.role_id = r.id
    LEFT JOIN core.role_permissions rp ON r.id = rp.role_id
    LEFT JOIN core.permissions p ON rp.permission_id = p.id
    WHERE u.email LIKE '%@iharc.ca'
    GROUP BY u.id, u.email, u.raw_user_meta_data
    ORDER BY u.email;
END;
$$;

-- Step 4: Grant execute permission
GRANT EXECUTE ON FUNCTION core.get_users_with_roles_debug() TO authenticated;

-- Step 5: Test the debug function
SELECT 'Testing debug function (no admin check):' as info;
SELECT * FROM core.get_users_with_roles_debug();

-- Step 6: Check what roles and permissions exist
SELECT 'Available roles:' as info;
SELECT * FROM core.roles;

SELECT 'Available permissions:' as info;
SELECT * FROM core.permissions;

SELECT 'Role-permission assignments:' as info;
SELECT 
    r.name as role_name,
    p.name as permission_name
FROM core.roles r
JOIN core.role_permissions rp ON r.id = rp.role_id
JOIN core.permissions p ON rp.permission_id = p.id
ORDER BY r.name, p.name;

-- Step 7: Check user role assignments
SELECT 'User role assignments:' as info;
SELECT 
    u.email,
    r.name as role_name
FROM auth.users u
LEFT JOIN core.user_roles ur ON u.id = ur.user_id
LEFT JOIN core.roles r ON ur.role_id = r.id
ORDER BY u.email; 