-- Test script to verify the permission system is working correctly
-- Run this in Supabase SQL Editor after the migration

-- 1. Check if the core schema and tables exist
SELECT 
    schemaname,
    tablename 
FROM pg_tables 
WHERE schemaname = 'core' 
ORDER BY tablename;

-- 2. Check if the functions exist
SELECT 
    proname as function_name,
    prosrc as function_source
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public' 
AND proname IN ('get_user_roles', 'get_user_permissions', 'has_permission', 'refresh_user_claims', 'assign_user_role', 'remove_user_role', 'get_users_with_roles');

-- 3. <NAME_EMAIL> has admin role
DO $$
DECLARE
    admin_user_id UUID;
    user_roles TEXT[];
BEGIN
    -- Find the user <NAME_EMAIL>
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    IF admin_user_id IS NOT NULL THEN
        -- Get user roles
        SELECT ARRAY_AGG(role_name) INTO user_roles
        FROM public.get_user_roles(admin_user_id);
        
        RAISE NOTICE 'User <EMAIL> (ID: %) has roles: %', admin_user_id, user_roles;
        
        -- Check if user has admin permission
        IF public.has_permission('admin.access') THEN
            RAISE NOTICE 'User has admin.access permission';
        ELSE
            RAISE NOTICE 'User does NOT have admin.access permission';
        END IF;
        
    ELSE
        RAISE NOTICE 'User <EMAIL> not found';
    END IF;
END $$;

-- 4. Check all users with roles
SELECT * FROM public.get_users_with_roles();

-- 5. Test permission checking for different permissions
SELECT 
    'admin.access' as permission,
    public.has_permission('admin.access') as has_permission
UNION ALL
SELECT 
    'users.read' as permission,
    public.has_permission('users.read') as has_permission
UNION ALL
SELECT 
    'users.create' as permission,
    public.has_permission('users.create') as has_permission;

-- 6. Check the core.user_roles table
SELECT 
    ur.id,
    u.email,
    r.name as role_name,
    ur.granted_at
FROM core.user_roles ur
JOIN auth.users u ON ur.user_id = u.id
JOIN core.roles r ON ur.role_id = r.id
ORDER BY u.email, r.name;

-- 7. Check role permissions mapping
SELECT 
    r.name as role_name,
    p.name as permission_name,
    p.category
FROM core.roles r
JOIN core.role_permissions rp ON r.id = rp.role_id
JOIN core.permissions p ON rp.permission_id = p.id
ORDER BY r.name, p.category, p.name; 