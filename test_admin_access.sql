-- Test Admin Access - Simple verification script
-- Run this to check if everything is working correctly

-- Step 1: Check if user exists and has admin role
SELECT 'Step 1: Checking user and role assignment' as info;
SELECT 
    u.email,
    r.name as role_name,
    ur.user_id,
    ur.role_id
FROM auth.users u
LEFT JOIN core.user_roles ur ON u.id = ur.user_id
LEFT JOIN core.roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';

-- Step 2: Check if admin role has permissions
SELECT 'Step 2: Checking admin role permissions' as info;
SELECT 
    r.name as role_name,
    p.name as permission_name,
    p.category
FROM core.roles r
JOIN core.role_permissions rp ON r.id = rp.role_id
JOIN core.permissions p ON rp.permission_id = p.id
WHERE r.name = 'iharc_admin'
ORDER BY p.category, p.name;

-- Step 3: Test core.get_user_roles function
SELECT 'Step 3: Testing core.get_user_roles function' as info;
SELECT core.get_user_roles(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Step 4: Test core.get_user_permissions function
SELECT 'Step 4: Testing core.get_user_permissions function' as info;
SELECT core.get_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Step 5: Test core.has_permission function
SELECT 'Step 5: Testing core.has_permission function' as info;
SELECT 
    'admin.access' as permission,
    core.has_permission('admin.access') as has_permission
UNION ALL
SELECT 
    'users.read' as permission,
    core.has_permission('users.read') as has_permission;

-- Step 6: Check current JWT metadata
SELECT 'Step 6: Current JWT metadata' as info;
SELECT 
    id,
    email,
    raw_app_meta_data
FROM auth.users 
WHERE email = '<EMAIL>';

-- Step 7: Refresh permissions and check again
SELECT 'Step 7: Refreshing permissions...' as info;
SELECT core.refresh_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Step 8: Check updated JWT metadata
SELECT 'Step 8: Updated JWT metadata' as info;
SELECT 
    id,
    email,
    raw_app_meta_data
FROM auth.users 
WHERE email = '<EMAIL>';

-- Step 9: Test get_users_with_roles function
SELECT 'Step 9: Testing get_users_with_roles function' as info;
SELECT * FROM core.get_users_with_roles(); 