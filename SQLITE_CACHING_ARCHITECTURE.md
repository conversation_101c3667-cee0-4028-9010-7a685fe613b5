# SQLite Caching Architecture Plan

## Overview
Redesign the data management system to be online-first with SQLite caching, real-time updates, and last-write-wins conflict resolution.

## Core Principles

### 1. Online-First Approach
- **When Online**: All writes go to Supabase first, then cached locally
- **Never Store Solely Local**: Data is always persisted to remote when online
- **SQLite as Cache**: Local database mirrors remote data for performance
- **Offline Fallback**: Only write locally when offline, queue for sync

### 2. Real-Time Synchronization
- **Live Updates**: Subscribe to Supabase real-time changes
- **Automatic Refresh**: Cache updates automatically when remote data changes
- **Multi-Client Sync**: Changes from other clients appear immediately

### 3. Last Write Wins Conflict Resolution
- **Timestamp-Based**: Use `updated_at` field to determine latest version
- **Simple Strategy**: Most recent update always wins
- **Conflict Detection**: Compare timestamps during sync operations

## Architecture Components

### 1. SQLiteManager
```javascript
class SQLiteManager {
  // Database initialization and schema management
  // CRUD operations on local cache
  // Transaction support for consistency
  // Schema migration support
}
```

### 2. RealtimeManager  
```javascript
class RealtimeManager {
  // Supabase real-time subscriptions
  // Event handling for data changes
  // Connection management
  // Subscription lifecycle
}
```

### 3. SyncManager
```javascript
class SyncManager {
  // Conflict resolution logic
  // Offline queue management
  // Last write wins implementation
  // Sync status tracking
}
```

### 4. Enhanced DataManager
```javascript
class DataManager {
  // Orchestrates all data operations
  // Online-first decision making
  // Cache management
  // Error handling and fallbacks
}
```

## Data Flow Architecture

### Create Operation (Online)
```
User Input → DataManager → Supabase → SQLite Cache → UI Update
                      ↓
                Real-time Event → Other Clients
```

### Read Operation
```
UI Request → SQLite Cache (immediate) → UI Update
          ↓
Background Refresh → Supabase → Cache Update (if changed)
```

### Update Operation (Online)
```
User Input → DataManager → Supabase (with timestamp) → SQLite Cache → UI Update
                      ↓
                Real-time Event → Other Clients → Cache Update
```

### Real-time Sync
```
Other Client Change → Supabase → Real-time Event → SQLite Cache → UI Update
```

### Offline Operation
```
User Input → DataManager → SQLite Cache → Sync Queue
          ↓
When Online → Sync Queue → Conflict Resolution → Supabase → Cache Update
```

## SQLite Schema Design

### Cache Tables (Mirror Supabase)
```sql
-- Core tables with additional cache metadata
CREATE TABLE cache_people (
  id INTEGER PRIMARY KEY,
  -- All original columns from Supabase
  first_name TEXT,
  last_name TEXT,
  -- ... other fields
  
  -- Cache metadata
  cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_synced DATETIME,
  sync_status TEXT DEFAULT 'synced', -- 'synced', 'pending', 'conflict'
  version INTEGER DEFAULT 1
);

-- Sync queue for offline operations
CREATE TABLE sync_queue (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  operation TEXT NOT NULL, -- 'insert', 'update', 'delete'
  record_id TEXT NOT NULL,
  data TEXT, -- JSON data
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  retry_count INTEGER DEFAULT 0,
  status TEXT DEFAULT 'pending' -- 'pending', 'syncing', 'completed', 'failed'
);

-- Conflict resolution log
CREATE TABLE conflict_log (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  record_id TEXT NOT NULL,
  local_version TEXT, -- JSON
  remote_version TEXT, -- JSON
  resolution TEXT, -- 'local_wins', 'remote_wins', 'merged'
  resolved_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Implementation Strategy

### Phase 1: SQLite Foundation
1. Add better-sqlite3 dependency
2. Create SQLiteManager class
3. Implement basic CRUD operations
4. Set up database initialization

### Phase 2: Online-First DataManager
1. Modify existing DataManager
2. Implement online-first logic
3. Add SQLite caching layer
4. Maintain backward compatibility

### Phase 3: Real-time Subscriptions
1. Create RealtimeManager
2. Set up Supabase subscriptions
3. Handle real-time events
4. Update cache automatically

### Phase 4: Conflict Resolution
1. Implement SyncManager
2. Add last write wins logic
3. Handle offline sync queue
4. Add conflict logging

### Phase 5: Testing & Validation
1. Test online/offline scenarios
2. Validate conflict resolution
3. Performance testing
4. Multi-client testing

## Benefits

### Performance
- **Fast Reads**: SQLite cache provides instant data access
- **Reduced Network**: Only sync when data changes
- **Efficient Queries**: Local SQL queries for complex operations

### Reliability
- **Always Current**: Real-time updates keep data fresh
- **Conflict Resolution**: Automatic handling of concurrent edits
- **Offline Support**: Graceful degradation when offline

### User Experience
- **Instant Response**: UI updates immediately from cache
- **Live Collaboration**: See changes from other users instantly
- **Seamless Sync**: Transparent online/offline transitions

## Migration Plan

### Backward Compatibility
- Keep existing localStorage as fallback
- Gradual migration of data to SQLite
- Maintain existing API surface

### Data Migration
- Export existing localStorage data
- Import into SQLite cache
- Verify data integrity
- Clean up old storage
