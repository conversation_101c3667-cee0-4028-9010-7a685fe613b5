# S.T.E.V.I Retro - System Documentation

## Overview

**S.T.E.V.I Retro** (Supportive Technology to Enable Vulnerable Individuals) is an Electron-based desktop application designed for IHARC (Indigenous Housing and Resource Centre) staff to manage outreach activities, case management, and resource coordination for vulnerable populations.

## Core Functionality

### Primary Use Cases
- **Person Management**: Track individuals, their housing status, medical issues, and pets
- **Activity Logging**: Record ranger activities, outreach transactions, and interactions
- **Property Management**: Log found property, track stolen items, manage bike registrations
- **Organization Management**: Maintain database of service providers and partner organizations
- **Supply Distribution**: Track inventory and distribution of supplies to individuals
- **Case Management**: Handle incidents, investigations, and follow-up activities
- **Reporting**: Generate reports and maintain audit trails

### Target Users
- **IHARC Staff**: Day-to-day operations, data entry, basic reporting
- **IHARC Admins**: Full system access, user management, advanced features

## Technical Architecture

### Frontend Stack
- **Electron**: Desktop application framework
- **HTML5/CSS3**: UI structure and styling
- **Vanilla JavaScript**: Application logic (no frameworks like React/Vue)
- **CSS Grid/Flexbox**: Responsive layout system

### Backend & Data
- **Supabase**: Backend-as-a-Service (PostgreSQL database, authentication, real-time sync)
- **Row Level Security (RLS)**: Database-level access control
- **Multi-schema Database**: 
  - `core`: Main application data (people, pets, organizations, etc.)
  - `case_mgmt`: Case management and property records
  - `audit`: Activity logs and audit trails
  - `public`: User profiles and system data

### Key Dependencies
- **Electron**: Desktop app framework
- **Supabase JS Client**: Database and auth integration
- **Node.js**: Runtime environment
- **Electron Builder**: Application packaging and distribution

## Application Structure

### Main Entry Points
- **main.js**: Electron main process, window management, auto-updater
- **renderer/index.html**: Main application window
- **renderer/js/app.js**: Primary application controller
- **supabase/functions/admin-user-management**: Edge Function for admin operations

### Core Modules
- **auth.js**: Authentication and user management
- **data.js**: Database operations and caching
- **ui.js**: User interface management and dialogs
- **commands.js**: Command system for user actions
- **schema.js**: Database schema definitions and form generation
- **vault-manager.js**: Secure API key management via Supabase Vault
- **admin.js**: Administrative functions
- **weather.js**: Weather service integration
- **bike-manager.js**: Bike registration and theft management
- **property-manager.js**: Property and evidence management

## Navigation Structure

### Main Tabs
1. **Dashboard**: Overview, active incidents, weather, quick actions
2. **Records**: Person management, organizations, search functionality
3. **Activities**: Activity logging by category (outreach, enforcement, etc.)
4. **Property**: Found property, missing property reports, bike management
5. **Dispatch**: Computer-aided dispatch, active incidents management
6. **Admin**: User management, system settings (admin only)

### Sub-Navigation Examples

#### Records Tab
- **People Management**: List, search, add, edit person records
  - Person Detail Pages: Basic info, pets, medical issues, activities
- **Organizations Management**: Service provider database
- **Search**: Global search across all record types

#### Activities Tab
- **Category Selection**: Outreach, enforcement, medical, administrative
- **Activity Forms**: Category-specific data entry forms
- **Activity History**: View and search past activities

#### Property Tab
- **Found Property List**: Manage recovered items
- **Missing Property Reports**: Track stolen/lost items
  - General Property Reports
  - Stolen Bike Reports
- **Log Property Recovery**: Record found items

## Data Management

### Database Schema
- **People**: Core demographic and contact information
- **Pets**: Animal companions linked to individuals
- **Medical Issues**: Health conditions and treatment tracking
- **Organizations**: Service providers and partner agencies
- **Activities**: All ranger and staff activities with full audit trail
- **Property Records**: Found, stolen, and recovered property
- **Bikes**: Bicycle registration and theft management
- **User Profiles**: Staff information and IHARC staff IDs

### Caching Strategy
- **Multi-level Caching**: Memory cache → Local storage → Supabase
- **Offline Capability**: Local storage fallback when offline
- **Sync Queue**: Automatic synchronization when connection restored
- **Real-time Updates**: Supabase real-time subscriptions for live data

### Security Model
- **Role-based Access**: `iharc_staff` and `iharc_admin` roles
- **Row Level Security**: Database-level access control
- **Secure API Keys**: Stored in Supabase Vault, not in client code
- **Audit Logging**: All actions tracked with user attribution

## Key Features

### Authentication & User Management
- Supabase-based authentication
- Role-based permissions
- User profile management with IHARC staff IDs
- Password reset and account management

### Real-time Capabilities
- Live weather updates (Google Weather API)
- Real-time data synchronization across multiple clients
- Automatic conflict resolution

### Offline Support
- Local data caching
- Offline form submission with sync queue
- Graceful degradation when connectivity lost

### Reporting & Analytics
- Activity reports and summaries
- Property tracking and recovery statistics
- User activity audit trails
- Export capabilities

### Integration Points
- **Google Maps API**: Location services and mapping
- **Google Weather API**: Current weather conditions
- **Supabase Vault**: Secure API key storage
- **Auto-updater**: Automatic application updates via GitHub releases

## Development Patterns

### Code Organization
- **Modular Architecture**: Separate concerns into focused modules
- **Event-driven**: UI interactions trigger commands and data operations
- **Promise-based**: Async/await patterns throughout
- **Error Handling**: Comprehensive error catching with user feedback

### UI Patterns
- **Tab-based Navigation**: Main application sections
- **Modal Dialogs**: Forms and confirmations
- **Toast Notifications**: User feedback and status updates
- **Responsive Design**: Optimized for 1024x768 Panasonic Toughbook screens

### Data Flow
1. User interaction → Command system
2. Command validation → Data layer
3. Database operation → Cache update
4. UI refresh → User feedback

## Deployment & Distribution

### Build Process
- Electron Builder for packaging
- GitHub Actions for CI/CD
- Azure Blob Storage for update distribution
- Automatic version management

### Update Mechanism
- Built-in auto-updater
- GitHub releases integration
- Secure update verification
- Rollback capabilities

### Target Environment
- Windows desktop application
- Panasonic Toughbook devices (1024x768 resolution)
- Network connectivity required for full functionality
- Offline capability for field operations

## Configuration Management

### Environment Variables
- Supabase connection details
- API keys (stored securely in Vault)
- Application settings and preferences

### User Settings
- Persistent user preferences
- Application state management
- Secure settings storage

## Performance Considerations

### Optimization Strategies
- Lazy loading of data
- Efficient caching mechanisms
- Minimal DOM manipulation
- Optimized database queries

### Scalability
- Multi-user concurrent access
- Real-time synchronization
- Efficient data pagination
- Resource management

## Security Considerations

### Data Protection
- Encrypted data transmission
- Secure local storage
- API key protection via Vault
- User session management

### Access Control
- Role-based permissions
- Database-level security (RLS)
- Audit trail maintenance
- Secure authentication flow

## Known Limitations & Technical Debt

### Current Constraints
- Single-window application design
- Limited mobile responsiveness
- Dependency on network connectivity for full features
- Manual user management (no self-registration)

### Areas for Improvement
- Enhanced offline capabilities
- Mobile companion app
- Advanced reporting dashboard
- Integration with external case management systems
- Automated backup and recovery
- Enhanced search and filtering capabilities

## Development Environment

### Setup Requirements
- Node.js 22+
- Electron development tools
- Supabase project access
- Google API credentials

### Testing Strategy
- Manual testing protocols
- Database schema validation
- Multi-client synchronization testing
- Offline/online transition testing

## Detailed Page Structure

### Dashboard Page
- **Active Incidents List**: Real-time incident tracking with status updates
- **Quick Action Buttons**: Fast access to common tasks (add person, log activity, etc.)
- **Weather Widget**: Current conditions for Cobourg, Ontario (default location)
- **Statistics Summary**: Key metrics and counts
- **Recent Activity Feed**: Latest system activities

### Records Management Pages

#### People Management
- **People List View**: Searchable table with person cards
- **Person Detail View**: Tabbed interface with:
  - Basic Information tab
  - Pets tab (one-to-many relationship)
  - Medical Issues tab
  - Activities History tab
- **Add/Edit Person Forms**: Dynamic form generation based on schema
- **Search Interface**: Global search across all person fields

#### Organizations Management
- **Organizations List**: Service provider directory
- **Organization Detail View**: Complete contact and service information
- **Service Tags System**: Categorization and filtering by service types
- **Partnership Management**: Track relationship types and referral processes

### Activity Management Pages

#### Activity Category Selection
- **Outreach Activities**: Supply distribution, wellness checks
- **Enforcement Activities**: Citations, property recovery
- **Medical Activities**: Health assessments, referrals
- **Administrative Activities**: Documentation, reporting

#### Activity Forms
- **Dynamic Form Generation**: Schema-driven form creation
- **Location Integration**: GPS coordinates and address lookup
- **File Attachments**: Photo and document support
- **Follow-up Tracking**: Automated reminder system

### Property Management Pages

#### Found Property Management
- **Property List View**: All recovered items with status tracking
- **Property Detail Forms**: Comprehensive item documentation
- **Status Workflow**: Found → Investigating → Returned/Police
- **Photo Documentation**: Base64 image storage
- **Owner Identification**: Contact and return tracking

#### Bike Registration System
- **Bike Registry**: Community bike registration program
- **Theft Reporting**: Stolen bike documentation
- **Recovery Tracking**: Found bike processing
- **Serial Number Database**: Searchable bike identification

### Dispatch Interface
- **Active Incidents Map**: Geographic incident visualization
- **Incident Assignment**: Ranger dispatch and tracking
- **Status Management**: Real-time incident status updates
- **Priority System**: High/Medium/Low incident prioritization
- **Communication Log**: Incident-related communications

## Form System Architecture

### Dynamic Form Generation
- **Schema-driven**: Forms automatically generated from database schema
- **Field Type Mapping**: Database types → HTML input types
- **Validation Rules**: Client-side and server-side validation
- **Conditional Fields**: Dynamic form behavior based on selections

### Form Field Types
- **Text/Textarea**: Standard text input
- **Select/Multi-select**: Dropdown and checkbox groups
- **Date/Time**: Date and time pickers
- **Boolean**: Yes/No selections
- **File Upload**: Image and document attachments
- **Location**: GPS coordinate capture

## Search and Filtering System

### Global Search
- **Cross-table Search**: Search across people, organizations, activities
- **Field-specific Filters**: Target specific data fields
- **Real-time Results**: Instant search as you type
- **Search History**: Recent search tracking

### Advanced Filtering
- **Date Range Filters**: Activity and creation date filtering
- **Status Filters**: Filter by record status or activity type
- **Geographic Filters**: Location-based filtering
- **Tag-based Filtering**: Service tags and category filtering

## Integration Details

### Google Services Integration
- **Maps API**: Location services, geocoding, mapping
- **Weather API**: Real-time weather data for field operations
- **API Key Management**: Secure key storage in Supabase Vault
- **Rate Limiting**: Efficient API usage management

### Supabase Integration
- **Real-time Subscriptions**: Live data updates across clients
- **Authentication**: User login and session management
- **Database Operations**: CRUD operations with RLS
- **File Storage**: Document and image storage (if implemented)

## Error Handling and Logging

### Error Management
- **Graceful Degradation**: Fallback to local storage when offline
- **User-friendly Messages**: Clear error communication
- **Retry Mechanisms**: Automatic retry for failed operations
- **Error Logging**: Comprehensive error tracking

### Audit Trail System
- **Activity Logging**: All user actions tracked
- **Data Change Tracking**: Before/after values for updates
- **User Attribution**: All changes linked to specific users
- **Timestamp Tracking**: Precise timing of all operations

## Performance Optimization

### Data Loading Strategies
- **Lazy Loading**: Load data as needed
- **Pagination**: Efficient large dataset handling
- **Caching Layers**: Memory, local storage, and database caching
- **Prefetching**: Anticipatory data loading

### UI Performance
- **Virtual Scrolling**: Efficient large list rendering
- **Debounced Search**: Optimized search input handling
- **Minimal DOM Updates**: Efficient UI refresh patterns
- **Resource Management**: Memory and CPU optimization

This comprehensive documentation provides all necessary context for understanding S.T.E.V.I Retro's complete functionality, architecture, and implementation details.
