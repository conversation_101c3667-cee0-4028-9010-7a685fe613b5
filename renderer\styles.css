/* Retro DOS Terminal Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    background: #000000;
    color: #ff0000;
    overflow: hidden;
    user-select: none;
    cursor: default;
}

#terminal-container {
    width: 100vw;
    height: 100vh;
    background: #000000;
    position: relative;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    background: #000000;
}

.screen.active {
    display: flex;
    flex-direction: column;
}

/* Boot Screen */
#boot-screen {
    justify-content: center;
    align-items: center;
}

.boot-content {
    text-align: center;
    max-width: 800px;
}

.iharc-branding {
    text-align: center;
    margin-bottom: 20px;
}

.iharc-logo {
    font-family: 'Courier New', monospace;
    font-size: 24px;
    line-height: 1;
    color: #ff4444;
    font-weight: bold;
    letter-spacing: 3px;
    margin-bottom: 10px;
}

.iharc-name {
    color: #ff0000;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 20px;
}

.ascii-art {
    font-size: 12px;
    line-height: 1;
    color: #ff0000;
    margin-bottom: 20px;
    white-space: pre;
    font-weight: bold;
}

.version-info {
    margin-bottom: 40px;
}

.version-info div {
    margin: 5px 0;
    color: #ff4444;
}

.boot-progress {
    margin-top: 40px;
}

/* Journey Animation */
.journey-animation {
    width: 400px;
    height: 140px;
    margin: 20px auto;
    position: relative;
    overflow: visible;
}

.journey-scene {
    width: 100%;
    height: 80px;
    position: relative;
    border: 2px solid #ff0000;
    background: linear-gradient(to bottom, #001122 0%, #000000 100%);
    overflow: hidden;
}

.scene-background {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: #333333;
    border-top: 1px solid #ff4444;
}

.person {
    position: absolute;
    bottom: 20px;
    left: 10px;
    transition: all 2s ease-in-out;
    z-index: 10;
}

/* Animated Stick Figure */
.stick-figure {
    position: relative;
    width: 30px;
    height: 40px;
}

.head {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    border: 2px solid #ff0000;
    border-radius: 50%;
    background: transparent;
}

.body {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 20px;
    background: #ff0000;
}

.arm {
    position: absolute;
    top: 15px;
    width: 2px;
    height: 12px;
    background: #ff0000;
    transform-origin: top center;
}

.arm-left {
    left: 10px;
    animation: arm-swing-left 0.8s infinite ease-in-out;
}

.arm-right {
    right: 10px;
    animation: arm-swing-right 0.8s infinite ease-in-out;
}

.leg {
    position: absolute;
    top: 28px;
    width: 2px;
    height: 15px;
    background: #ff0000;
    transform-origin: top center;
}

.leg-left {
    left: 12px;
    animation: leg-walk-left 0.8s infinite ease-in-out;
}

.leg-right {
    right: 12px;
    animation: leg-walk-right 0.8s infinite ease-in-out;
}

/* Walking Animations */
@keyframes arm-swing-left {
    0%, 100% { transform: rotate(-15deg); }
    50% { transform: rotate(15deg); }
}

@keyframes arm-swing-right {
    0%, 100% { transform: rotate(15deg); }
    50% { transform: rotate(-15deg); }
}

@keyframes leg-walk-left {
    0%, 100% { transform: rotate(-20deg); }
    50% { transform: rotate(20deg); }
}

@keyframes leg-walk-right {
    0%, 100% { transform: rotate(20deg); }
    50% { transform: rotate(-20deg); }
}

.shelter {
    position: absolute;
    bottom: 20px;
    font-size: 20px;
    transition: all 1s ease-in-out;
    opacity: 0;
}

.progress-path {
    position: absolute;
    bottom: 18px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, #ff0000 0%, transparent 0%);
    transition: background 0.5s ease;
}

.journey-text {
    text-align: center;
    color: #ff4444;
    font-size: 14px;
    margin-top: 15px;
    margin-bottom: 15px;
    min-height: 40px;
    line-height: 1.3;
    padding: 0 10px;
}

/* Animation States */
.journey-scene.stage-1 .person {
    left: 10px;
}

.journey-scene.stage-1 .shelter {
    left: 50px;
    opacity: 0;
}

.journey-scene.stage-2 .person {
    left: 25%;
}

.journey-scene.stage-2 .shelter {
    left: 30%;
    opacity: 1;
}

.journey-scene.stage-2 .shelter::before {
    content: "⛺";
}

.journey-scene.stage-3 .person {
    left: 50%;
}

.journey-scene.stage-3 .shelter {
    left: 55%;
    opacity: 1;
}

.journey-scene.stage-3 .shelter::before {
    content: "🏠";
}

.journey-scene.stage-4 .person {
    left: 75%;
}

.journey-scene.stage-4 .shelter {
    left: 80%;
    opacity: 1;
}

.journey-scene.stage-4 .shelter::before {
    content: "🏡";
}

.journey-scene.stage-5 .person {
    left: calc(100% - 50px);
}

.journey-scene.stage-5 .shelter {
    left: calc(100% - 45px);
    opacity: 1;
}

.journey-scene.stage-5 .shelter::before {
    content: "🏠";
}

/* Progress path animation */
.journey-scene.stage-1 .progress-path {
    background: linear-gradient(to right, #ff0000 20%, transparent 20%);
}

.journey-scene.stage-2 .progress-path {
    background: linear-gradient(to right, #ff0000 40%, transparent 40%);
}

.journey-scene.stage-3 .progress-path {
    background: linear-gradient(to right, #ff0000 60%, transparent 60%);
}

.journey-scene.stage-4 .progress-path {
    background: linear-gradient(to right, #ff0000 80%, transparent 80%);
}

.journey-scene.stage-5 .progress-path {
    background: linear-gradient(to right, #ff0000 100%, transparent 100%);
}

.progress-bar {
    width: 100%;
    height: 20px;
    border: 2px solid #ff0000;
    margin: 10px auto 15px;
    background: #000000;
}

.progress-fill {
    height: 100%;
    background: #ff0000;
    width: 0%;
    transition: width 0.3s ease;
}

.boot-messages {
    min-height: 60px;
    text-align: left;
    font-size: 14px;
}

.boot-messages div {
    margin: 2px 0;
    color: #ff4444;
}

/* Login Screen */
#login-screen {
    justify-content: center;
    align-items: center;
}

.login-content {
    text-align: center;
    max-width: 500px;
    border: 2px solid #ff0000;
    padding: 40px;
    background: #000000;
}

.login-header {
    margin-bottom: 30px;
    text-align: center;
}

.iharc-brand-login {
    font-size: 20px;
    font-weight: bold;
    color: #ff4444;
    margin-bottom: 5px;
    letter-spacing: 2px;
}

.system-name {
    font-size: 24px;
    font-weight: bold;
    color: #ff0000;
    margin-bottom: 10px;
}

.login-prompt {
    color: #ff4444;
    font-size: 14px;
}

.login-form {
    text-align: left;
}

.form-field {
    margin-bottom: 20px;
}

.form-field label {
    display: block;
    margin-bottom: 5px;
    color: #ff0000;
    font-weight: bold;
}

.form-field input,
.form-field select {
    width: 100%;
    max-width: 100%;
    padding: 8px;
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    font-family: inherit;
    font-size: 14px;
    box-sizing: border-box;
}

.form-field input:focus,
.form-field select:focus {
    outline: none;
    border-color: #ff4444;
    box-shadow: 0 0 5px #ff0000;
}

.form-field select option {
    background: #000000;
    color: #ff0000;
}

/* Checkbox Styling */
.form-field input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #ff0000;
    background: #000000;
    margin-right: 8px;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
}

.form-field input[type="checkbox"]:checked {
    background: #ff0000;
}

.form-field input[type="checkbox"]:checked::before {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 1px;
    color: #000000;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
}

.form-field input[type="checkbox"]:focus {
    outline: none;
    box-shadow: 0 0 5px #ff0000;
    border-color: #ff4444;
}

.form-field input[type="checkbox"]:hover {
    border-color: #ff4444;
}

/* Custom Date Input */
.custom-date-input {
    display: flex;
    align-items: center;
    gap: 2px;
    max-width: 120px;
}

.custom-date-input input[type="text"] {
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 2px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    text-align: center;
    width: auto !important;
    min-width: 0;
    max-width: none;
    box-sizing: border-box;
}

.custom-date-input .date-year {
    width: 38px;
}

.custom-date-input .date-month,
.custom-date-input .date-day {
    width: 24px;
}

.custom-date-input .date-separator {
    color: #ff0000;
    font-weight: bold;
    font-size: 12px;
    line-height: 1;
}

.custom-date-input input[type="text"]:focus {
    outline: none;
    border-color: #ff4444;
    box-shadow: 0 0 2px #ff0000;
}

.custom-date-input input[type="text"].invalid {
    border-color: #ff6666;
    background: #330000;
}

.custom-date-input.invalid {
    animation: shake 0.2s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-1px); }
    75% { transform: translateX(1px); }
}

.form-actions {
    text-align: center;
    margin-top: 30px;
}

.primary-button {
    background: #ff0000;
    color: #000000;
    border: none;
    padding: 10px 30px;
    font-family: inherit;
    font-weight: bold;
    cursor: pointer;
    font-size: 14px;
}

.primary-button:hover {
    background: #ff4444;
}

.primary-button:active {
    background: #cc0000;
}

.login-status {
    margin-top: 20px;
    min-height: 20px;
    color: #ff4444;
}

/* Main Interface */
#main-screen {
    flex-direction: column;
}

.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    border-bottom: 2px solid #ff0000;
    background: #000000;
}

.header-left .system-name {
    font-size: 18px;
    font-weight: bold;
    margin-right: 20px;
}

.user-info {
    color: #ff4444;
    font-size: 14px;
}

.datetime {
    color: #ff4444;
    font-size: 14px;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Tab Bar */
.tab-bar {
    display: flex;
    background: #000000;
    border-bottom: 1px solid #ff0000;
}

.tab {
    padding: 12px 24px;
    border-right: 1px solid #ff0000;
    cursor: pointer;
    color: #ff4444;
    font-weight: bold;
    transition: all 0.2s ease;
}

.tab:hover {
    background: #330000;
    color: #ff0000;
}

.tab.active {
    background: #ff0000;
    color: #000000;
}

.admin-tab {
    background: #330000;
    color: #ffaa00;
    font-weight: bold;
}

.admin-tab:hover {
    background: #440000;
    color: #ffcc00;
}

.admin-tab.active {
    background: #ffaa00;
    color: #000000;
}

.logout-tab {
    margin-left: auto;
    border-left: 1px solid #ff0000;
    border-right: none;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 20px;
    background: #000000;
    overflow-y: auto;
    height: calc(100vh - 120px);
    max-height: calc(100vh - 120px);
    position: relative;
    box-sizing: border-box;
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
    border-top: 2px solid #ff0000;
    background: #000000;
    font-size: 12px;
}

.status-left {
    color: #ff0000;
    flex: 1;
}

.iharc-brand {
    color: #ff0000;
    font-weight: bold;
    font-size: 13px;
    letter-spacing: 1px;
}

.status-separator {
    margin: 0 8px;
    color: #ff0000;
}

.status-center {
    flex: 0 0 auto;
    display: flex;
    justify-content: center;
}

.status-right {
    color: #ff4444;
    flex: 1;
    text-align: right;
}

/* Network Status */
.network-status {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #ff0000;
    font-weight: bold;
    font-size: 11px;
}

.network-indicator {
    font-size: 14px;
    line-height: 1;
}

.network-status.online .network-indicator {
    color: #ff0000;
    text-shadow: 0 0 3px #ff0000;
}

.network-status.offline .network-indicator {
    color: #666666;
}

.network-status.offline #network-text {
    color: #666666;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #000000;
    border: 1px solid #ff0000;
}

::-webkit-scrollbar-thumb {
    background: #ff0000;
}

::-webkit-scrollbar-thumb:hover {
    background: #ff4444;
}

/* Selection */
::selection {
    background: #ff0000;
    color: #000000;
}

/* Animations */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.cursor {
    animation: blink 1s infinite;
}

/* Modal Dialogs */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-dialog {
    background: #000000;
    border: 2px solid #ff0000;
    min-width: 400px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.form-modal {
    min-width: 500px;
}

.search-modal {
    min-width: 700px;
    max-width: 900px;
    max-height: 80vh;
}

/* Search Interface */
.search-form {
    margin-bottom: 20px;
}

.search-results {
    border-top: 1px solid #ff0000;
    padding-top: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.search-results h4 {
    color: #ff0000;
    margin-bottom: 15px;
    font-size: 16px;
}

.results-section {
    margin-bottom: 20px;
}

.results-section h5 {
    color: #ff4444;
    margin-bottom: 10px;
    font-size: 14px;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.record-item {
    background: #111111;
    border: 1px solid #ff0000;
    padding: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.record-item:hover {
    background: #220000;
}

.record-header {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 5px;
}

.record-details {
    color: #ff4444;
    font-size: 12px;
    margin-bottom: 5px;
}

.record-details span {
    display: inline-block;
    margin-right: 15px;
}

.record-meta {
    color: #666666;
    font-size: 11px;
    font-style: italic;
}

.no-results {
    text-align: center;
    color: #ff4444;
    padding: 40px;
    font-style: italic;
}

/* Record Detail View */
.record-detail-modal {
    min-width: 900px;
    max-width: 1200px;
    max-height: 90vh;
}

.record-view-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    grid-template-rows: auto 1fr;
    gap: 20px;
    min-height: 500px;
}

/* Profile Section */
.profile-section {
    grid-row: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: #111111;
    border: 2px solid #ff0000;
}

.profile-picture {
    text-align: center;
    margin-bottom: 20px;
}

.ascii-avatar {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: #ff0000;
    line-height: 1.1;
    white-space: pre;
    background: #000000;
    padding: 8px;
    border: 2px solid #ff0000;
    margin-bottom: 10px;
    text-align: center;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

.profile-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    word-wrap: break-word;
}

.record-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

.record-actions button {
    width: 100%;
    padding: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

/* Details and Activities Sections */
.details-section,
.activities-section {
    background: #111111;
    border: 1px solid #ff0000;
    padding: 20px;
}

.details-section h4,
.activities-section h4 {
    color: #ff0000;
    margin: 0 0 15px 0;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
}

/* Record Details */
.record-details-view {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.detail-row {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333333;
    min-height: 30px;
}

.detail-label {
    color: #ff4444;
    font-weight: bold;
    min-width: 140px;
    margin-right: 10px;
}

.detail-value {
    color: #ff0000;
    flex: 1;
}

.detail-value em {
    color: #666666;
    font-style: italic;
}

/* Inline Edit Controls */
.edit-controls {
    display: none;
    align-items: center;
    gap: 5px;
    flex: 1;
}

.edit-input {
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    font-family: inherit;
    font-size: 13px;
    flex: 1;
    max-width: 200px;
}

.edit-input:focus {
    outline: none;
    border-color: #ff4444;
    box-shadow: 0 0 3px #ff0000;
}

.edit-input[type="checkbox"] {
    width: 16px;
    height: 16px;
    flex: none;
}

.edit-input textarea {
    resize: vertical;
    min-height: 40px;
}

.save-field-btn,
.cancel-field-btn {
    background: none;
    border: 1px solid #ff0000;
    color: #ff0000;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: none;
}

.save-field-btn:hover {
    background: #00ff00;
    color: #000000;
    border-color: #00ff00;
}

.cancel-field-btn:hover {
    background: #ff0000;
    color: #000000;
}

/* Activities */
.activities-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.activities-header h4 {
    color: #ff0000;
    margin: 0;
}

.activities-list {
    max-height: 500px;
    overflow-y: auto;
}

.activity-item {
    background: #111111;
    border: 1px solid #ff0000;
    margin-bottom: 15px;
    padding: 15px;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.activity-title {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
}

.activity-meta {
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 12px;
}

.activity-type {
    background: #ff0000;
    color: #000000;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
}

.priority {
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
}

.priority-low { background: #666666; color: #ffffff; }
.priority-medium { background: #ffaa00; color: #000000; }
.priority-high { background: #ff6600; color: #ffffff; }
.priority-urgent { background: #ff0000; color: #ffffff; }

.activity-date {
    color: #ff4444;
}

.activity-content {
    color: #ff4444;
    margin-bottom: 10px;
    line-height: 1.4;
}

.activity-content p {
    margin: 0 0 10px 0;
}

.activity-location,
.activity-outcome,
.activity-findings,
.activity-action {
    margin: 5px 0;
    font-size: 13px;
}

.activity-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666666;
    border-top: 1px solid #333333;
    padding-top: 8px;
}

.follow-up-required {
    color: #ffaa00;
    font-weight: bold;
}

.no-activities {
    text-align: center;
    color: #ff4444;
    padding: 40px;
    font-style: italic;
}

.close-button {
    background: none;
    border: none;
    color: #ff0000;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    color: #ff4444;
}

/* Dashboard Styles - New Responsive Layout */
.dashboard-container {
    padding: 15px;
    max-width: 100%;
    margin: 0 auto;
    height: calc(100vh - 180px);
    overflow-y: auto;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: 15px;
    height: 100%;
}

/* Sidebar styling */
.dashboard-sidebar {
    background: #111111;
    border: 2px solid #ff0000;
    display: flex;
    flex-direction: column;
    width: 120px;
    min-width: 120px;
    height: 100%;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    border-bottom: 1px solid #ff4444;
}

.sidebar-header h3 {
    color: #ff0000;
    font-size: clamp(14px, 2vw, 16px);
    margin: 0;
    font-weight: bold;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 5px 5px 5px;
}

/* Main content area containing incidents and bottom sections */
.main-content-area {
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: 100%;
}

/* Bottom sections container for weather and stats */
.bottom-sections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.dashboard-section {
    background: #111111;
    border: 2px solid #ff0000;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ff4444;
}

.section-header h3 {
    color: #ff0000;
    font-size: clamp(14px, 2vw, 18px);
    margin: 0;
    font-weight: bold;
}

.section-content {
    flex: 1;
    overflow-y: auto;
}

.section-footer {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #ff4444;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .dashboard-container {
        padding: 10px;
        height: calc(100vh - 160px);
    }

    .dashboard-content {
        grid-template-columns: 100px 1fr;
        gap: 10px;
    }

    .main-content-area {
        gap: 10px;
    }

    .bottom-sections {
        gap: 10px;
    }

    .dashboard-section {
        padding: 10px;
    }

    .section-header h3 {
        font-size: clamp(12px, 1.8vw, 16px);
    }

    /* Sidebar adjustments */
    .dashboard-sidebar {
        width: 100px;
        min-width: 100px;
    }

    .sidebar-header {
        padding: 8px;
    }

    .sidebar-header h3 {
        font-size: 12px;
    }

    .action-btn {
        min-height: 45px;
        padding: 5px 3px;
        font-size: 8px;
    }

    .action-icon {
        font-size: 12px;
    }

    .action-text {
        font-size: 7px;
    }
}

@media (max-width: 768px) {
    .dashboard-content {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    /* Convert to single column layout on small screens */
    .main-content-area {
        order: 2;
        gap: 10px;
    }

    .dashboard-sidebar {
        order: 1;
        width: 100%;
        min-width: 100%;
        height: auto;
        flex-direction: column;
    }

    .bottom-sections {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    /* Make quick actions horizontal on small screens */
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
        gap: 6px;
    }

    .action-btn {
        min-height: 45px;
        padding: 4px 2px;
    }

    .weather-section,
    .stats-section {
        min-height: 150px;
    }
}

/* Weather Section Specific */
.weather-section {
    overflow: hidden;
    min-height: 180px;
}

/* Incidents Section - Main focus */
.incidents-section {
    flex: 1;
    min-height: 300px;
    display: flex;
    flex-direction: column;
}

.incidents-section .section-content {
    flex: 1;
    overflow-y: auto;
}

/* Weather and Stats sections */
.weather-section,
.stats-section {
    min-height: 180px;
}

/* Incidents Section Specific - MAIN FOCUS taking bulk of screen */
.incidents-section {
    min-height: 300px;
}

/* Stats Section Specific */
.stats-section {
    overflow: hidden;
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ff4444;
    flex-shrink: 0;
}

.widget-header h3 {
    color: #ff0000;
    font-size: 16px;
    margin: 0;
}

.task-count,
.incident-count {
    background: #ff0000;
    color: #000000;
    padding: 3px 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 12px;
}

.widget-content {
    color: #ff4444;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Special handling for weather widget to prevent scrolling */
.weather-widget .widget-content {
    overflow: hidden;
}

.loading {
    text-align: center;
    color: #ff4444;
    font-style: italic;
    padding: 20px;
}

.error {
    text-align: center;
    color: #ff6666;
    font-style: italic;
    padding: 20px;
}

/* Weather Widget - Responsive Design */
.weather-display {
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    width: 100%;
}

.weather-main {
    margin-bottom: 0.4rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
}

.weather-icon {
    font-size: clamp(16px, 2.5vw, 24px);
    margin-bottom: 0.1rem;
}

.weather-temp {
    font-size: clamp(20px, 3.5vw, 32px);
    font-weight: bold;
    color: #ff0000;
    display: block;
    line-height: 1;
}

.weather-condition {
    font-size: clamp(10px, 1.5vw, 16px);
    color: #ff4444;
    line-height: 1;
}

.weather-location {
    font-size: clamp(8px, 1.2vw, 14px);
    color: #ff6666;
    margin-bottom: 0.4rem;
}

.weather-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.2rem;
    margin-bottom: 0.4rem;
    flex-shrink: 0;
    overflow: hidden;
}

.weather-detail {
    text-align: center;
}

.detail-label {
    display: block;
    font-size: clamp(7px, 1vw, 12px);
    color: #ff4444;
    line-height: 1;
}

.detail-value {
    display: block;
    font-size: clamp(9px, 1.3vw, 14px);
    font-weight: bold;
    color: #ff0000;
    line-height: 1;
}

.weather-alert {
    background: #330000;
    border: 1px solid #ff6666;
    padding: 0.3rem 0.4rem;
    font-size: clamp(7px, 1vw, 12px);
    color: #ff6666;
    text-align: left;
    line-height: 1.1;
    margin-top: 0.3rem;
    flex-shrink: 0;
}

.weather-alert.alert-severe {
    background: #440000;
    border-color: #ff3333;
    color: #ff3333;
}

.weather-alert.alert-moderate {
    background: #332200;
    border-color: #ffaa00;
    color: #ffaa00;
}

.weather-alert.alert-low {
    background: #003322;
    border-color: #00aa66;
    color: #00aa66;
}

.weather-footer {
    margin-top: 0.3rem;
    padding-top: 0.2rem;
    border-top: 1px solid #444444;
    flex-shrink: 0;
}

.weather-footer small {
    color: #888888;
    font-size: clamp(6px, 0.8vw, 10px);
    line-height: 1;
}

/* Weather Widget Media Queries for Better Responsiveness */
@media (max-width: 768px) {
    .weather-temp {
        font-size: clamp(18px, 4vw, 28px);
    }

    .weather-condition {
        font-size: clamp(9px, 2vw, 14px);
    }

    .detail-label {
        font-size: clamp(6px, 1.2vw, 10px);
    }

    .detail-value {
        font-size: clamp(8px, 1.5vw, 12px);
    }
}

@media (min-width: 1200px) {
    .weather-temp {
        font-size: clamp(24px, 2.5vw, 36px);
    }

    .weather-condition {
        font-size: clamp(12px, 1.2vw, 18px);
    }

    .weather-icon {
        font-size: clamp(20px, 2vw, 28px);
    }

    .detail-label {
        font-size: clamp(8px, 0.8vw, 14px);
    }

    .detail-value {
        font-size: clamp(10px, 1vw, 16px);
    }

    .weather-alert {
        font-size: clamp(8px, 0.8vw, 14px);
    }
}

/* Task Queue Widget */
.task-item {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 8px;
    margin-bottom: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-item:hover {
    border-color: #ff0000;
    background: #220000;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3px;
}

.task-number {
    font-weight: bold;
    color: #ff0000;
    font-size: 12px;
}

.task-priority {
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 9px;
    font-weight: bold;
}

.priority-low { background: #666666; color: #ffffff; }
.priority-medium { background: #ffaa00; color: #000000; }
.priority-high { background: #ff6600; color: #ffffff; }
.priority-urgent { background: #ff0000; color: #ffffff; }

.task-description {
    color: #ff4444;
    font-size: 12px;
    margin-bottom: 3px;
    line-height: 1.2;
}

.task-location,
.task-time {
    font-size: 10px;
    color: #ff6666;
}

.no-tasks,
.no-incidents,
.no-activities {
    text-align: center;
    color: #ff4444;
    font-style: italic;
    padding: 20px;
}

/* Incidents Widget */
.incident-item {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 10px;
    margin-bottom: 10px;
}

.incident-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.incident-number {
    font-weight: bold;
    color: #ff0000;
}

.incident-status {
    background: #ff4444;
    color: #000000;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
}

.incident-description {
    color: #ff4444;
    font-size: 14px;
    margin-bottom: 5px;
}

.incident-location {
    font-size: 12px;
    color: #ff6666;
}

/* Activities Widget */
.activity-item {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 8px;
    margin-bottom: 8px;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3px;
}

.activity-type {
    background: #ff0000;
    color: #000000;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
}

.activity-date {
    font-size: 11px;
    color: #ff6666;
}

.activity-title {
    color: #ff4444;
    font-size: 13px;
    margin-bottom: 3px;
}

.activity-staff {
    font-size: 11px;
    color: #ff6666;
}

/* Stats Widget */
.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333333;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #ff4444;
    font-size: 14px;
}

.stat-value {
    color: #ff0000;
    font-size: 18px;
    font-weight: bold;
}

/* Quick Actions Widget - Vertical Sidebar Layout */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 0;
}

.action-btn {
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 6px 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
    font-family: inherit;
    font-size: 9px;
    min-height: 50px;
    justify-content: center;
    width: 100%;
    margin-bottom: 4px;
}

.action-btn:hover {
    background: #ff0000;
    color: #000000;
}

.action-icon {
    font-size: 14px;
}

.action-text {
    font-size: 8px;
    font-weight: bold;
    text-align: center;
    line-height: 1.1;
}

/* User Profile Modal */
.user-profile-modal {
    min-width: 600px;
    max-width: 800px;
    max-height: 90vh;
}

.profile-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.profile-info-section,
.security-section,
.session-section {
    background: #111111;
    border: 1px solid #ff0000;
    padding: 20px;
}

.profile-info-section h4,
.security-section h4,
.session-section h4 {
    color: #ff0000;
    margin: 0 0 15px 0;
    font-size: 18px;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 8px;
}

.profile-details,
.session-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.profile-field,
.session-field {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333333;
}

.profile-field:last-child,
.session-field:last-child {
    border-bottom: none;
}

.profile-field label,
.session-field label {
    color: #ff4444;
    font-weight: bold;
    min-width: 140px;
    margin-right: 15px;
}

.field-value {
    color: #ff0000;
    flex: 1;
    margin-right: 10px;
}

.status-active {
    color: #00ff00;
    font-weight: bold;
}

.edit-field-btn {
    background: none;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.edit-field-btn:hover {
    background: #ff0000;
    color: #000000;
}

.field-note {
    color: #888888;
    font-size: 12px;
    font-style: italic;
    margin-left: 10px;
}

.security-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.action-button {
    background: #000000;
    border: 2px solid #ff0000;
    color: #ff0000;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: inherit;
    font-size: 14px;
}

.action-button:hover {
    background: #ff0000;
    color: #000000;
}

.action-icon {
    font-size: 16px;
}

.action-text {
    font-weight: bold;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ff0000;
}

.modal-header h3 {
    color: #ff0000;
    font-size: 18px;
    margin: 0;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #ff0000;
    text-align: right;
}

.secondary-button {
    background: #000000;
    color: #ff0000;
    border: 1px solid #ff0000;
    padding: 8px 20px;
    font-family: inherit;
    font-weight: bold;
    cursor: pointer;
    margin-right: 10px;
}

.secondary-button:hover {
    background: #330000;
}

/* Form Styling */
.form-field textarea {
    width: 100%;
    min-height: 80px;
    padding: 8px;
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
}

.form-field textarea:focus {
    outline: none;
    border-color: #ff4444;
    box-shadow: 0 0 5px #ff0000;
}

/* Status Classes */
.status-error {
    color: #ff4444 !important;
}

.status-warning {
    color: #ff6666 !important;
}

.status-success {
    color: #ff0000 !important;
}

.login-status.error {
    color: #ff4444;
}

/* Retro Effects */
.scanlines {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background: linear-gradient(transparent 50%, rgba(255, 0, 0, 0.03) 50%);
    background-size: 100% 4px;
    z-index: 999;
}

.crt-effect {
    filter: contrast(1.1) brightness(1.1);
}

.crt-effect::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, transparent 70%, rgba(0, 0, 0, 0.3) 100%);
    pointer-events: none;
    z-index: 998;
}

/* Admin Interface Styles */
.admin-container {
    padding: 20px;
    color: #ff0000;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ff0000;
}

.admin-header h2 {
    color: #ffaa00;
    margin: 0;
    font-size: 24px;
}

.admin-user-info {
    color: #ff4444;
    font-size: 14px;
}

.admin-sections {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.admin-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid #ff0000;
    padding-bottom: 10px;
}

.admin-nav-btn {
    padding: 10px 20px;
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    cursor: pointer;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    transition: all 0.2s ease;
}

.admin-nav-btn:hover {
    background: #330000;
    color: #ffaa00;
}

.admin-nav-btn.active {
    background: #ff0000;
    color: #000000;
}

.admin-section {
    border: 1px solid #ff0000;
    padding: 20px;
    background: #111111;
}

.admin-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.admin-section .section-header h3 {
    color: #ff0000;
    margin: 0;
    font-size: 18px;
}

.item-controls {
    margin-bottom: 20px;
}

.search-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input, .filter-select {
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 8px 12px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.search-input {
    flex: 1;
    min-width: 200px;
}

.filter-select {
    min-width: 120px;
}

.search-input:focus, .filter-select:focus {
    outline: none;
    border-color: #ffaa00;
    background: #111111;
}

.items-list {
    border: 1px solid #ff4444;
    background: #000000;
    min-height: 400px;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
}

.items-table th,
.items-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ff4444;
    color: #ff0000;
    font-family: 'Courier New', monospace;
}

.items-table th {
    background: #330000;
    color: #ffaa00;
    font-weight: bold;
    position: sticky;
    top: 0;
}

.items-table tr:hover {
    background: #111111;
}

.item-actions {
    display: flex;
    gap: 10px;
}

.item-actions button {
    padding: 4px 8px;
    font-size: 12px;
    border: 1px solid #ff0000;
    background: #000000;
    color: #ff0000;
    cursor: pointer;
    font-family: 'Courier New', monospace;
}

.item-actions button:hover {
    background: #ff0000;
    color: #000000;
}

.item-actions .edit-btn {
    border-color: #ffaa00;
    color: #ffaa00;
}

.item-actions .edit-btn:hover {
    background: #ffaa00;
    color: #000000;
}

.item-actions .delete-btn {
    border-color: #ff4444;
    color: #ff4444;
}

.item-actions .delete-btn:hover {
    background: #ff4444;
    color: #000000;
}

.stock-low {
    color: #ff4444 !important;
    font-weight: bold;
}

.stock-ok {
    color: #00ff00;
}

.status-active {
    color: #00ff00;
}

.status-inactive {
    color: #ff4444;
}

/* User Management Styles */
.user-controls {
    margin-bottom: 20px;
}

.users-list {
    border: 1px solid #ff4444;
    background: #000000;
    min-height: 400px;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ff4444;
    color: #ff0000;
    font-family: 'Courier New', monospace;
}

.users-table th {
    background: #330000;
    color: #ffaa00;
    font-weight: bold;
    position: sticky;
    top: 0;
}

.users-table tr:hover {
    background: #111111;
}

.user-actions {
    display: flex;
    gap: 10px;
}

.user-actions button {
    padding: 4px 8px;
    font-size: 12px;
    border: 1px solid #ff0000;
    background: #000000;
    color: #ff0000;
    cursor: pointer;
    font-family: 'Courier New', monospace;
}

.user-actions button:hover {
    background: #ff0000;
    color: #000000;
}

.role-staff {
    color: #00ff00;
}

.role-admin {
    color: #ffaa00;
    font-weight: bold;
}

.user-actions .promote-btn {
    border-color: #00ff00;
    color: #00ff00;
}

.user-actions .promote-btn:hover {
    background: #00ff00;
    color: #000000;
}

.user-actions .demote-btn {
    border-color: #ffaa00;
    color: #ffaa00;
}

.user-actions .demote-btn:hover {
    background: #ffaa00;
    color: #000000;
}

.user-actions .reset-btn {
    border-color: #0088ff;
    color: #0088ff;
}

.user-actions .reset-btn:hover {
    background: #0088ff;
    color: #000000;
}

/* Responsive */
@media (max-width: 768px) {
    .ascii-art {
        font-size: 8px;
    }

    .tab {
        padding: 8px 12px;
        font-size: 12px;
    }

    .main-header {
        padding: 8px 12px;
    }

    .content-area {
        padding: 12px;
    }

    .modal-dialog {
        min-width: 300px;
        margin: 20px;
    }

    .form-modal {
        min-width: 350px;
    }

    .status-bar {
        padding: 6px 12px;
        font-size: 10px;
    }

    .network-status {
        font-size: 10px;
    }

    .network-indicator {
        font-size: 12px;
    }
}

/* Update Dialog Styles */
.update-notification .modal-dialog {
    max-width: 600px;
}

.update-details {
    background: #111111;
    border: 1px solid #ff0000;
    padding: 15px;
    margin: 15px 0;
    border-radius: 3px;
}

.update-details p {
    margin: 5px 0;
    font-size: 14px;
}

.release-notes {
    margin: 15px 0;
}

.release-notes h4 {
    color: #ff0000;
    margin-bottom: 10px;
    font-size: 16px;
}

.release-notes-content {
    background: #111111;
    border: 1px solid #333333;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    font-size: 13px;
    line-height: 1.4;
    border-radius: 3px;
}

.release-notes-content code {
    background: #222222;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: 'JetBrains Mono', monospace;
}

.progress-container {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #111111;
    border: 1px solid #ff0000;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff0000, #cc0000);
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-weight: bold;
    color: #ff0000;
}

.download-details {
    text-align: center;
    margin-top: 10px;
    font-size: 12px;
    color: #cccccc;
}

.update-error .modal-dialog {
    border-color: #ff4444;
}

.update-error .modal-header {
    background: #330000;
    border-bottom-color: #ff4444;
}

.no-update .modal-dialog {
    border-color: #00ff00;
}

.no-update .modal-header {
    background: #003300;
    border-bottom-color: #00ff00;
}

.install-confirmation .modal-dialog {
    border-color: #ffff00;
}

.install-confirmation .modal-header {
    background: #333300;
    border-bottom-color: #ffff00;
}

/* Development Mode Dialog */
.development-mode .modal-dialog {
    border-color: #ff8800;
}

.development-mode .modal-header {
    background: #332200;
    border-bottom-color: #ff8800;
}

.dev-info {
    background: #111111;
    border: 1px solid #ff8800;
    padding: 15px;
    margin: 15px 0;
    border-radius: 3px;
}

.dev-info ul {
    margin: 10px 0 0 20px;
    color: #cccccc;
}

.dev-info li {
    margin: 5px 0;
}

/* Force Enable Dialog */
.force-enable .modal-dialog {
    border-color: #ff4444;
}

.force-enable .modal-header {
    background: #330000;
    border-bottom-color: #ff4444;
}

.warning-box {
    background: #331100;
    border: 2px solid #ff8800;
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}

.warning-box p {
    margin: 5px 0;
    color: #ffaa00;
}

.danger-button {
    background: #ff4444;
    color: #ffffff;
    border: 1px solid #ff6666;
    padding: 10px 20px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 14px;
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.danger-button:hover {
    background: #ff6666;
    border-color: #ff8888;
}

/* Release Notes Dialog */
.release-notes-dialog .modal-dialog {
    border-color: #00aaff;
}

.release-notes-dialog .modal-header {
    background: #002244;
    border-bottom-color: #00aaff;
}

.release-info {
    background: #111111;
    border: 1px solid #00aaff;
    padding: 15px;
    margin: 15px 0;
    border-radius: 3px;
}

.release-info p {
    margin: 5px 0;
    font-size: 14px;
}

/* Success Dialog */
.success-dialog .modal-dialog {
    border-color: #00ff00;
}

.success-dialog .modal-header {
    background: #003300;
    border-bottom-color: #00ff00;
}

/* Search Incidents Styles */
.large-modal .modal-dialog {
    max-width: 900px;
    width: 90%;
}

.search-form {
    background: rgba(0, 255, 0, 0.05);
    border: 1px solid #00ff00;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    color: #00ff00;
    font-weight: bold;
    margin-bottom: 5px;
    font-size: clamp(10px, 1.2vw, 14px);
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #00ff00;
    border-radius: 4px;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: clamp(10px, 1.1vw, 13px);
}

.form-control:focus {
    outline: none;
    border-color: #00ff88;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-start;
    margin-top: 20px;
}

.search-results {
    margin-top: 20px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #00ff00;
}

.results-header h4 {
    color: #00ff00;
    margin: 0;
    font-size: clamp(14px, 1.5vw, 18px);
}

.results-count {
    color: #00ff88;
    font-size: clamp(10px, 1.1vw, 13px);
    font-weight: bold;
}

.results-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #00ff00;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
}

.incident-result-item {
    border-bottom: 1px solid rgba(0, 255, 0, 0.3);
    padding: 15px;
    transition: background-color 0.2s;
}

.incident-result-item:hover {
    background: rgba(0, 255, 0, 0.1);
}

.incident-result-item:last-child {
    border-bottom: none;
}

.incident-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.incident-number {
    font-weight: bold;
    color: #00ff88;
    font-size: clamp(12px, 1.3vw, 16px);
}

.incident-badges {
    display: flex;
    gap: 8px;
}

.status-badge, .priority-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: clamp(8px, 1vw, 11px);
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.status-open {
    background: rgba(255, 165, 0, 0.2);
    color: #ffaa00;
    border: 1px solid #ffaa00;
}

.status-badge.status-progress {
    background: rgba(0, 123, 255, 0.2);
    color: #007bff;
    border: 1px solid #007bff;
}

.status-badge.status-resolved {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid #28a745;
}

.status-badge.status-closed {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid #6c757d;
}

.priority-badge.priority-low {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid #28a745;
}

.priority-badge.priority-medium {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid #ffc107;
}

.priority-badge.priority-high {
    background: rgba(255, 102, 0, 0.2);
    color: #ff6600;
    border: 1px solid #ff6600;
}

.priority-badge.priority-urgent {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid #dc3545;
}

.incident-result-content {
    margin-bottom: 15px;
}

.incident-description {
    color: #00ff00;
    margin-bottom: 10px;
    font-size: clamp(10px, 1.1vw, 13px);
}

.incident-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 8px;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: clamp(9px, 1vw, 12px);
}

.detail-label {
    color: #00ff88;
    font-weight: bold;
    min-width: 80px;
    flex-shrink: 0;
}

.detail-value {
    color: #00ff00;
    word-break: break-word;
}

.incident-result-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-button {
    padding: 6px 12px;
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid #00ff00;
    border-radius: 4px;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: clamp(8px, 1vw, 11px);
    cursor: pointer;
    transition: all 0.2s;
}

.action-button:hover {
    background: rgba(0, 255, 0, 0.2);
    border-color: #00ff88;
}

.action-button:active {
    transform: translateY(1px);
}

.no-results {
    text-align: center;
    color: #00ff88;
    padding: 40px;
    font-style: italic;
    font-size: clamp(11px, 1.2vw, 14px);
}

.incident-details-view {
    color: #00ff00;
    font-family: 'Courier New', monospace;
}

.incident-details-view h4 {
    color: #00ff88;
    margin-bottom: 15px;
    font-size: clamp(14px, 1.5vw, 18px);
}

.details-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.detail-row {
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 255, 0, 0.2);
    font-size: clamp(10px, 1.1vw, 13px);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row strong {
    color: #00ff88;
}

/* Dispatch Screen Styles */
.dispatch-container {
    padding: 15px;
    height: calc(100vh - 180px);
    overflow: hidden;
}

.dispatch-grid {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 15px;
    height: 100%;
}

.dispatch-incident-list,
.dispatch-incident-detail {
    background: #111111;
    border: 2px solid #ff0000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.dispatch-header {
    padding: 10px 15px;
    border-bottom: 1px solid #ff0000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #000000;
}

.dispatch-header h3 {
    color: #ff0000;
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.dispatch-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.incident-count {
    background: #ff0000;
    color: #000000;
    padding: 2px 8px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 12px;
}

.refresh-btn {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    font-family: monospace;
    font-size: 14px;
}

.refresh-btn:hover {
    background: #ff0000;
    color: #000000;
}

.shortcut-hint {
    color: #ff4444;
    font-size: 11px;
    font-family: monospace;
}

.incident-list-container,
.incident-detail-container {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.no-selection {
    text-align: center;
    color: #ff4444;
    padding: 50px 20px;
    font-style: italic;
}

/* Incident List Headers */
.incident-list-header {
    border-bottom: 2px solid #ff0000;
    margin-bottom: 5px;
    padding-bottom: 5px;
}

.header-row {
    font-weight: bold;
    color: #ff0000 !important;
    background: rgba(255, 0, 0, 0.1);
    padding: 5px 8px;
    border: 1px solid #ff0000;
}

.header-row > div {
    color: #ff0000 !important;
    font-size: 11px;
    text-align: center;
}

.incident-list-body {
    max-height: calc(100% - 40px);
    overflow-y: auto;
}

/* Incident List Items */
.dispatch-incident-item {
    border: 1px solid #ff4444;
    margin-bottom: 2px;
    padding: 3px 5px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 11px;
}

.dispatch-incident-item:hover {
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.05);
}

.dispatch-incident-item.selected {
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
}

.dispatch-incident-item.new {
    animation: flashNew 2s ease-in-out;
}

@keyframes flashNew {
    0%, 100% { background: rgba(255, 0, 0, 0.1); }
    50% { background: rgba(255, 0, 0, 0.3); }
}

.incident-row {
    display: grid;
    grid-template-columns: 140px 30px 70px 45px;
    gap: 8px;
    align-items: center;
    font-size: 11px;
    font-family: monospace;
}

.incident-call-number {
    color: #ff0000;
    font-weight: bold;
}

.incident-type {
    color: #ffffff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.incident-priority {
    text-align: center;
    font-weight: bold;
}

.incident-priority.high {
    color: #ff0000;
}

.incident-priority.medium {
    color: #ffaa00;
}

.incident-priority.low {
    color: #00ff88;
}

.incident-status {
    color: #ff4444;
    font-size: 11px;
}

.incident-time {
    color: #ff4444;
    font-size: 11px;
    text-align: right;
}

/* Incident Detail Tabs */
.incident-detail-tabs {
    display: flex;
    border-bottom: 1px solid #ff0000;
    margin-bottom: 15px;
}

.incident-detail-tab {
    padding: 8px 16px;
    border-right: 1px solid #ff0000;
    cursor: pointer;
    color: #ff4444;
    font-size: 12px;
    font-weight: bold;
    background: transparent;
    border-top: none;
    border-left: none;
    border-bottom: none;
}

.incident-detail-tab:last-child {
    border-right: none;
}

.incident-detail-tab:hover {
    background: rgba(255, 0, 0, 0.1);
    color: #ff0000;
}

.incident-detail-tab.active {
    background: #ff0000;
    color: #000000;
}

.incident-detail-content {
    flex: 1;
    overflow-y: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Overview Tab */
.incident-overview {
    color: #ffffff;
    line-height: 1.6;
}

.incident-overview .detail-row {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
}

.incident-overview .detail-label {
    color: #ff0000;
    font-weight: bold;
    min-width: 100px;
    margin-right: 10px;
}

.incident-overview .detail-value {
    color: #ffffff;
    flex: 1;
}

/* Map Tab */
.incident-map-container {
    height: 400px;
    border: 1px solid #ff0000;
    position: relative;
}

.map-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff4444;
    font-style: italic;
}

.map-error {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff0000;
    font-weight: bold;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff0000;
}

.map-controls {
    margin-top: 10px;
    text-align: center;
}

.directions-btn {
    background: #ff0000;
    color: #000000;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
    font-family: monospace;
    font-weight: bold;
}

.directions-btn:hover {
    background: #ff4444;
}

/* Quick Actions */
.dispatch-quick-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ff0000;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-action-btn {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 11px;
    font-weight: bold;
    flex: 1;
    min-width: 80px;
}

.quick-action-btn:hover {
    background: #ff0000;
    color: #000000;
}

.quick-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quick-action-btn:disabled:hover {
    background: transparent;
    color: #ff0000;
}

.shortcut-key {
    background: rgba(255, 0, 0, 0.2);
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: bold;
    margin-right: 5px;
    font-size: 10px;
}

/* Toast Notifications */
.dispatch-toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #ff0000;
    color: #000000;
    padding: 10px 15px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
}

.dispatch-toast.success {
    background: #00ff88;
}

.dispatch-toast.warning {
    background: #ffaa00;
}

.dispatch-toast.error {
    background: #ff0000;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design for Dispatch - Optimized for 1024x768 */
@media (max-width: 1024px) {
    /* Keep side-by-side layout for 1024x768 - email inbox style */
    .dispatch-grid {
        grid-template-columns: 320px 1fr; /* Adjusted for better fit */
        gap: 10px;
    }

    .dispatch-container {
        padding: 10px;
        height: calc(100vh - 160px); /* Adjust for smaller header */
    }

    .incident-row {
        grid-template-columns: 100px 25px 60px 40px;
        gap: 6px;
        font-size: 10px;
        padding: 3px 4px;
    }

    .dispatch-incident-item {
        padding: 2px 4px;
        font-size: 10px;
    }

    .dispatch-header {
        padding: 6px 10px;
    }

    .dispatch-header h3 {
        font-size: 14px;
    }

    .shortcut-hint {
        font-size: 9px;
    }

    /* Optimize incident detail for smaller screens */
    .incident-detail-container {
        padding: 8px;
    }

    .incident-tabs {
        margin-bottom: 8px;
    }

    .incident-tab {
        padding: 6px 10px;
        font-size: 11px;
    }
}

@media (max-width: 768px) {
    .dispatch-container {
        padding: 10px;
    }

    /* On small screens, we still want to maintain the email inbox layout */
    .dispatch-grid {
        grid-template-columns: 280px 1fr;
        gap: 8px;
    }

    .dispatch-header {
        padding: 6px 8px;
    }

    .dispatch-header h3 {
        font-size: 12px;
    }

    .incident-row {
        grid-template-columns: 90px 25px 55px 35px;
        gap: 5px;
        font-size: 9px;
    }

    .dispatch-incident-item {
        padding: 2px 3px;
        font-size: 9px;
    }

    .quick-action-btn {
        font-size: 10px;
        padding: 4px 8px;
    }

    .incident-map-container {
        height: 200px;
    }

    .map-placeholder {
        height: 100%;
        min-height: 200px;
    }
}

/* Map container styles for all screen sizes */
.incident-map-container {
    height: 300px;
    width: 100%;
    border: 1px solid #ff4444;
    background: #000000;
    position: relative;
}

.map-placeholder {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff4444;
    font-family: monospace;
    font-size: 12px;
    background: #111111;
}

.map-error {
    color: #ff6666;
    text-align: center;
    padding: 20px;
    font-style: italic;
}

.map-controls {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.directions-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 11px;
    transition: all 0.2s ease;
}

.directions-btn:hover {
    background: #ff4444;
    color: #000000;
}

/* Ensure Google Maps displays correctly */
.incident-map-container .gm-style {
    font-family: monospace !important;
}

.incident-map-container .gm-style-iw {
    background: #000000 !important;
    color: #ffffff !important;
}

/* Specific optimizations for 1024x768 (Panasonic Toughbook) */
@media (width: 1024px) and (height: 768px) {
    /* Main layout adjustments */
    body {
        font-size: 12px;
    }

    .main-header {
        padding: 6px 10px;
        height: 40px;
    }

    .tab {
        padding: 6px 10px;
        font-size: 11px;
    }

    .content-area {
        padding: 8px;
        height: calc(100vh - 120px);
    }

    .content-area.incident-creation-content {
        overflow-y: auto !important;
        height: calc(100vh - 120px) !important;
        max-height: calc(100vh - 120px) !important;
        padding: 8px !important;
    }

    .status-bar {
        padding: 4px 10px;
        font-size: 10px;
        height: 24px;
    }

    /* Dashboard specific */
    .dashboard-container {
        padding: 8px;
        height: calc(100vh - 120px);
    }

    .dashboard-content {
        grid-template-columns: 90px 1fr;
        gap: 8px;
    }

    .main-content-area {
        gap: 8px;
    }

    .bottom-sections {
        gap: 8px;
    }

    .dashboard-section {
        padding: 8px;
        font-size: 11px;
    }

    .section-header h3 {
        font-size: 13px;
        margin-bottom: 6px;
    }

    /* Dispatch/CAD specific - email inbox layout */
    .dispatch-container {
        padding: 8px;
        height: calc(100vh - 120px);
    }

    .dispatch-grid {
        grid-template-columns: 300px 1fr; /* Adjusted for Toughbook */
        gap: 8px;
    }

    .dispatch-header {
        padding: 4px 8px;
    }

    .dispatch-header h3 {
        font-size: 12px;
    }

    .incident-row {
        grid-template-columns: 85px 25px 55px 35px;
        gap: 5px;
        font-size: 9px;
        padding: 2px 4px;
    }

    .dispatch-incident-item {
        padding: 2px 3px;
        font-size: 8px;
    }

    .incident-detail-container {
        padding: 6px;
    }

    .incident-tabs {
        margin-bottom: 6px;
    }

    .incident-tab {
        padding: 4px 8px;
        font-size: 10px;
    }

    .incident-map-container {
        height: 220px;
    }

    /* Weather widget optimizations for 1024x768 */
    .weather-section {
        flex: 0 0 240px;
        max-width: 240px;
    }

    .weather-temp {
        font-size: 18px;
    }

    .weather-condition {
        font-size: 9px;
    }

    .weather-location {
        font-size: 7px;
    }

    .weather-details {
        grid-template-columns: 1fr 1fr;
        gap: 0.1rem;
    }

    .detail-label {
        font-size: 7px;
    }

    .detail-value {
        font-size: 8px;
    }

    /* Grid layout adjustments for 1024x768 */
    .dashboard-sidebar {
        width: 90px;
        min-width: 90px;
    }

    .sidebar-header {
        padding: 6px;
    }

    .sidebar-header h3 {
        font-size: 11px;
    }

    .sidebar-content {
        padding: 0 4px 4px 4px;
    }

    .weather-section,
    .stats-section {
        min-height: 160px;
    }

    .incidents-section {
        min-height: 280px;
    }

    /* Quick actions for 1024x768 */
    .quick-actions {
        gap: 4px;
    }

    .action-btn {
        padding: 4px 2px;
        font-size: 7px;
        min-height: 38px;
        margin-bottom: 3px;
    }

    .action-icon {
        font-size: 11px;
    }

    .action-text {
        font-size: 6px;
        line-height: 1;
    }

    /* Incidents section optimizations */
    .incidents-section {
        min-height: 280px;
    }

    /* Quick actions */
    .quick-action-btn {
        padding: 4px 8px;
        font-size: 10px;
        margin: 1px;
    }

    /* Modal adjustments */
    .modal-dialog {
        max-width: 800px;
        margin: 20px;
    }

    .form-modal {
        max-width: 600px;
    }
}

/* Property Management Styles */
.property-container {
    color: #ffffff;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.property-header h2 {
    color: #ff0000;
    margin: 0;
}

.property-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.property-controls input,
.property-controls select {
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 5px 8px;
    font-family: monospace;
    font-size: 12px;
}

.property-stats {
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff4444;
    padding: 15px;
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #ff0000;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #ffffff;
}

.property-content {
    flex: 1;
    overflow-y: auto;
}

.property-list {
    border: 1px solid #ff4444;
}

.property-list-header {
    background: rgba(255, 0, 0, 0.2);
    border-bottom: 1px solid #ff4444;
}

.property-row {
    display: grid;
    grid-template-columns: 120px 100px 1fr 120px 100px 80px;
    gap: 10px;
    align-items: center;
    padding: 8px 10px;
    font-size: 12px;
    font-family: monospace;
}

.property-row.header-row {
    font-weight: bold;
    color: #ff0000;
}

.property-item {
    border-bottom: 1px solid #ff2222;
}

.property-item:hover {
    background: rgba(255, 0, 0, 0.05);
}

.property-status {
    font-weight: bold;
}

.status-found { color: #ffaa00; }
.status-investigating { color: #00aaff; }
.status-owner_identified { color: #aa00ff; }
.status-returned { color: #00ff00; }
.status-handed_to_police { color: #ff6600; }
.status-disposed { color: #666666; }

.property-actions {
    display: flex;
    gap: 5px;
}

.property-actions .action-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff0000;
    padding: 2px 6px;
    cursor: pointer;
    font-size: 12px;
}

.property-actions .action-btn:hover {
    background: #ff0000;
    color: #000000;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #ff4444;
}

.no-data h3 {
    color: #ff0000;
    margin-bottom: 10px;
}

/* Property Actions Panel */
.property-actions-panel {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ff4444;
}

.property-actions-panel h4 {
    color: #ff0000;
    margin-bottom: 10px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-buttons button {
    padding: 8px 16px;
    font-family: monospace;
    font-size: 12px;
    border: 1px solid;
    cursor: pointer;
    background: transparent;
}

.success-button {
    color: #00ff00;
    border-color: #00ff00;
}

.success-button:hover {
    background: #00ff00;
    color: #000000;
}

.warning-button {
    color: #ffaa00;
    border-color: #ffaa00;
}

.warning-button:hover {
    background: #ffaa00;
    color: #000000;
}

/* Property Details Styling */
.property-details {
    color: #ffffff;
}

.property-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.info-section {
    border: 1px solid #ff4444;
    padding: 15px;
}

.info-section h4 {
    color: #ff0000;
    margin-bottom: 10px;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
}

.info-row {
    margin-bottom: 8px;
    font-size: 12px;
}

.info-row strong {
    color: #ff8888;
    display: inline-block;
    width: 120px;
}

.notes-content {
    background: rgba(255, 0, 0, 0.1);
    padding: 10px;
    border-left: 3px solid #ff0000;
    font-size: 12px;
    white-space: pre-wrap;
}

.action-history {
    max-height: 200px;
    overflow-y: auto;
}

.action-item {
    border-bottom: 1px solid #ff2222;
    padding: 8px 0;
    font-size: 11px;
}

.action-item:last-child {
    border-bottom: none;
}

.action-date {
    color: #ff8888;
    font-weight: bold;
}

.action-description {
    color: #ffffff;
    margin: 2px 0;
}

.action-by {
    color: #aaaaaa;
    font-style: italic;
}

.action-notes {
    color: #cccccc;
    margin-top: 3px;
    padding-left: 10px;
    border-left: 2px solid #ff4444;
}

/* Property Report Styles */
.property-report {
    color: #ffffff;
    font-family: monospace;
}

.report-header {
    border-bottom: 2px solid #ff0000;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.report-header h3 {
    color: #ff0000;
    margin: 0 0 5px 0;
}

.report-header p {
    color: #aaaaaa;
    margin: 0;
    font-size: 12px;
}

.report-section {
    margin-bottom: 20px;
    border: 1px solid #ff4444;
    padding: 15px;
}

.report-section h4 {
    color: #ff0000;
    margin: 0 0 10px 0;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
}

.report-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.report-stats .stat-item {
    background: rgba(255, 0, 0, 0.1);
    padding: 8px;
    border-left: 3px solid #ff0000;
    font-size: 12px;
}

.report-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
}

.breakdown-item {
    background: rgba(255, 0, 0, 0.05);
    padding: 6px 10px;
    border: 1px solid #ff2222;
    font-size: 12px;
}

.overdue-list, .trends-list, .location-list {
    max-height: 300px;
    overflow-y: auto;
}

.overdue-item, .trend-item, .location-item {
    border-bottom: 1px solid #ff2222;
    padding: 10px 0;
    font-size: 12px;
}

.overdue-item:last-child, .trend-item:last-child, .location-item:last-child {
    border-bottom: none;
}

.property-number {
    color: #ff8888;
    font-weight: bold;
    margin-bottom: 3px;
}

.property-details {
    color: #ffffff;
    margin-bottom: 3px;
}

.property-date {
    color: #aaaaaa;
    font-size: 11px;
}

.trend-date {
    color: #ff8888;
    font-weight: bold;
    margin-bottom: 3px;
}

.trend-stats {
    color: #ffffff;
    font-size: 11px;
}

.location-name {
    color: #ff8888;
    margin-bottom: 3px;
}

.location-stats {
    color: #ffffff;
    font-size: 11px;
    margin-bottom: 2px;
}

.location-breakdown {
    color: #aaaaaa;
    font-size: 10px;
}

/* Property Type Selection Styles */
.property-type-selection {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.property-type-selection h3 {
    color: #00ff00;
    margin-bottom: 10px;
    font-family: 'Courier New', monospace;
}

.property-type-selection p {
    color: #cccccc;
    margin-bottom: 30px;
    font-family: 'Courier New', monospace;
}

.property-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.property-type-card {
    background: #1a1a1a;
    border: 2px solid #333333;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.property-type-card:hover {
    border-color: #00ff00;
    background: #2a2a2a;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 255, 0, 0.2);
}

.property-type-card:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 255, 0, 0.3);
}

.property-type-card .type-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.property-type-card .type-title {
    color: #00ff00;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.property-type-card .type-description {
    color: #cccccc;
    font-size: 14px;
    line-height: 1.4;
}

/* Property Form Styles */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    height: calc(100vh - 200px);
    overflow-y: auto;
}

.property-form {
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 30px;
    font-family: 'Courier New', monospace;
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #333333;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #00ff00;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #cccccc;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    background: #000000;
    border: 1px solid #333333;
    color: #ffffff;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    border-radius: 4px;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #333333;
}

/* Content Section Styles for Property Pages */
.content-section {
    height: calc(100vh - 60px); /* Account for header height */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.content-section .section-header {
    flex-shrink: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #ff4444;
    background: #000000;
    position: sticky;
    top: 0;
    z-index: 10;
}

.content-section .form-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0;
}

.content-section .form-container,
.content-section .property-list,
.content-section .missing-property-sections {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    min-height: 0; /* Fix for flexbox scrolling in some browsers */
}

/* Ensure form takes full height and scrolls */
.encampment-form {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0; /* Fix for flexbox scrolling */
}

.encampment-form .form-grid {
    flex: 1;
    overflow-y: auto;
    padding-right: 10px; /* Add some padding for scrollbar */
    margin-bottom: 20px;
}

/* Full-screen form styles */
#form-screen {
    display: none; /* Hidden by default */
    flex-direction: column;
    height: 100vh;
    background: #000000;
    color: #00ff00;
}

#form-screen.active {
    display: flex; /* Show when active */
}

#form-screen .main-header {
    flex-shrink: 0;
}

.form-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 20px;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #00ff00;
}

.form-header h2 {
    margin: 0;
    color: #00ff00;
    font-size: 24px;
    font-weight: bold;
}

.form-header .form-actions {
    display: flex;
    gap: 10px;
}

.form-body {
    flex: 1;
    overflow-y: auto;
    padding-right: 10px;
}

.fullscreen-form {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 60px); /* Account for top navigation */
    margin-top: 60px; /* Ensure top nav remains visible */
    position: relative;
    z-index: 10;
}

#form-fields-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}

/* Responsive design for form fields */
@media (max-width: 768px) {
    .form-fields-container {
        padding: 15px;
        max-width: 100%;
    }

    .fullscreen-form {
        height: calc(100vh - 50px);
        margin-top: 50px;
    }
}

@media (max-width: 480px) {
    .form-fields-container {
        padding: 10px;
    }
}

/* Form field styling for full-screen forms */
.fullscreen-form .form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    width: 100%;
}

.fullscreen-form .form-group label {
    color: #00ff00;
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 14px;
}

.fullscreen-form .form-group input,
.fullscreen-form .form-group select,
.fullscreen-form .form-group textarea {
    background: #111111;
    border: 1px solid #00ff00;
    color: #00ff00;
    padding: 8px 12px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 14px;
    border-radius: 3px;
}

.fullscreen-form .form-group input:focus,
.fullscreen-form .form-group select:focus,
.fullscreen-form .form-group textarea:focus {
    outline: none;
    border-color: #00aaff;
    box-shadow: 0 0 5px rgba(0, 170, 255, 0.3);
}

.fullscreen-form .form-group textarea {
    min-height: 80px;
    resize: vertical;
}

/* Checkbox styling for full-screen forms */
.fullscreen-form .checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 5px;
}

.fullscreen-form .checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.fullscreen-form .checkbox-group label {
    margin: 0;
    font-weight: normal;
}

/* Multi-select styling for full-screen forms */
.fullscreen-form .multi-select-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 5px;
}

.fullscreen-form .multi-select-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fullscreen-form .multi-select-option input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.fullscreen-form .multi-select-option label {
    margin: 0;
    font-weight: normal;
    cursor: pointer;
}

/* Required field indicator */
.fullscreen-form .form-group label.required::after {
    content: " *";
    color: #ff0000;
}

/* Form validation styling */
.fullscreen-form .form-group.error input,
.fullscreen-form .form-group.error select,
.fullscreen-form .form-group.error textarea {
    border-color: #ff0000;
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
}

.fullscreen-form .error-message {
    color: #ff0000;
    font-size: 12px;
    margin-top: 5px;
}

/* Person creation menu styles */
.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.menu-item {
    background: #111111;
    border: 2px solid #333333;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
    color: #00ff00;
}

.menu-item:hover {
    border-color: #00ff00;
    background: #1a1a1a;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.2);
}

.menu-icon {
    font-size: 2.5em;
    margin-bottom: 10px;
    display: block;
}

.menu-title {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 16px;
    color: #00ff00;
}

.menu-desc {
    font-size: 13px;
    color: #cccccc;
    line-height: 1.4;
}

/* Responsive menu grid */
@media (max-width: 768px) {
    .menu-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .menu-item {
        padding: 15px;
    }

    .menu-icon {
        font-size: 2em;
    }

    .menu-title {
        font-size: 15px;
    }

    .menu-desc {
        font-size: 12px;
    }
}

/* Person deletion warning styles */
.warning-section {
    background: #1a1a1a;
    border: 1px solid #ff4444;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.warning-section h4 {
    color: #ff4444;
    margin: 0 0 10px 0;
    font-size: 14px;
}

.warning-section ul {
    margin: 8px 0;
    padding-left: 20px;
    color: #cccccc;
}

.warning-section li {
    margin: 4px 0;
    font-size: 13px;
}

.danger-text {
    color: #ff4444;
    font-weight: bold;
    text-align: center;
    margin: 15px 0 5px 0;
}

.danger-button {
    background: #ff4444;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
}

.danger-button:hover {
    background: #ff0000;
}

/* Linked Records Styles */
.linked-records-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.linked-record-card {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: all 0.2s ease;
}

.linked-record-card:hover {
    border-color: #ff0000;
    background: #1a0000;
}

.record-info {
    flex: 1;
    min-width: 0;
}

.record-title {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 8px;
}

.record-description {
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.record-detail {
    color: #cccccc;
    font-size: 13px;
    margin-bottom: 4px;
}

.record-status, .record-type, .record-severity, .record-priority {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin: 4px 4px 4px 0;
}

.record-status {
    background: #333333;
    color: #ffffff;
}

.status-active, .status-scheduled {
    background: #28a745;
    color: #ffffff;
}

.status-completed, .status-resolved {
    background: #007cba;
    color: #ffffff;
}

.status-cancelled, .status-breached, .status-missed {
    background: #dc3545;
    color: #ffffff;
}

.status-in-progress, .status-rescheduled {
    background: #ffc107;
    color: #000000;
}

.record-type {
    background: #6f42c1;
    color: #ffffff;
}

.record-severity, .record-priority {
    background: #fd7e14;
    color: #ffffff;
}

.severity-high, .priority-high, .priority-urgent {
    background: #dc3545;
    color: #ffffff;
}

.severity-medium, .priority-medium {
    background: #ffc107;
    color: #000000;
}

.severity-low, .priority-low {
    background: #28a745;
    color: #ffffff;
}

.record-flag {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    margin: 2px 4px 2px 0;
}

.emergency-contact {
    background: #dc3545;
    color: #ffffff;
}

.primary-contact {
    background: #007cba;
    color: #ffffff;
}

.record-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.no-records {
    text-align: center;
    color: #ff4444;
    padding: 30px;
    font-style: italic;
}

/* Criminal Justice Specific Styles */
.criminal-justice-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
}

.criminal-justice-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.criminal-justice-actions .primary-button {
    font-size: 12px;
    padding: 6px 12px;
}

.criminal-justice-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.criminal-justice-subsection {
    border: 1px solid #333333;
    padding: 15px;
    background: #111111;
}

.criminal-justice-subsection h5 {
    color: #ff0000;
    font-size: 14px;
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #333333;
}

/* Responsive adjustments for linked records */
@media (max-width: 768px) {
    .linked-record-card {
        flex-direction: column;
        gap: 10px;
    }

    .record-actions {
        align-self: stretch;
        justify-content: flex-end;
    }

    .criminal-justice-actions {
        flex-direction: column;
    }

    .criminal-justice-actions .primary-button {
        width: 100%;
    }
}

.encampment-form .form-actions {
    flex-shrink: 0;
    padding: 15px 0;
    background: #000000;
    border-top: 1px solid #ff4444;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    position: sticky;
    bottom: 0;
    z-index: 5;
}

/* Make sure form actions stay at the bottom */
.form-section {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Style scrollbar for better visibility */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #ff4444;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #ff6666;
}

.content-section .property-type-selection {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Search Section Styles */
.search-section {
    flex-shrink: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #333333;
    background: #111111;
}

.search-bar {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    flex: 1;
    background: #000000;
    border: 1px solid #333333;
    color: #ffffff;
    padding: 8px 12px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    border-radius: 4px;
}

.search-input:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
}

.search-button {
    background: #333333;
    border: 1px solid #555555;
    color: #ffffff;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.search-button:hover {
    background: #555555;
    border-color: #777777;
}

/* Links Tab Styles */
.incident-links {
    color: #ffffff;
}

.incident-links h4, .incident-links h5 {
    color: #ff0000;
    margin: 0 0 10px 0;
}

.links-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.link-category {
    border: 1px solid #ff4444;
    padding: 10px;
}

.attach-btn {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    font-family: monospace;
    font-size: 11px;
    margin-bottom: 10px;
}

.attach-btn:hover {
    background: #ff0000;
    color: #000000;
}

.linked-items {
    min-height: 30px;
}

.linked-item {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff4444;
    padding: 5px 8px;
    margin-bottom: 5px;
    font-size: 12px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.link-type {
    color: #ff8888;
    font-size: 10px;
    font-style: italic;
}

.link-notes {
    color: #cccccc;
    font-size: 10px;
    margin-top: 3px;
    padding-left: 10px;
    border-left: 2px solid #ff4444;
}

.vehicle-details, .address-details {
    color: #aaaaaa;
    font-size: 10px;
}

.remove-link-btn {
    position: absolute;
    top: 2px;
    right: 5px;
    background: transparent;
    border: none;
    color: #ff4444;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-link-btn:hover {
    background: #ff4444;
    color: #000000;
    border-radius: 50%;
}

.no-links {
    color: #ff4444;
    font-style: italic;
    font-size: 12px;
}

/* Log Tab Styles */
.incident-log {
    color: #ffffff;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.log-header h4 {
    color: #ff0000;
    margin: 0;
}

.add-log-btn {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    font-family: monospace;
    font-size: 11px;
}

.add-log-btn:hover {
    background: #ff0000;
    color: #000000;
}

.log-entries {
    max-height: 300px;
    overflow-y: auto;
}

.log-entry {
    border-left: 2px solid #ff4444;
    padding-left: 10px;
    margin-bottom: 15px;
}

.log-timestamp {
    color: #ff4444;
    font-size: 11px;
    margin-bottom: 5px;
}

.log-content {
    color: #ffffff;
    font-size: 12px;
    line-height: 1.4;
}

.log-type {
    display: inline-block;
    font-weight: bold;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.log-type.incident-created {
    background: #004400;
    color: #00ff00;
}

.log-type-note {
    background: #333333;
    color: #ffffff;
}

.log-type-status_update {
    background: #444400;
    color: #ffff00;
}

.log-type-action_taken {
    background: #004400;
    color: #00ff00;
}

.log-type-follow_up {
    background: #440000;
    color: #ff4444;
}

.log-type-contact_made {
    background: #004444;
    color: #00ffff;
}

.log-type-other {
    background: #440044;
    color: #ff44ff;
}

.log-user {
    color: #ff4444;
    font-size: 10px;
    font-style: italic;
    margin-bottom: 3px;
}

.log-text {
    color: #ffffff;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
}

/* Status Tab Styles */
.incident-status {
    color: #ffffff;
}

.incident-status h4 {
    color: #ff0000;
    margin: 0 0 15px 0;
}

.status-timeline {
    position: relative;
}

.status-timeline::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #ff4444;
}

.status-entry {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.status-entry::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 5px;
    width: 10px;
    height: 10px;
    background: #ff0000;
    border-radius: 50%;
}

.status-timestamp {
    color: #ff4444;
    font-size: 11px;
    margin-bottom: 3px;
}

.status-change {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 3px;
}

.status-user {
    color: #ff4444;
    font-size: 11px;
    font-style: italic;
}

/* Updated Dashboard Widget Styles */
.dashboard-widget .widget-footer {
    padding: 10px 0 0 0;
    border-top: 1px solid #ff4444;
    margin-top: 10px;
    text-align: center;
}

/* People Management Styles - Clean Implementation */
.content-area.people-management-content {
    padding: 0px 15px 15px 15px !important;
}

.content-section {
    margin: 5px 0 0 0 !important;
    padding: 0 !important;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 8px 0 !important;
    padding: 0 0 6px 0 !important;
    border-bottom: 2px solid #ff4444;
}

.section-header h2 {
    color: #ff0000;
    margin: 0;
    padding: 0;
    font-size: clamp(16px, 2vw, 20px);
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-actions .primary-button,
.header-actions .secondary-button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 6px 10px;
    font-size: 12px;
    min-height: 30px;
}

.button-icon {
    font-size: 14px;
}

.search-section {
    margin-bottom: 20px;
}

.search-bar {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 10px;
    background: #111111;
    border: 2px solid #ff4444;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #ff0000;
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
}

.search-button {
    padding: 10px 15px;
    background: #ff0000;
    border: none;
    color: #ffffff;
    cursor: pointer;
    font-size: 16px;
    min-width: 45px;
}

.search-button:hover {
    background: #cc0000;
}

.people-list-container {
    background: #111111;
    border: 2px solid #ff4444;
    padding: 15px;
}

.people-count {
    color: #ff4444;
    font-size: 14px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.people-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.person-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #000000;
    border: 1px solid #ff4444;
    cursor: pointer;
    transition: all 0.2s ease;
}

.person-card:hover {
    border-color: #ff0000;
    background: #1a0000;
}

.person-avatar {
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    background: #ff0000;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    border-radius: 50%;
}

.person-info {
    flex: 1;
    min-width: 0;
}

.person-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
}

.person-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 5px;
}

.detail-item {
    color: #ffffff;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 3px;
}

.person-meta {
    color: #ff4444;
    font-size: 11px;
}

.person-actions {
    flex-shrink: 0;
}

.action-button {
    padding: 8px 12px;
    background: #ff0000;
    border: none;
    color: #ffffff;
    cursor: pointer;
    font-size: 12px;
    font-family: 'Courier New', monospace;
}

.action-button:hover {
    background: #cc0000;
}

.view-button {
    background: #ff4444;
}

.view-button:hover {
    background: #ff0000;
}

.no-records {
    text-align: center;
    color: #ff4444;
    padding: 40px;
    font-style: italic;
}

/* Person Detail Styles */
.person-detail-container {
    background: #111111;
    border: 2px solid #ff4444;
    padding: 10px 15px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.person-detail-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 2px solid #ff4444;
}

.person-avatar-large {
    flex-shrink: 0;
}

.avatar-placeholder-large {
    width: 55px;
    height: 55px;
    background: #ff0000;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    border-radius: 50%;
}

.person-title-info {
    flex: 1;
}

.person-full-name {
    color: #ff0000;
    font-size: clamp(18px, 2.5vw, 22px);
    margin: 0 0 8px 0;
}

.person-id,
.person-created {
    color: #ff4444;
    font-size: 13px;
    margin-bottom: 4px;
}

.person-detail-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.detail-sections {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.detail-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 10px 12px;
}

.detail-section h4 {
    color: #ff0000;
    font-size: 15px;
    margin: 0 0 8px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.detail-grid .detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-grid .detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-grid .detail-item label {
    color: #ff4444;
    font-size: 12px;
    font-weight: bold;
}

.detail-grid .detail-item span {
    color: #ffffff;
    font-size: 14px;
    word-wrap: break-word;
}

.activity-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 20px;
    height: fit-content;
}

.activity-section h4 {
    color: #ff0000;
    font-size: 16px;
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.no-activities {
    color: #ff4444;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Person Detail Tabs */
.person-detail-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 10px;
    border-bottom: 2px solid #ff4444;
}

.person-detail-tab {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    border-bottom: none;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.person-detail-tab:hover {
    background: #ff4444;
    color: #000000;
}

.person-detail-tab.active {
    background: #ff0000;
    color: #ffffff;
    font-weight: bold;
}

.person-detail-content .tab-content {
    display: none;
}

.person-detail-content .tab-content.active {
    display: block;
}

/* Pets Section */
.pets-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
}

.pets-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.pets-section h4 {
    color: #ff0000;
    font-size: 16px;
    margin: 0;
}

.pets-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.pet-card {
    background: #111111;
    border: 1px solid #ff4444;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
}

.pet-info {
    flex: 1;
}

.pet-name {
    color: #ff0000;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.pet-details {
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 5px;
}

.pet-species {
    color: #ff4444;
    font-weight: bold;
}

.pet-breed,
.pet-age {
    color: #ffffff;
}

.pet-color,
.pet-description,
.pet-microchip,
.pet-vaccination {
    color: #ffffff;
    font-size: 13px;
    margin-bottom: 3px;
}

.pet-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.pet-actions .action-button {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
}

.no-pets {
    color: #ff4444;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Medical Issues Section */
.medical-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
}

.medical-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.medical-section h4 {
    color: #ff0000;
    font-size: 16px;
    margin: 0;
}

.medical-issues-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.medical-issue-card {
    background: #111111;
    border: 1px solid #ff4444;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
}

.medical-issue-info {
    flex: 1;
}

.medical-issue-name {
    color: #ff0000;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.medical-issue-details {
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 5px;
}

.medical-severity {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.medical-severity.severity-mild {
    background: #28a745;
    color: #ffffff;
}

.medical-severity.severity-moderate {
    background: #ffc107;
    color: #000000;
}

.medical-severity.severity-severe {
    background: #fd7e14;
    color: #ffffff;
}

.medical-severity.severity-critical {
    background: #dc3545;
    color: #ffffff;
}

.medical-status {
    color: #ff4444;
    font-weight: bold;
}

.medical-date {
    color: #ffffff;
}

.medical-treatment,
.medical-medication,
.medical-provider,
.medical-followup,
.medical-notes {
    color: #ffffff;
    font-size: 13px;
    margin-bottom: 3px;
}

.medical-treatment {
    color: #90EE90;
}

.medical-medication {
    color: #87CEEB;
}

.medical-provider {
    color: #DDA0DD;
}

.medical-followup {
    color: #FFB6C1;
    font-weight: bold;
}

.medical-issue-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.medical-issue-actions .action-button {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
}

.no-medical-issues {
    color: #ff4444;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Organizations Management Styles */
.organizations-management-content {
    background: #000000;
    color: #ffffff;
    min-height: calc(100vh - 100px);
}

.organizations-list-container {
    background: #111111;
    border: 2px solid #ff4444;
    padding: 15px;
    margin-top: 15px;
}

.organizations-count {
    color: #ff4444;
    font-size: 14px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.organizations-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.organization-card {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    transition: border-color 0.2s ease;
}

.organization-card:hover {
    border-color: #ff0000;
}

.organization-icon {
    flex-shrink: 0;
}

.org-icon-placeholder {
    width: 50px;
    height: 50px;
    background: #ff0000;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    border-radius: 8px;
}

.organization-info {
    flex: 1;
}

.organization-name {
    color: #ff0000;
    font-size: clamp(16px, 2.5vw, 20px);
    font-weight: bold;
    margin-bottom: 8px;
}

.organization-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 8px;
}

.organization-details .detail-item {
    color: #ffffff;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.organization-services {
    color: #90EE90;
    font-size: 13px;
    margin-bottom: 8px;
    font-style: italic;
}

.organization-meta {
    color: #ff4444;
    font-size: 12px;
}

.organization-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-shrink: 0;
}

.organization-actions .action-button {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 80px;
}

.no-records {
    color: #ff4444;
    font-style: italic;
    text-align: center;
    padding: 40px;
    font-size: 16px;
}

/* Multi-select checkbox styles */
.multi-select-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ff4444;
    padding: 10px;
    background: #111111;
}

.checkbox-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px;
}

.checkbox-option input[type="checkbox"] {
    accent-color: #ff0000;
}

.checkbox-option label {
    color: #ffffff;
    font-size: 13px;
    cursor: pointer;
}

.checkbox-option:hover {
    background: #222222;
}

/* Services tags display */
.services-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 5px;
}

.service-tag {
    background: #ff0000;
    color: #ffffff;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

/* Responsive adjustments for person detail */
@media (max-width: 1024px) {
    .person-detail-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .person-detail-header {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        margin-bottom: 15px;
        padding-bottom: 10px;
    }

    .detail-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .content-area.people-management-content {
        padding: 5px 10px 10px 10px;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
        margin-bottom: 10px;
        padding-bottom: 8px;
    }

    .header-actions {
        justify-content: center;
    }

    .person-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .person-details {
        justify-content: center;
    }
}

/* Person Summary Modal Styles */
.person-summary-modal {
    max-width: 600px;
    width: 90%;
}

.person-summary-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.person-summary-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ff4444;
}

.person-avatar-summary {
    flex-shrink: 0;
}

.avatar-placeholder-summary {
    width: 60px;
    height: 60px;
    background: #ff0000;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    border-radius: 50%;
}

.person-summary-info {
    flex: 1;
}

.person-summary-name {
    color: #ff0000;
    font-size: 20px;
    margin: 0 0 5px 0;
}

.person-summary-id {
    color: #ff4444;
    font-size: 14px;
}

.person-summary-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.summary-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
}

.summary-section h5 {
    color: #ff0000;
    font-size: 14px;
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.summary-label {
    color: #ff4444;
    font-size: 12px;
    font-weight: bold;
}

.summary-value {
    color: #ffffff;
    font-size: 13px;
    word-wrap: break-word;
}

/* Responsive adjustments for summary modal */
@media (max-width: 768px) {
    .person-summary-modal {
        width: 95%;
        max-width: none;
    }

    .person-summary-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .summary-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

.go-to-dispatch-btn {
    background: #ff0000;
    color: #000000;
    border: none;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-weight: bold;
    font-size: 11px;
    width: 100%;
}

.go-to-dispatch-btn:hover {
    background: #ff4444;
}

.incident-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.incident-item:hover {
    background: rgba(255, 0, 0, 0.05);
}

.incident-type {
    color: #ffffff;
    font-size: 12px;
    margin: 3px 0;
    font-weight: bold;
}

.incident-assigned {
    color: #ff4444;
    font-size: 11px;
    margin: 3px 0;
}

.incident-priority {
    font-weight: bold;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 2px;
}

.incident-priority.priority-high {
    background: #ff0000;
    color: #000000;
}

.incident-priority.priority-medium {
    background: #ffaa00;
    color: #000000;
}

.incident-priority.priority-low {
    background: #00ff88;
    color: #000000;
}

/* Final Polish - Ensure consistent monospace theme */
.dispatch-container * {
    font-family: 'Courier New', monospace;
}

.dispatch-incident-item:focus {
    outline: 2px solid #ff0000;
    outline-offset: -2px;
}

.incident-detail-tab:focus {
    outline: 2px solid #ff0000;
    outline-offset: -2px;
}

.quick-action-btn:focus {
    outline: 2px solid #ff0000;
    outline-offset: -2px;
}

/* Loading states */
.loading {
    color: #ff4444;
    text-align: center;
    padding: 20px;
    font-style: italic;
}

.error {
    color: #ff0000;
    text-align: center;
    padding: 20px;
    font-weight: bold;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff0000;
}

/* Accessibility improvements */
.dispatch-incident-item[aria-selected="true"] {
    background: rgba(255, 0, 0, 0.2);
    border-color: #ff0000;
}

/* Animation for new incidents */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.dispatch-incident-item.new-incident {
    animation: slideInLeft 0.5s ease-out;
}

/* Improved scrollbar styling for dispatch areas */
.incident-list-body::-webkit-scrollbar,
.incident-detail-container::-webkit-scrollbar {
    width: 8px;
}

.incident-list-body::-webkit-scrollbar-track,
.incident-detail-container::-webkit-scrollbar-track {
    background: #000000;
}

.incident-list-body::-webkit-scrollbar-thumb,
.incident-detail-container::-webkit-scrollbar-thumb {
    background: #ff0000;
    border-radius: 4px;
}

.incident-list-body::-webkit-scrollbar-thumb:hover,
.incident-detail-container::-webkit-scrollbar-thumb:hover {
    background: #ff4444;
}

/* Supply Provision Styles */
.large-modal .modal-dialog {
    max-width: 800px;
    width: 90%;
}

.activity-summary {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff4444;
    padding: 10px;
    margin-bottom: 15px;
    font-family: monospace;
}

.activity-summary p {
    margin: 5px 0;
    color: #ffffff;
}

.activity-summary strong {
    color: #ff0000;
}

.supply-items-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ff4444;
    padding: 10px;
    background: #111111;
}

.supply-item {
    border: 1px solid #ff4444;
    margin-bottom: 10px;
    padding: 10px;
    background: #000000;
    font-family: monospace;
}

.supply-item:last-child {
    margin-bottom: 0;
}

.supply-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.supply-item-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
}

.supply-item-stock {
    color: #00ff88;
    font-size: 12px;
}

.supply-item-description {
    color: #ffffff;
    font-size: 12px;
    margin-bottom: 5px;
    font-style: italic;
}

.supply-item-category {
    color: #ff4444;
    font-size: 11px;
    text-transform: uppercase;
    margin-bottom: 8px;
}

.supply-item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.supply-item-controls label {
    color: #ff0000;
    font-size: 12px;
    font-weight: bold;
}

.quantity-input {
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 4px 8px;
    width: 80px;
    font-family: monospace;
    font-size: 12px;
}

.quantity-input:focus {
    outline: none;
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
}

.unit-label {
    color: #ff4444;
    font-size: 11px;
}

.supply-notes {
    margin-top: 15px;
}

.supply-notes label {
    display: block;
    color: #ff0000;
    font-weight: bold;
    margin-bottom: 5px;
    font-family: monospace;
}

.supply-notes textarea {
    width: 100%;
    height: 80px;
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 8px;
    font-family: monospace;
    font-size: 12px;
    resize: vertical;
}

.supply-notes textarea:focus {
    outline: none;
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.05);
}

/* Supply provision activity display */
.activity-supplies {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid #00ff88;
    padding: 8px;
    margin-top: 8px;
    font-family: monospace;
}

.activity-supplies h5 {
    color: #00ff88;
    margin: 0 0 5px 0;
    font-size: 12px;
}

.supply-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.supply-list li {
    color: #ffffff;
    font-size: 11px;
    margin-bottom: 2px;
}

.supply-quantity {
    color: #00ff88;
    font-weight: bold;
}

/* Items Management Styles */
.items-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ff4444;
}

.items-table {
    font-family: monospace;
    width: 100%;
}

.items-header {
    display: grid;
    grid-template-columns: 2fr 1fr 80px 80px 80px 120px;
    gap: 10px;
    padding: 10px;
    background: #ff0000;
    color: #000000;
    font-weight: bold;
    font-size: 12px;
    border: 1px solid #ff0000;
}

.items-body {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ff4444;
    border-top: none;
}

.item-row {
    display: grid;
    grid-template-columns: 2fr 1fr 80px 80px 80px 120px;
    gap: 10px;
    padding: 8px 10px;
    border-bottom: 1px solid #ff4444;
    color: #ffffff;
    font-size: 12px;
    align-items: center;
}

.item-row:hover {
    background: rgba(255, 0, 0, 0.1);
}

.item-row.low-stock {
    background: rgba(255, 170, 0, 0.1);
    border-color: #ffaa00;
}

.item-name {
    font-weight: bold;
    color: #ff0000;
}

.item-category {
    text-transform: capitalize;
    color: #ff4444;
}

.item-stock.low-stock {
    color: #ffaa00;
    font-weight: bold;
}

.item-status.active {
    color: #00ff88;
}

.item-status.inactive {
    color: #ff4444;
}

.item-actions {
    display: flex;
    gap: 5px;
}

.action-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 2px 6px;
    cursor: pointer;
    font-size: 12px;
    border-radius: 2px;
}

.action-btn:hover {
    background: #ff4444;
    color: #000000;
}

.no-items {
    text-align: center;
    color: #ff4444;
    padding: 40px 20px;
    font-style: italic;
}

/* Low Stock Alert Styles */
.low-stock-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ffaa00;
    padding: 10px;
    background: rgba(255, 170, 0, 0.1);
}

.low-stock-item {
    background: #000000;
    border: 1px solid #ffaa00;
    padding: 10px;
    margin-bottom: 10px;
    color: #ffffff;
    font-family: monospace;
    font-size: 12px;
}

.low-stock-item:last-child {
    margin-bottom: 0;
}

.low-stock-item strong {
    color: #ffaa00;
}

/* Outreach Transaction Screen Styles */
.outreach-transaction-screen {
    padding: 15px;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.outreach-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ff0000;
}

.outreach-header h2 {
    color: #ff0000;
    margin: 0;
    font-size: clamp(18px, 2.5vw, 24px);
}

.close-screen-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 12px;
    transition: all 0.2s ease;
}

.close-screen-btn:hover {
    background: #ff4444;
    color: #000000;
}

.outreach-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
}

.outreach-section {
    background: #111111;
    border: 2px solid #ff0000;
    padding: 15px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.person-section {
    min-height: 200px;
}

.items-section {
    min-height: 250px;
}

.details-section {
    min-height: 150px;
}

.outreach-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ff4444;
}

.outreach-section .section-header h3 {
    color: #ff0000;
    margin: 0;
    font-size: clamp(14px, 1.8vw, 18px);
}

/* Person Search Styles */
.person-search-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.search-input-group {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.person-search-input {
    flex: 1;
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 8px 12px;
    font-family: monospace;
    font-size: 12px;
}

.person-search-input:focus {
    outline: none;
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
}

.search-btn, .add-person-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 8px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 12px;
    white-space: nowrap;
}

.search-btn:hover, .add-person-btn:hover {
    background: #ff4444;
    color: #000000;
}

.add-person-btn {
    border-color: #00ff88;
    color: #00ff88;
}

.add-person-btn:hover {
    background: #00ff88;
    color: #000000;
}

.person-results {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #ff4444;
    background: #000000;
}

.person-result {
    padding: 10px;
    border-bottom: 1px solid #ff4444;
    cursor: pointer;
    transition: background 0.2s ease;
}

.person-result:hover {
    background: rgba(255, 0, 0, 0.1);
}

.person-result:last-child {
    border-bottom: none;
}

.person-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 4px;
}

.person-details {
    color: #ffffff;
    font-size: 11px;
    margin-bottom: 4px;
}

.person-id {
    color: #ff4444;
    font-size: 10px;
}

.selected-person {
    border: 2px solid #00ff88;
    padding: 15px;
    background: rgba(0, 255, 136, 0.1);
}

.selected-person-info h4 {
    color: #00ff88;
    margin: 0 0 8px 0;
    font-size: 16px;
}

.person-contact {
    color: #ffffff;
    font-size: 12px;
    margin-bottom: 8px;
}

.change-person-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 4px 8px;
    cursor: pointer;
    font-family: monospace;
    font-size: 10px;
    margin-top: 10px;
}

.change-person-btn:hover {
    background: #ff4444;
    color: #000000;
}

.no-selection, .no-results, .no-items {
    text-align: center;
    color: #ff4444;
    padding: 20px;
    font-style: italic;
}

.loading {
    text-align: center;
    color: #ffaa00;
    padding: 20px;
}

.error {
    text-align: center;
    color: #ff6666;
    padding: 20px;
}

/* Items Section Styles */
.add-item-btn {
    background: transparent;
    border: 1px solid #00ff88;
    color: #00ff88;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 12px;
}

.add-item-btn:hover:not(:disabled) {
    background: #00ff88;
    color: #000000;
}

.add-item-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.items-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #ff4444;
    background: #000000;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #ff4444;
}

.transaction-item:last-child {
    border-bottom: none;
}

.item-details {
    flex: 1;
}

.item-details .item-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 13px;
    margin-bottom: 4px;
}

.item-details .item-category {
    color: #ff4444;
    font-size: 10px;
    text-transform: uppercase;
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: 4px;
    margin: 0 15px;
}

.item-quantity .quantity {
    color: #00ff88;
    font-weight: bold;
    font-size: 16px;
}

.item-quantity .unit {
    color: #ffffff;
    font-size: 12px;
}

.item-actions {
    display: flex;
    gap: 5px;
}

.edit-quantity-btn, .remove-item-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 4px 6px;
    cursor: pointer;
    font-size: 12px;
    border-radius: 2px;
}

.edit-quantity-btn:hover {
    background: #ffaa00;
    border-color: #ffaa00;
    color: #000000;
}

.remove-item-btn:hover {
    background: #ff4444;
    color: #000000;
}

.items-total {
    margin-top: 10px;
    padding: 10px;
    border-top: 1px solid #ff4444;
    text-align: center;
}

.total-items {
    color: #00ff88;
    font-weight: bold;
    font-size: 14px;
}

/* Details Section Styles */
.details-form {
    flex: 1;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    color: #ff0000;
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 12px;
}

.form-group input, .form-group textarea {
    width: 100%;
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 8px;
    font-family: monospace;
    font-size: 12px;
    box-sizing: border-box;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
}

.form-group textarea {
    height: 80px;
    resize: vertical;
}

.location-btn {
    background: transparent;
    border: 1px solid #ffaa00;
    color: #ffaa00;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 8px;
    border-radius: 2px;
}

.location-btn:hover {
    background: #ffaa00;
    color: #000000;
}

/* Outreach Actions */
.outreach-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 2px solid #ff0000;
    flex-shrink: 0;
}

.outreach-actions button {
    padding: 10px 20px;
    font-family: monospace;
    font-size: 12px;
    cursor: pointer;
    border: 2px solid;
    background: transparent;
    transition: all 0.2s ease;
}

.outreach-actions .secondary-button {
    border-color: #ff4444;
    color: #ff4444;
}

.outreach-actions .secondary-button:hover {
    background: #ff4444;
    color: #000000;
}

.outreach-actions .primary-button {
    border-color: #00ff88;
    color: #00ff88;
}

.outreach-actions .primary-button:hover:not(:disabled) {
    background: #00ff88;
    color: #000000;
}

.outreach-actions .primary-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Item Catalog Modal Styles */
.item-catalog {
    max-height: 500px;
    overflow-y: auto;
}

.catalog-category {
    margin-bottom: 20px;
}

.category-title {
    color: #ff0000;
    font-size: 14px;
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.category-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.catalog-item {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.catalog-item:hover:not(.out-of-stock) {
    background: rgba(255, 0, 0, 0.1);
    border-color: #ff0000;
}

.catalog-item.out-of-stock {
    opacity: 0.5;
    cursor: not-allowed;
}

.catalog-item .item-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 13px;
    margin-bottom: 5px;
}

.catalog-item .item-description {
    color: #ffffff;
    font-size: 11px;
    margin-bottom: 5px;
}

.catalog-item .item-stock {
    color: #00ff88;
    font-size: 10px;
}

.catalog-item .stock-warning {
    color: #ff6666;
    font-size: 10px;
    font-weight: bold;
    margin-top: 5px;
}

/* Quantity Input Modal */
.quantity-input-form {
    text-align: center;
}

.quantity-input-form .item-info {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff4444;
}

.quantity-input-form .item-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
}

.quantity-input-form .item-description {
    color: #ffffff;
    font-size: 12px;
    margin-bottom: 5px;
}

.quantity-input-form .item-stock {
    color: #00ff88;
    font-size: 11px;
}

.quantity-input {
    width: 80px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
}

.unit-label {
    color: #ff4444;
    font-size: 12px;
    margin-left: 8px;
}

/* Responsive adjustments for outreach transactions */
@media (max-width: 1024px) {
    .outreach-content {
        gap: 10px;
    }

    .outreach-section {
        min-height: unset;
    }

    .person-section {
        min-height: 180px;
    }

    .items-section {
        min-height: 200px;
    }

    .details-section {
        min-height: 120px;
    }

    .search-input-group {
        flex-wrap: wrap;
    }

    .category-items {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .outreach-transaction-screen {
        padding: 10px;
    }

    .outreach-actions {
        flex-direction: column;
    }

    .outreach-actions button {
        width: 100%;
    }
}

/* Notification animations */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    border: 1px solid #ff0000;
    box-shadow: 2px 2px 0 rgba(255, 0, 0, 0.3);
}

.notification-info {
    background: #ff0000;
    color: #000000;
}

.notification-success {
    background: #00ff00;
    color: #000000;
}

.notification-error {
    background: #ff4444;
    color: #ffffff;
}

/* Incident Creation Form Styles - Clean implementation */
.incident-form-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.incident-form-scroll {
    flex: 1;
    overflow-y: auto;
    height: calc(100vh - 180px);
    max-height: calc(100vh - 180px);
    padding: 10px;
    box-sizing: border-box;
    border: 2px solid #333;
}

.content-area.incident-creation-content {
    overflow: hidden !important;
    padding: 10px !important;
}

/* Override content-area for incidents tab */
.content-area.incidents-content {
    height: auto !important;
    max-height: none !important;
    overflow: hidden !important;
    padding: 0 !important;
}

/* Clean Incidents Interface */
.incidents-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    background: #000000;
    color: #ff0000;
}

.incidents-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #ff0000;
    background: #000000;
}

.incidents-header h2 {
    color: #ff0000;
    font-size: 18px;
    margin: 0;
}

.incidents-status {
    font-size: 12px;
    color: #ff4444;
}

.incidents-main {
    flex: 1;
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 15px;
    padding: 15px;
    overflow: hidden;
    min-height: 0;
}

.incidents-list-section {
    display: flex;
    flex-direction: column;
    border: 1px solid #ff4444;
    background: #000000;
    height: 100%;
    overflow: hidden;
}

.incidents-list-header {
    padding: 10px;
    border-bottom: 1px solid #ff4444;
    background: #000000;
    flex-shrink: 0;
}

.incidents-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.incidents-list::-webkit-scrollbar {
    width: 6px;
}

.incidents-list::-webkit-scrollbar-track {
    background: #000000;
}

.incidents-list::-webkit-scrollbar-thumb {
    background: #ff4444;
    border-radius: 3px;
}

.incidents-list::-webkit-scrollbar-thumb:hover {
    background: #ff0000;
}

.list-filters {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
    overflow: hidden;
}

.list-filters select,
.list-filters input {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    padding: 4px 6px;
    font-family: inherit;
    font-size: 11px;
    min-width: 0;
    flex-shrink: 1;
}

.list-filters input[type="text"] {
    flex: 1;
    min-width: 100px;
    max-width: 200px;
}

.list-filters select {
    min-width: 80px;
    max-width: 120px;
}

.list-filters select:focus,
.list-filters input:focus {
    outline: none;
    border-color: #ff0000;
    color: #ff0000;
}

.incident-item {
    padding: 10px;
    margin-bottom: 8px;
    border: 1px solid #ff4444;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #000000;
}

.incident-item:hover {
    background: #330000;
    border-color: #ff0000;
    color: #ff0000;
}

.incident-item.selected {
    background: #ff0000;
    color: #000000;
    border-color: #ff0000;
}

.incident-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.incident-number {
    font-weight: bold;
    color: #ff0000;
}

.incident-item.selected .incident-number {
    color: #000000;
}

.incident-priority {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 2px;
}

.incident-priority.high {
    background: #ff0000;
    color: #000000;
}

.incident-priority.medium {
    background: #ffff00;
    color: #000000;
}

.incident-priority.low {
    background: #00ff00;
    color: #000000;
}

.incident-type {
    font-size: 12px;
    color: #ff4444;
    margin-bottom: 3px;
}

.incident-location {
    font-size: 11px;
    color: #888888;
}

.incident-details-section {
    border: 1px solid #ff4444;
    background: #000000;
    padding: 15px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.no-selection {
    text-align: center;
    color: #ff4444;
    padding: 40px 20px;
}

.no-selection h3 {
    color: #ff0000;
    margin-bottom: 10px;
}

.incident-details {
    color: #ff4444;
}

.incident-details h3 {
    color: #ff0000;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    margin-bottom: 8px;
}

.detail-label {
    font-weight: bold;
    color: #ff0000;
    min-width: 120px;
}

.detail-value {
    color: #ff4444;
}

.incident-actions {
    margin-top: auto;
    padding: 15px 0 0 0;
    border-top: 1px solid #ff4444;
    display: flex;
    gap: 8px;
    flex-shrink: 0;
    background: #000000;
}

.incident-actions .action-button {
    flex: 1;
    min-height: 32px;
    font-size: 11px;
    padding: 6px 8px;
}

.close-button {
    background: #004400 !important;
    color: #00ff00 !important;
    border-color: #00ff00 !important;
}

.close-button:hover {
    background: #006600 !important;
    color: #00ff00 !important;
    border-color: #00ff00 !important;
}

.delete-button {
    background: #660000 !important;
    color: #ff4444 !important;
    border-color: #ff4444 !important;
}

.delete-button:hover {
    background: #990000 !important;
    color: #ff0000 !important;
    border-color: #ff0000 !important;
}

/* Incident Detail Tabs */
.incident-detail-tabs {
    display: flex;
    border-bottom: 1px solid #ff4444;
    margin-bottom: 15px;
}

.incident-detail-tab {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    border-bottom: none;
    padding: 8px 16px;
    font-family: inherit;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 2px;
}

.incident-detail-tab:hover {
    background: #330000;
    color: #ff0000;
}

.incident-detail-tab.active {
    background: #ff0000;
    color: #000000;
    border-color: #ff0000;
}

.incident-detail-content {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.tab-content {
    display: none;
    height: 100%;
    overflow-y: auto;
    padding-bottom: 20px;
}

.tab-content.active {
    display: block;
}

/* Tab Content Styling */
.incident-overview,
.incident-log,
.incident-links,
.incident-status {
    color: #ff4444;
}

.incident-overview h4,
.incident-log h4,
.incident-links h4,
.incident-status h4 {
    color: #ff0000;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.add-log-btn,
.attach-btn {
    background: #000000;
    color: #00ff00;
    border: 1px solid #00ff00;
    padding: 4px 8px;
    font-family: inherit;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-log-btn:hover,
.attach-btn:hover {
    background: #003300;
    color: #00ff00;
}

.log-entries {
    max-height: 300px;
    overflow-y: auto;
}

.log-entry {
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid #ff4444;
    background: #000000;
}

.log-timestamp {
    font-size: 11px;
    color: #888888;
    margin-bottom: 3px;
}

.log-content {
    color: #ff4444;
}

.links-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.link-category h5 {
    color: #ff0000;
    margin-bottom: 8px;
}

.linked-items {
    margin-top: 8px;
}

.linked-item {
    padding: 6px;
    margin-bottom: 5px;
    border: 1px solid #ff4444;
    background: #000000;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remove-link-btn {
    background: #660000;
    color: #ff4444;
    border: 1px solid #ff4444;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-link-btn:hover {
    background: #990000;
    color: #ff0000;
}

.status-timeline {
    max-height: 300px;
    overflow-y: auto;
}

.status-entry {
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid #ff4444;
    background: #000000;
}

.status-timestamp {
    font-size: 11px;
    color: #888888;
    margin-bottom: 3px;
}

.status-change {
    color: #ff4444;
    margin-bottom: 3px;
}

.status-user {
    font-size: 11px;
    color: #00ff00;
}

.no-links {
    color: #888888;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Incident Search Styles */
.incident-search-container {
    padding: 20px;
    color: #ff4444;
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 10px;
}

.search-header h2 {
    color: #ff0000;
    margin: 0;
}

.back-button {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    padding: 8px 16px;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.2s ease;
}

.back-button:hover {
    background: #330000;
    color: #ff0000;
}

.search-form {
    background: #111111;
    border: 1px solid #ff4444;
    padding: 20px;
    margin-bottom: 20px;
}

.search-filters {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    color: #ff0000;
    font-size: 12px;
    font-weight: bold;
}

.filter-group input,
.filter-group select {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    padding: 8px;
    font-family: inherit;
    font-size: 12px;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #ff0000;
    background: #110000;
}

.search-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: center;
}

.search-actions .action-button.primary {
    background: #004400;
    color: #00ff00;
    border-color: #00ff00;
}

.search-actions .action-button.primary:hover {
    background: #006600;
}

.search-results-section {
    background: #111111;
    border: 1px solid #ff4444;
    padding: 20px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 10px;
}

.results-header h3 {
    color: #ff0000;
    margin: 0;
}

.results-count {
    color: #888888;
    font-size: 12px;
}

.incident-search-results {
    max-height: 500px;
    overflow-y: auto;
}

.search-result-incident {
    background: #000000;
    border: 1px solid #ff4444;
    margin-bottom: 10px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-result-incident:hover {
    background: #110000;
    border-color: #ff0000;
}

.incident-summary {
    flex: 1;
}

.incident-header {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 8px;
}

.incident-number {
    color: #00ff00;
    font-weight: bold;
    font-size: 14px;
}

.incident-date {
    color: #888888;
    font-size: 11px;
}

.incident-priority {
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    border-radius: 2px;
}

.incident-priority.high {
    background: #660000;
    color: #ff0000;
}

.incident-priority.medium {
    background: #664400;
    color: #ffaa00;
}

.incident-priority.low {
    background: #004400;
    color: #00ff00;
}

.incident-type {
    color: #ff4444;
    font-weight: bold;
    margin-bottom: 5px;
}

.incident-location {
    color: #888888;
    font-size: 11px;
    margin-bottom: 5px;
}

.incident-description {
    color: #ff4444;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 5px;
}

.incident-status {
    color: #888888;
    font-size: 11px;
}

.incident-actions {
    margin-left: 15px;
}

.no-results {
    text-align: center;
    color: #888888;
    padding: 40px;
    font-style: italic;
}

.error {
    text-align: center;
    color: #ff0000;
    padding: 40px;
}

.loading {
    text-align: center;
    color: #ff4444;
    padding: 40px;
}

/* Responsive fixes for smaller screens */
@media (max-width: 1200px) {
    .incidents-main {
        grid-template-columns: 300px 1fr;
        gap: 10px;
        padding: 10px;
    }

    .list-filters {
        gap: 5px;
    }

    .list-filters select,
    .list-filters input {
        font-size: 10px;
        padding: 3px 5px;
    }

    .list-filters input[type="text"] {
        min-width: 80px;
        max-width: 150px;
    }

    .list-filters select {
        min-width: 60px;
        max-width: 100px;
    }

    .incident-actions .action-button {
        font-size: 10px;
        padding: 4px 6px;
        min-height: 28px;
    }
}

@media (max-width: 900px) {
    .incidents-main {
        grid-template-columns: 250px 1fr;
    }

    .list-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }

    .list-filters input[type="text"],
    .list-filters select {
        max-width: none;
    }
}

/* Record Linking Styles for Incident Creation */
.record-links-container {
    border: 1px solid #ff4444;
    background: #111111;
    padding: 15px;
    margin-top: 10px;
}

.link-section {
    margin-bottom: 20px;
}

.link-section:last-child {
    margin-bottom: 0;
}

.link-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.link-header span {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
}

.link-button {
    background: #000000;
    color: #00ff00;
    border: 1px solid #00ff00;
    padding: 4px 8px;
    font-family: inherit;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.link-button:hover {
    background: #003300;
    color: #00ff00;
}

.linked-records {
    min-height: 40px;
    max-height: 150px;
    overflow-y: auto;
}

.linked-record-item {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 8px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.linked-record-info {
    flex: 1;
    color: #ff4444;
    font-size: 12px;
}

.linked-record-type {
    color: #888888;
    font-size: 10px;
    margin-bottom: 2px;
}

.linked-record-details {
    color: #ff4444;
}

.remove-link {
    background: #660000;
    color: #ff4444;
    border: 1px solid #ff4444;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-link:hover {
    background: #990000;
    color: #ff0000;
}

.no-links {
    color: #888888;
    font-style: italic;
    text-align: center;
    padding: 15px;
    font-size: 12px;
}

.incidents-actions {
    display: flex;
    gap: 10px;
    padding: 15px 20px;
    border-top: 1px solid #ff0000;
    background: #000000;
}

.action-button {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    padding: 8px 16px;
    font-family: inherit;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background: #330000;
    color: #ff0000;
    border-color: #ff0000;
}

.action-button.primary {
    background: #ff0000;
    color: #000000;
    border-color: #ff0000;
}

.action-button.primary:hover {
    background: #ff4444;
}

.loading {
    text-align: center;
    color: #ffff00;
    padding: 20px;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .incidents-main {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .incidents-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .list-filters {
        flex-direction: column;
        gap: 5px;
    }

    .incidents-actions {
        flex-wrap: wrap;
        gap: 5px;
    }

    .action-button {
        flex: 1;
        min-width: 120px;
    }
}

/* Geocoding Status Styling */
.geocode-status {
    font-size: 12px;
    margin-left: 10px;
    padding: 4px 8px;
    border-radius: 3px;
    font-family: inherit;
}

.geocode-status.loading {
    color: #ffff00;
    background: rgba(255, 255, 0, 0.1);
    border: 1px solid #ffff00;
}

.geocode-status.success {
    color: #00ff00;
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid #00ff00;
}

.geocode-status.error {
    color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff0000;
}

/* Address Form Styling */
.form-section h3 {
    color: #ff0000;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.form-group label {
    color: #ff4444;
    font-size: 12px;
    margin-bottom: 5px;
    display: block;
}

.form-group input[readonly] {
    background: #111111;
    color: #888888;
    border-color: #444444;
}

.form-group select {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    font-family: inherit;
    font-size: 12px;
    padding: 6px 8px;
}

.form-group select:focus {
    outline: none;
    border-color: #ff0000;
    color: #ff0000;
}

.secondary-button {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    padding: 8px 16px;
    font-family: inherit;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondary-button:hover {
    background: #330000;
    color: #ff0000;
    border-color: #ff0000;
}

/* Incident form specific styling */
.incident-creation-form .form-field {
    display: inline-block;
    width: auto;
    margin-right: 20px;
}

.incident-creation-form .form-field label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
}

.incident-creation-form .form-actions {
    margin-top: 30px;
    padding: 20px;
    border-top: 1px solid #333;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.incident-creation-content .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #333333;
}

.incident-creation-form {
    max-height: none;
    margin-bottom: 40px;
}

.incident-creation-form .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    align-items: flex-start;
}

.incident-creation-form .form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.incident-creation-form .form-group.full-width {
    flex: 100%;
}

/* File Upload Styles - Retro theme compatible */
.file-upload-area {
    margin-top: 15px;
    border: 1px solid #333333;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
}

.upload-zone {
    border: 1px dashed #333333;
    padding: 30px 20px;
    text-align: center;
    background: rgba(0, 255, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.upload-zone:hover,
.upload-zone.drag-over {
    border-color: #00ff00;
    background: rgba(0, 255, 0, 0.1);
}

.upload-icon {
    font-size: 36px;
    margin-bottom: 10px;
    opacity: 0.7;
}

.uploaded-files {
    margin-top: 15px;
    display: none;
}

.uploaded-file {
    display: flex;
    align-items: center;
    background: rgba(0, 255, 0, 0.05);
    border: 1px solid #333333;
    padding: 10px;
    margin-bottom: 8px;
    gap: 10px;
}

.file-preview {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.file-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-info {
    flex: 1;
}

.file-name {
    color: #ffffff;
    font-size: 12px;
    margin-bottom: 2px;
    word-break: break-all;
}

.file-size {
    color: #888888;
    font-size: 10px;
    margin-bottom: 2px;
}

.file-status {
    color: #00ff00;
    font-size: 10px;
}

.remove-file-btn {
    background: #ff0000;
    border: none;
    color: #ffffff;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
}

.remove-file-btn:hover {
    background: #ff4444;
}

.link-button {
    background: none;
    border: none;
    color: #00ff00;
    text-decoration: underline;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
}

.link-button:hover {
    color: #44ff44;
}

/* Attachment Display Styles */
.no-attachments {
    color: #888888;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.attachment-item {
    display: flex;
    align-items: center;
    background: rgba(0, 255, 0, 0.05);
    border: 1px solid #333333;
    padding: 10px;
    margin-bottom: 8px;
    gap: 10px;
}

.attachment-icon {
    font-size: 24px;
    width: 40px;
    text-align: center;
}

.attachment-info {
    flex: 1;
}

.attachment-name {
    color: #ffffff;
    font-weight: bold;
    font-size: 12px;
    margin-bottom: 2px;
}

.attachment-details {
    color: #888888;
    font-size: 10px;
    margin-bottom: 2px;
}

.attachment-description {
    color: #cccccc;
    font-size: 10px;
    font-style: italic;
}

.attachment-actions {
    display: flex;
    gap: 5px;
}

.action-button {
    background: #00ff00;
    color: #000000;
    border: 1px solid #00ff00;
    padding: 4px 8px;
    font-size: 10px;
    cursor: pointer;
    font-family: inherit;
}

.action-button:hover {
    background: #44ff44;
    border-color: #44ff44;
}
