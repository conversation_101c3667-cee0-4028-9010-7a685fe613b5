-- Migrate Permission Functions to Core Schema
-- This script moves the permission functions from public to core schema for better organization

-- Step 1: Drop existing functions from public schema
DROP FUNCTION IF EXISTS public.get_user_roles(UUID);
DROP FUNCTION IF EXISTS public.get_user_permissions(UUID);
DROP FUNCTION IF EXISTS public.has_permission(TEXT);
DROP FUNCTION IF EXISTS public.refresh_user_claims(UUID);
DROP FUNCTION IF EXISTS public.refresh_user_permissions(UUID);
DROP FUNCTION IF EXISTS public.assign_user_role(UUID, TEXT, UUID);
DROP FUNCTION IF EXISTS public.remove_user_role(UUID, TEXT);
DROP FUNCTION IF EXISTS public.get_users_with_roles();
DROP FUNCTION IF EXISTS public.assign_permission_to_role(TEXT, TEXT, UUID);
DROP FUNCTION IF EXISTS public.remove_permission_from_role(TEXT, TEXT);

-- Step 2: Create functions in core schema

-- Function to get user roles
CREATE OR REPLACE FUNCTION core.get_user_roles(user_uuid UUID DEFAULT auth.uid())
RETURNS TABLE(role_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RETURN QUERY
        SELECT r.name
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = user_uuid;
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Function to get user permissions
CREATE OR REPLACE FUNCTION core.get_user_permissions(user_uuid UUID DEFAULT auth.uid())
RETURNS TABLE(permission_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RETURN QUERY
        SELECT DISTINCT p.name
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        JOIN core.role_permissions rp ON r.id = rp.role_id
        JOIN core.permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = user_uuid;
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION core.has_permission(permission_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        JOIN core.role_permissions rp ON r.id = rp.role_id
        JOIN core.permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = auth.uid() AND p.name = permission_name
    );
END;
$$;

-- Function to refresh user permissions in JWT
CREATE OR REPLACE FUNCTION core.refresh_user_permissions(user_uuid UUID DEFAULT auth.uid())
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_roles TEXT[];
    user_permissions TEXT[];
    result JSON;
BEGIN
    -- Get user roles
    SELECT ARRAY_AGG(role_name) INTO user_roles
    FROM core.get_user_roles(user_uuid);
    
    -- Get user permissions
    SELECT ARRAY_AGG(permission_name) INTO user_permissions
    FROM core.get_user_permissions(user_uuid);
    
    -- Build result
    result := json_build_object(
        'roles', COALESCE(user_roles, ARRAY[]::TEXT[]),
        'permissions', COALESCE(user_permissions, ARRAY[]::TEXT[])
    );
    
    -- Update user's app_metadata with roles and permissions
    UPDATE auth.users 
    SET raw_app_meta_data = jsonb_set(
        COALESCE(raw_app_meta_data, '{}'::jsonb),
        '{roles,permissions}',
        result::jsonb
    )
    WHERE id = user_uuid;
    
    RETURN result;
END;
$$;

-- Function to assign role to user (admin only)
CREATE OR REPLACE FUNCTION core.assign_user_role(
    target_user_id UUID,
    role_name TEXT,
    assigned_by UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    role_id UUID;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Get the role ID
    SELECT id INTO role_id FROM core.roles WHERE name = role_name;
    IF role_id IS NULL THEN
        RAISE EXCEPTION 'Role not found: %', role_name;
    END IF;
    
    -- Insert or update the role assignment
    INSERT INTO core.user_roles (user_id, role_id, assigned_by)
    VALUES (target_user_id, role_id, assigned_by)
    ON CONFLICT (user_id, role_id) 
    DO UPDATE SET 
        assigned_by = EXCLUDED.assigned_by;
    
    -- Refresh the user's permissions in JWT
    PERFORM core.refresh_user_permissions(target_user_id);
    
    RETURN TRUE;
END;
$$;

-- Function to remove role from user (admin only)
CREATE OR REPLACE FUNCTION core.remove_user_role(
    target_user_id UUID,
    role_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    role_id UUID;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Get the role ID
    SELECT id INTO role_id FROM core.roles WHERE name = role_name;
    IF role_id IS NULL THEN
        RAISE EXCEPTION 'Role not found: %', role_name;
    END IF;
    
    -- Remove the role assignment
    DELETE FROM core.user_roles 
    WHERE user_id = target_user_id AND role_id = role_id;
    
    -- Refresh the user's permissions in JWT
    PERFORM core.refresh_user_permissions(target_user_id);
    
    RETURN TRUE;
END;
$$;

-- Function to get all users with their roles and permissions (for admin interface)
CREATE OR REPLACE FUNCTION core.get_users_with_roles()
RETURNS TABLE(
    user_id UUID,
    email TEXT,
    full_name TEXT,
    roles TEXT[],
    permissions TEXT[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.email,
        COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name') as full_name,
        ARRAY_AGG(DISTINCT r.name) as roles,
        ARRAY_AGG(DISTINCT p.name) as permissions
    FROM auth.users u
    LEFT JOIN core.user_roles ur ON u.id = ur.user_id
    LEFT JOIN core.roles r ON ur.role_id = r.id
    LEFT JOIN core.role_permissions rp ON r.id = rp.role_id
    LEFT JOIN core.permissions p ON rp.permission_id = p.id
    WHERE u.email LIKE '%@iharc.ca'
    GROUP BY u.id, u.email, u.raw_user_meta_data
    ORDER BY u.email;
END;
$$;

-- Function to assign permission to role (admin only)
CREATE OR REPLACE FUNCTION core.assign_permission_to_role(
    role_name TEXT,
    permission_name TEXT,
    assigned_by UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    role_id UUID;
    permission_id UUID;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Get the role ID
    SELECT id INTO role_id FROM core.roles WHERE name = role_name;
    IF role_id IS NULL THEN
        RAISE EXCEPTION 'Role not found: %', role_name;
    END IF;
    
    -- Get the permission ID
    SELECT id INTO permission_id FROM core.permissions WHERE name = permission_name;
    IF permission_id IS NULL THEN
        RAISE EXCEPTION 'Permission not found: %', permission_name;
    END IF;
    
    -- Insert the role-permission assignment
    INSERT INTO core.role_permissions (role_id, permission_id, assigned_by)
    VALUES (role_id, permission_id, assigned_by)
    ON CONFLICT (role_id, permission_id) DO NOTHING;
    
    RETURN TRUE;
END;
$$;

-- Function to remove permission from role (admin only)
CREATE OR REPLACE FUNCTION core.remove_permission_from_role(
    role_name TEXT,
    permission_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    role_id UUID;
    permission_id UUID;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Get the role ID
    SELECT id INTO role_id FROM core.roles WHERE name = role_name;
    IF role_id IS NULL THEN
        RAISE EXCEPTION 'Role not found: %', role_name;
    END IF;
    
    -- Get the permission ID
    SELECT id INTO permission_id FROM core.permissions WHERE name = permission_name;
    IF permission_id IS NULL THEN
        RAISE EXCEPTION 'Permission not found: %', permission_name;
    END IF;
    
    -- Remove the role-permission assignment
    DELETE FROM core.role_permissions 
    WHERE role_id = role_id AND permission_id = permission_id;
    
    RETURN TRUE;
END;
$$;

-- Step 3: Grant execute permissions on core functions
GRANT EXECUTE ON FUNCTION core.get_user_roles(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION core.get_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION core.has_permission(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION core.refresh_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION core.assign_user_role(UUID, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION core.remove_user_role(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION core.get_users_with_roles() TO authenticated;
GRANT EXECUTE ON FUNCTION core.assign_permission_to_role(TEXT, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION core.remove_permission_from_role(TEXT, TEXT) TO authenticated;

-- Step 4: Test the functions
SELECT 'Testing core schema functions...' as info;

-- Test get_user_<NAME_EMAIL>
SELECT 'Testing core.get_user_roles:' as test;
SELECT core.get_user_roles(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Test get_user_<NAME_EMAIL>
SELECT 'Testing core.get_user_permissions:' as test;
SELECT core.get_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Test has_<NAME_EMAIL>
SELECT 'Testing core.has_permission:' as test;
SELECT core.has_permission('admin.access');

-- Step 5: Refresh <NAME_EMAIL>
SELECT 'Refreshing user permissions...' as info;
SELECT core.refresh_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Step 6: Verify the fix
SELECT 'Verification - User metadata after refresh:' as info;
SELECT 
    id,
    email,
    raw_app_meta_data
FROM auth.users 
WHERE email = '<EMAIL>'; 