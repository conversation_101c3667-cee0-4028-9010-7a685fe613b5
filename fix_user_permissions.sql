-- Fix User <NAME_EMAIL>
-- This script ensures the user has the correct role and permissions

-- Step 1: Check current state
SELECT 'Current user state:' as info;
SELECT 
    id,
    email,
    raw_app_meta_data,
    raw_user_meta_data
FROM auth.users 
WHERE email = '<EMAIL>';

-- Step 2: Check if roles exist
SELECT 'Available roles:' as info;
SELECT * FROM core.roles ORDER BY name;

-- Step 3: Check if permissions exist
SELECT 'Available permissions:' as info;
SELECT * FROM core.permissions ORDER BY category, name;

-- Step 4: Check current user roles
SELECT 'Current user roles:' as info;
SELECT 
    ur.id,
    u.email,
    r.name as role_name
FROM core.user_roles ur
JOIN auth.users u ON ur.user_id = u.id
JOIN core.roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';

-- Step 5: Create admin role if it doesn't exist
INSERT INTO core.roles (name, display_name, description, is_system_role)
VALUES 
    ('iharc_admin', 'IHARC Administrator', 'Full system administrator with all permissions', true),
    ('iharc_staff', 'IHARC Staff', 'Standard staff member with basic permissions', true),
    ('iharc_supervisor', 'IHARC Supervisor', 'Supervisor with enhanced permissions', true),
    ('iharc_volunteer', 'IHARC Volunteer', 'Volunteer with limited permissions', true)
ON CONFLICT (name) DO NOTHING;

-- Step 6: Create permissions if they don't exist
INSERT INTO core.permissions (name, description, category)
VALUES 
    -- Admin permissions
    ('admin.access', 'Access to admin interface', 'admin'),
    ('users.read', 'Read user information', 'users'),
    ('users.create', 'Create new users', 'users'),
    ('users.update', 'Update user information', 'users'),
    ('users.delete', 'Delete users', 'users'),
    
    -- System permissions
    ('items.manage', 'Manage system items', 'system'),
    ('system.manage', 'Manage system settings', 'system'),
    ('security.monitoring', 'Access security monitoring', 'system'),
    
    -- People permissions
    ('people.read', 'Read people records', 'people'),
    ('people.create', 'Create people records', 'people'),
    ('people.update', 'Update people records', 'people'),
    
    -- Incident permissions
    ('incidents.read', 'Read incident records', 'incidents'),
    ('incidents.create', 'Create incident records', 'incidents'),
    ('incidents.update', 'Update incident records', 'incidents'),
    
    -- Property permissions
    ('property.read', 'Read property records', 'property'),
    ('property.create', 'Create property records', 'property'),
    ('property.update', 'Update property records', 'property'),
    
    -- Report permissions
    ('reports.read', 'Read reports', 'reports'),
    ('reports.create', 'Create reports', 'reports')
ON CONFLICT (name) DO NOTHING;

-- Step 7: Assign permissions to admin role
INSERT INTO core.role_permissions (role_id, permission_id)
SELECT 
    r.id as role_id,
    p.id as permission_id
FROM core.roles r
CROSS JOIN core.permissions p
WHERE r.name = 'iharc_admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Step 8: Assign admin <NAME_EMAIL>
DO $$
DECLARE
    admin_user_id UUID;
    admin_role_id UUID;
BEGIN
    -- Find the user <NAME_EMAIL>
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    -- Find the admin role
    SELECT id INTO admin_role_id 
    FROM core.roles 
    WHERE name = 'iharc_admin';
    
    IF admin_user_id IS NOT NULL AND admin_role_id IS NOT NULL THEN
        -- Remove any existing roles for this user
        DELETE FROM core.user_roles WHERE user_id = admin_user_id;
        
        -- Assign admin <NAME_EMAIL>
        INSERT INTO core.user_roles (user_id, role_id, assigned_by)
        VALUES (admin_user_id, admin_role_id, admin_user_id);
        
        RAISE NOTICE 'Admin role <NAME_EMAIL> (User ID: %, Role ID: %)', admin_user_id, admin_role_id;
    ELSE
        RAISE NOTICE 'User <EMAIL> or admin role not found. User ID: %, Role ID: %', admin_user_id, admin_role_id;
    END IF;
END $$;

-- Step 9: Refresh the user's permissions in JWT
SELECT 'Refreshing user permissions...' as info;
SELECT public.refresh_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Step 10: Verify the fix
SELECT 'Verification - User roles:' as info;
SELECT 
    ur.id,
    u.email,
    r.name as role_name
FROM core.user_roles ur
JOIN auth.users u ON ur.user_id = u.id
JOIN core.roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';

SELECT 'Verification - User permissions:' as info;
SELECT DISTINCT
    p.name as permission_name,
    p.category
FROM core.user_roles ur
JOIN core.roles r ON ur.role_id = r.id
JOIN core.role_permissions rp ON r.id = rp.role_id
JOIN core.permissions p ON rp.permission_id = p.id
JOIN auth.users u ON ur.user_id = u.id
WHERE u.email = '<EMAIL>'
ORDER BY p.category, p.name;

SELECT 'Verification - Updated user metadata:' as info;
SELECT 
    id,
    email,
    raw_app_meta_data
FROM auth.users 
WHERE email = '<EMAIL>'; 