# S.T.E.V.I Retro PowerShell Launcher
Write-Host ""
Write-Host "===============================================" -ForegroundColor Red
Write-Host " S.T.E.V.I Retro - Desktop Application Launcher" -ForegroundColor Red
Write-Host "===============================================" -ForegroundColor Red
Write-Host ""

# Set window title
$Host.UI.RawUI.WindowTitle = "S.T.E.V.I Retro Launcher"

# Check if Node.js is installed
try {
    $nodeVersion = node --version 2>$null
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js 18+ from https://nodejs.org" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: package.json not found" -ForegroundColor Red
    Write-Host "Please run this script from the S.T.E.V.I Retro directory" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if dependencies are installed
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Launch the application
Write-Host "Launching S.T.E.V.I Retro..." -ForegroundColor Green
Write-Host ""

try {
    npm start
} catch {
    Write-Host "ERROR: Failed to start application" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}

# If we get here, the app has closed
Write-Host ""
Write-Host "S.T.E.V.I Retro has closed." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
