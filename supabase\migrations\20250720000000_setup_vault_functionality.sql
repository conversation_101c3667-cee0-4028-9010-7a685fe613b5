-- Enable the Vault extension if not already enabled
CREATE EXTENSION IF NOT EXISTS vault WITH SCHEMA vault;

-- Create a function to get a secret by name
CREATE OR REPLACE FUNCTION public.get_secret_by_name(secret_name text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  secret_record RECORD;
BEGIN
  -- First try to get the secret from the vault
  SELECT * INTO secret_record 
  FROM vault.secrets 
  WHERE name = secret_name 
  LIMIT 1;
  
  -- If found, return the decrypted secret
  IF FOUND THEN
    RETURN convert_from(
      pgsodium.crypto_aead_det_decrypt(
        secret_record.secret,
        convert_to(secret_record.key_id::text, 'utf8'),
        secret_record.key_id,
        secret_record.nonce
      ),
      'utf8'
    );
  END IF;
  
  -- If not found, return NULL
  RETURN NULL;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_secret_by_name(text) TO authenticated;
