// Outreach Transaction Manager - POS-style interface for outreach transactions
export class OutreachTransactionManager {
    constructor(dataManager, uiManager) {
        this.data = dataManager;
        this.ui = uiManager;
        this.selectedPerson = null;
        this.transactionItems = [];
        this.currentLocation = null;
        
        this.init();
    }

    init() {
        // Set up event listeners
        this.setupEventListeners();
        
        // Initialize search functionality
        this.initializeSearch();
    }

    setupEventListeners() {
        // Person search input
        const personSearch = document.getElementById('person-search');
        if (personSearch) {
            personSearch.addEventListener('input', this.debounce(() => {
                this.searchPerson();
            }, 300));
            
            personSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchPerson();
                }
            });
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    initializeSearch() {
        // Focus on person search input
        const personSearch = document.getElementById('person-search');
        if (personSearch) {
            personSearch.focus();
        }
    }

    async searchPerson() {
        const searchInput = document.getElementById('person-search');
        const resultsContainer = document.getElementById('person-results');
        
        if (!searchInput || !resultsContainer) return;
        
        const query = searchInput.value.trim();
        
        if (query.length < 2) {
            resultsContainer.innerHTML = '<div class="no-selection">Enter at least 2 characters to search</div>';
            return;
        }

        try {
            resultsContainer.innerHTML = '<div class="loading">Searching...</div>';
            
            // Search people by name, phone, or other identifiers
            const people = await this.data.search('people', {});
            
            // Filter results based on query
            const filteredPeople = people.filter(person => {
                const searchFields = [
                    person.first_name,
                    person.last_name,
                    person.phone,
                    person.email,
                    `${person.first_name} ${person.last_name}`
                ].filter(Boolean).map(field => field.toLowerCase());
                
                return searchFields.some(field => field.includes(query.toLowerCase()));
            });

            if (filteredPeople.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <p>No people found matching "${query}"</p>
                        <button class="primary-button" onclick="outreachTransaction.showAddPersonModal('${query}')">
                            Create New Person
                        </button>
                    </div>
                `;
                return;
            }

            // Display search results
            const resultsHTML = filteredPeople.map(person => `
                <div class="person-result" onclick="outreachTransaction.selectPerson('${person.id}')">
                    <div class="person-name">${person.first_name} ${person.last_name}</div>
                    <div class="person-details">
                        ${person.phone ? `📞 ${person.phone}` : ''}
                        ${person.email ? `📧 ${person.email}` : ''}
                    </div>
                    <div class="person-id">ID: ${person.id.substring(0, 8)}...</div>
                </div>
            `).join('');

            resultsContainer.innerHTML = resultsHTML;

        } catch (error) {
            console.error('Error searching people:', error);
            resultsContainer.innerHTML = '<div class="error">Error searching people. Please try again.</div>';
        }
    }

    async selectPerson(personId) {
        try {
            const person = await this.data.get('people', personId);
            if (!person) {
                this.ui.showDialog('Error', 'Person not found', 'error');
                return;
            }

            this.selectedPerson = person;
            
            // Update UI to show selected person
            const selectedPersonContainer = document.getElementById('selected-person');
            const personResults = document.getElementById('person-results');
            const addItemBtn = document.getElementById('add-item-btn');
            
            if (selectedPersonContainer && personResults) {
                selectedPersonContainer.style.display = 'block';
                personResults.style.display = 'none';

                const personInfoElement = selectedPersonContainer.querySelector('.person-info');
                if (personInfoElement) {
                    personInfoElement.innerHTML = `
                        <div class="selected-person-info">
                            <h4>${person.first_name} ${person.last_name}</h4>
                            <div class="person-contact">
                                ${person.phone ? `📞 ${person.phone}` : ''}
                                ${person.email ? `📧 ${person.email}` : ''}
                            </div>
                            <div class="person-id">ID: ${person.id.substring(0, 8)}...</div>
                        </div>
                    `;
                }
            }

            // Enable add item button
            if (addItemBtn) {
                addItemBtn.disabled = false;
            }

            // Enable completion buttons
            this.updateCompletionButtons();
            
            this.ui.setStatus(`Selected person: ${person.first_name} ${person.last_name}`, 'success');

        } catch (error) {
            console.error('Error selecting person:', error);
            this.ui.showDialog('Error', `Failed to select person: ${error.message}`, 'error');
        }
    }

    clearPersonSelection() {
        this.selectedPerson = null;
        
        const selectedPersonContainer = document.getElementById('selected-person');
        const personResults = document.getElementById('person-results');
        const addItemBtn = document.getElementById('add-item-btn');
        const personSearch = document.getElementById('person-search');
        
        if (selectedPersonContainer) selectedPersonContainer.style.display = 'none';
        if (personResults) personResults.style.display = 'block';
        if (addItemBtn) addItemBtn.disabled = true;
        if (personSearch) {
            personSearch.value = '';
            personSearch.focus();
        }

        // Clear items list
        this.transactionItems = [];
        this.updateItemsList();
        this.updateCompletionButtons();
        
        this.ui.setStatus('Person selection cleared', 'info');
    }

    async showAddPersonModal(prefillName = '') {
        // This will be implemented to show a modal for creating a new person
        // For now, show a placeholder
        this.ui.showDialog(
            'Add New Person',
            'Person creation modal will be implemented here.\n\n' +
            `Pre-filled name: ${prefillName}`,
            'info'
        );
    }

    async showItemCatalog() {
        if (!this.selectedPerson) {
            this.ui.showDialog('Error', 'Please select a person first', 'warning');
            return;
        }

        try {
            // Get available items from database
            const items = await this.data.search('items', { active: true });
            
            if (items.length === 0) {
                this.ui.showDialog('No Items', 'No active items found in inventory', 'warning');
                return;
            }

            // Create item catalog modal
            this.showItemCatalogModal(items);

        } catch (error) {
            console.error('Error loading item catalog:', error);
            this.ui.showDialog('Error', `Failed to load item catalog: ${error.message}`, 'error');
        }
    }

    showItemCatalogModal(items) {
        // Group items by category
        const itemsByCategory = items.reduce((acc, item) => {
            const category = item.category || 'other';
            if (!acc[category]) acc[category] = [];
            acc[category].push(item);
            return acc;
        }, {});

        const categoriesHTML = Object.entries(itemsByCategory).map(([category, categoryItems]) => {
            const itemsHTML = categoryItems.map(item => `
                <div class="catalog-item ${item.current_stock <= 0 ? 'out-of-stock' : ''}"
                     onclick="outreachTransaction.selectCatalogItem('${item.id}')">
                    <div class="item-name">${item.name}</div>
                    <div class="item-description">${item.description || ''}</div>
                    <div class="item-stock">Stock: ${item.current_stock} ${item.unit_type}</div>
                    ${item.current_stock <= 0 ? '<div class="stock-warning">Out of Stock</div>' : ''}
                </div>
            `).join('');

            return `
                <div class="catalog-category">
                    <h4 class="category-title">${category.replace('_', ' ').toUpperCase()}</h4>
                    <div class="category-items">
                        ${itemsHTML}
                    </div>
                </div>
            `;
        }).join('');

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h3>📦 Select Item to Add</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="item-catalog">
                        ${categoriesHTML}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    async selectCatalogItem(itemId) {
        try {
            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            if (item.current_stock <= 0) {
                this.ui.showDialog('Out of Stock', `${item.name} is currently out of stock`, 'warning');
                return;
            }

            // Close catalog modal
            const modal = document.querySelector('.modal-overlay');
            if (modal) modal.remove();

            // Show quantity input modal
            this.showQuantityInputModal(item);

        } catch (error) {
            console.error('Error selecting catalog item:', error);
            this.ui.showDialog('Error', `Failed to select item: ${error.message}`, 'error');
        }
    }

    showQuantityInputModal(item) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>📦 Add ${item.name}</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="quantity-input-form">
                        <div class="item-info">
                            <div class="item-name">${item.name}</div>
                            <div class="item-description">${item.description || ''}</div>
                            <div class="item-stock">Available: ${item.current_stock} ${item.unit_type}</div>
                        </div>
                        <div class="form-group">
                            <label for="item-quantity">Quantity to distribute:</label>
                            <input type="number" id="item-quantity" min="1" max="${item.current_stock}" value="1" class="quantity-input">
                            <span class="unit-label">${item.unit_type}</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="primary-button" onclick="outreachTransaction.addItemToTransaction('${item.id}')">Add to Transaction</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Focus on quantity input
        const quantityInput = modal.querySelector('#item-quantity');
        if (quantityInput) {
            quantityInput.focus();
            quantityInput.select();
        }
    }

    async addItemToTransaction(itemId) {
        try {
            const quantityInput = document.getElementById('item-quantity');
            if (!quantityInput) return;

            const quantity = parseInt(quantityInput.value);
            if (isNaN(quantity) || quantity <= 0) {
                this.ui.showDialog('Invalid Quantity', 'Please enter a valid quantity', 'warning');
                return;
            }

            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            if (quantity > item.current_stock) {
                this.ui.showDialog('Insufficient Stock', `Only ${item.current_stock} ${item.unit_type} available`, 'warning');
                return;
            }

            // Check if item already exists in transaction
            const existingItemIndex = this.transactionItems.findIndex(transItem => transItem.item_id === itemId);

            if (existingItemIndex >= 0) {
                // Update existing item quantity
                const newQuantity = this.transactionItems[existingItemIndex].quantity + quantity;
                if (newQuantity > item.current_stock) {
                    this.ui.showDialog('Insufficient Stock',
                        `Total quantity would be ${newQuantity}, but only ${item.current_stock} ${item.unit_type} available`,
                        'warning');
                    return;
                }
                this.transactionItems[existingItemIndex].quantity = newQuantity;
            } else {
                // Add new item to transaction
                this.transactionItems.push({
                    item_id: itemId,
                    item_name: item.name,
                    unit_type: item.unit_type,
                    quantity: quantity,
                    category: item.category
                });
            }

            // Close modal
            const modal = document.querySelector('.modal-overlay');
            if (modal) modal.remove();

            // Update items list display
            this.updateItemsList();
            this.updateCompletionButtons();

            this.ui.setStatus(`Added ${quantity} ${item.unit_type} of ${item.name}`, 'success');

        } catch (error) {
            console.error('Error adding item to distribution:', error);
            this.ui.showDialog('Error', `Failed to add item: ${error.message}`, 'error');
        }
    }

    updateItemsList() {
        const itemsList = document.getElementById('items-list');
        const itemsTotal = document.getElementById('items-total');
        
        if (!itemsList) return;

        if (this.transactionItems.length === 0) {
            itemsList.innerHTML = '<div class="no-items">No items added yet</div>';
            if (itemsTotal) itemsTotal.style.display = 'none';
            return;
        }

        const itemsHTML = this.transactionItems.map((item, index) => `
            <div class="transaction-item">
                <div class="item-details">
                    <div class="item-name">${item.item_name}</div>
                    <div class="item-category">${item.category.replace('_', ' ')}</div>
                </div>
                <div class="item-quantity">
                    <span class="quantity">${item.quantity}</span>
                    <span class="unit">${item.unit_type}</span>
                </div>
                <div class="item-actions">
                    <button class="edit-quantity-btn" onclick="outreachTransaction.editItemQuantity(${index})" title="Edit quantity">✏️</button>
                    <button class="remove-item-btn" onclick="outreachTransaction.removeItem(${index})" title="Remove item">🗑️</button>
                </div>
            </div>
        `).join('');

        itemsList.innerHTML = itemsHTML;

        // Update total
        if (itemsTotal) {
            const totalItems = this.transactionItems.reduce((sum, item) => sum + item.quantity, 0);
            document.getElementById('total-item-count').textContent = totalItems;
            itemsTotal.style.display = 'block';
        }
    }

    editItemQuantity(index) {
        const item = this.transactionItems[index];
        if (!item) return;

        const newQuantity = prompt(`Enter new quantity for ${item.item_name}:`, item.quantity);
        if (newQuantity === null) return;

        const quantity = parseInt(newQuantity);
        if (isNaN(quantity) || quantity <= 0) {
            this.ui.showDialog('Invalid Quantity', 'Please enter a valid quantity', 'warning');
            return;
        }

        // TODO: Check against current stock
        item.quantity = quantity;
        this.updateItemsList();
        this.ui.setStatus(`Updated quantity for ${item.item_name}`, 'success');
    }

    removeItem(index) {
        const item = this.transactionItems[index];
        if (!item) return;

        if (confirm(`Remove ${item.item_name} from transaction?`)) {
            this.transactionItems.splice(index, 1);
            this.updateItemsList();
            this.updateCompletionButtons();
            this.ui.setStatus(`Removed ${item.item_name} from transaction`, 'info');
        }
    }

    updateCompletionButtons() {
        const completeBtn = document.getElementById('complete-btn');
        const completeNewBtn = document.getElementById('complete-new-btn');

        const canComplete = this.selectedPerson && this.transactionItems.length > 0;

        if (completeBtn) completeBtn.disabled = !canComplete;
        if (completeNewBtn) completeNewBtn.disabled = !canComplete;
    }

    async getCurrentLocation() {
        try {
            const locationInput = document.getElementById('transaction-location');
            if (!locationInput) return;

            locationInput.value = 'Getting location...';
            locationInput.disabled = true;

            if (!navigator.geolocation) {
                throw new Error('Geolocation not supported');
            }

            const position = await new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, {
                    timeout: 10000,
                    enableHighAccuracy: true
                });
            });

            const { latitude, longitude } = position.coords;
            
            // Try to geocode the coordinates
            try {
                const address = await this.geocodeCoordinates(latitude, longitude);
                locationInput.value = address;
                this.currentLocation = {
                    address: address,
                    coordinates: `${latitude}, ${longitude}`
                };
            } catch (geocodeError) {
                console.warn('Geocoding failed:', geocodeError);
                locationInput.value = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
                this.currentLocation = {
                    address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                    coordinates: `${latitude}, ${longitude}`
                };
            }

            this.ui.setStatus('Location updated', 'success');

        } catch (error) {
            console.error('Error getting location:', error);
            const locationInput = document.getElementById('transaction-location');
            if (locationInput) {
                locationInput.value = '';
                this.ui.showDialog('Location Error', 'Could not get current location. Please enter manually.', 'warning');
            }
        } finally {
            const locationInput = document.getElementById('transaction-location');
            if (locationInput) locationInput.disabled = false;
        }
    }

    async geocodeCoordinates(lat, lng) {
        try {
            // Get Google API key from config
            const apiKey = window.app?.config?.get('google.apiKey');
            if (!apiKey) {
                console.warn('Google API key not available for geocoding');
                return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            }

            const response = await fetch(
                `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`
            );

            if (!response.ok) {
                throw new Error(`Geocoding request failed: ${response.status}`);
            }

            const data = await response.json();

            if (data.status === 'OK' && data.results.length > 0) {
                // Return the formatted address
                return data.results[0].formatted_address;
            } else {
                console.warn('Geocoding failed:', data.status);
                return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            }
        } catch (error) {
            console.error('Error geocoding coordinates:', error);
            return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        }
    }

    async completeTransaction() {
        if (!this.selectedPerson || this.transactionItems.length === 0) {
            this.ui.showDialog('Incomplete', 'Please select a person and add items before completing', 'warning');
            return;
        }

        try {
            await this.saveTransaction();
            this.ui.showDialog('Success', 'Outreach transaction completed successfully!', 'success');

            // Return to dashboard
            setTimeout(() => {
                window.app.goToDashboard();
            }, 2000);

        } catch (error) {
            console.error('Error completing transaction:', error);
            this.ui.showDialog('Error', `Failed to complete transaction: ${error.message}`, 'error');
        }
    }

    async completeAndNew() {
        if (!this.selectedPerson || this.transactionItems.length === 0) {
            this.ui.showDialog('Incomplete', 'Please select a person and add items before completing', 'warning');
            return;
        }

        try {
            await this.saveTransaction();
            this.ui.showDialog('Success', 'Outreach transaction completed! Ready for next transaction.', 'success');

            // Reset form for new transaction
            this.resetForm();

        } catch (error) {
            console.error('Error completing transaction:', error);
            this.ui.showDialog('Error', `Failed to complete transaction: ${error.message}`, 'error');
        }
    }

    async saveTransaction() {
        // Create activity record
        const activityData = {
            person_id: this.selectedPerson.id,
            activity_type: 'supply_provision',
            title: 'Outreach Transaction',
            description: document.getElementById('transaction-notes')?.value || '',
            location: document.getElementById('transaction-location')?.value || '',
            coordinates: this.currentLocation?.coordinates || null,
            activity_date: new Date().toISOString().split('T')[0],
            staff_member: 'Current User', // TODO: Get from auth
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        const activity = await this.data.insert('people_activities', activityData);

        // Create supply provision records and update inventory
        for (const item of this.transactionItems) {
            // Create supply provision record
            await this.data.insert('supply_provisions', {
                activity_id: activity.id,
                item_id: item.item_id,
                quantity_provided: item.quantity,
                notes: activityData.description,
                created_at: new Date().toISOString()
            });

            // Update item stock
            const currentItem = await this.data.get('items', item.item_id);
            const newStock = currentItem.current_stock - item.quantity;

            await this.data.update('items', item.item_id, {
                current_stock: newStock,
                updated_at: new Date().toISOString()
            });
        }
    }

    resetForm() {
        // Clear person selection
        this.clearPersonSelection();
        
        // Clear location and notes
        const locationInput = document.getElementById('transaction-location');
        const notesInput = document.getElementById('transaction-notes');

        if (locationInput) locationInput.value = '';
        if (notesInput) notesInput.value = '';

        // Reset current location
        this.currentLocation = null;

        this.ui.setStatus('Form reset for new transaction', 'info');
    }
}
