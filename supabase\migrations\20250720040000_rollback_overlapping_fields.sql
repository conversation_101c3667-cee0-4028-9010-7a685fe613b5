-- Rollback overlapping fields from people table and create proper linked tables
-- This migration removes fields that should be in separate linked tables

-- Remove overlapping fields from people table (keep only person-specific single-value fields)
ALTER TABLE core.people DROP COLUMN IF EXISTS mental_health_concerns;
ALTER TABLE core.people DROP COLUMN IF EXISTS medications;
ALTER TABLE core.people DROP COLUMN IF EXISTS chronic_conditions;
ALTER TABLE core.people DROP COLUMN IF EXISTS disabilities;
ALTER TABLE core.people DROP COLUMN IF EXISTS case_manager;
ALTER TABLE core.people DROP COLUMN IF EXISTS service_barriers;
ALTER TABLE core.people DROP COLUMN IF EXISTS support_network;

-- Keep these fields as they are person-specific and single-value:
-- preferred_pronouns, primary_language, has_id_documents, veteran_status, 
-- income_source, risk_level, last_service_date

-- Create disabilities table for multiple disability entries
CREATE TABLE IF NOT EXISTS core.disabilities (
    id BIGSERIAL PRIMARY KEY,
    person_id BIGINT NOT NULL REFERENCES core.people(id) ON DELETE CASCADE,
    disability_type TEXT NOT NULL,
    description TEXT,
    severity TEXT CHECK (severity IN ('Mild', 'Moderate', 'Severe')),
    accommodation_needs TEXT,
    diagnosed_date DATE,
    healthcare_provider TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT
);

-- Create case_management table for case management information
CREATE TABLE IF NOT EXISTS core.case_management (
    id BIGSERIAL PRIMARY KEY,
    person_id BIGINT NOT NULL REFERENCES core.people(id) ON DELETE CASCADE,
    case_manager_name TEXT NOT NULL,
    case_manager_contact TEXT,
    agency TEXT,
    case_number TEXT,
    start_date DATE,
    end_date DATE,
    status TEXT CHECK (status IN ('Active', 'Inactive', 'Transferred', 'Closed')),
    case_type TEXT,
    priority TEXT CHECK (priority IN ('Low', 'Medium', 'High', 'Urgent')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT
);

-- Create service_barriers table for multiple barriers
CREATE TABLE IF NOT EXISTS core.service_barriers (
    id BIGSERIAL PRIMARY KEY,
    person_id BIGINT NOT NULL REFERENCES core.people(id) ON DELETE CASCADE,
    barrier_type TEXT NOT NULL,
    description TEXT NOT NULL,
    severity TEXT CHECK (severity IN ('Low', 'Medium', 'High')),
    status TEXT CHECK (status IN ('Active', 'Resolved', 'In Progress')),
    identified_date DATE DEFAULT CURRENT_DATE,
    resolved_date DATE,
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT
);

-- Create support_contacts table for support network
CREATE TABLE IF NOT EXISTS core.support_contacts (
    id BIGSERIAL PRIMARY KEY,
    person_id BIGINT NOT NULL REFERENCES core.people(id) ON DELETE CASCADE,
    contact_name TEXT NOT NULL,
    relationship TEXT,
    contact_type TEXT CHECK (contact_type IN ('Family', 'Friend', 'Professional', 'Peer', 'Other')),
    phone TEXT,
    email TEXT,
    address TEXT,
    availability TEXT,
    notes TEXT,
    is_emergency_contact BOOLEAN DEFAULT FALSE,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_disabilities_person_id ON core.disabilities(person_id);
CREATE INDEX IF NOT EXISTS idx_case_management_person_id ON core.case_management(person_id);
CREATE INDEX IF NOT EXISTS idx_service_barriers_person_id ON core.service_barriers(person_id);
CREATE INDEX IF NOT EXISTS idx_support_contacts_person_id ON core.support_contacts(person_id);

-- Add comments for documentation
COMMENT ON TABLE core.disabilities IS 'Physical or cognitive disabilities for people';
COMMENT ON TABLE core.case_management IS 'Case management information for people';
COMMENT ON TABLE core.service_barriers IS 'Barriers to accessing services';
COMMENT ON TABLE core.support_contacts IS 'Support network contacts for people';

-- Add RLS policies (following existing patterns)
ALTER TABLE core.disabilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.case_management ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.service_barriers ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.support_contacts ENABLE ROW LEVEL SECURITY;

-- Create policies for disabilities table
CREATE POLICY "Enable read access for iharc_staff" ON core.disabilities
    FOR SELECT TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable insert for iharc_staff" ON core.disabilities
    FOR INSERT TO authenticated
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable update for iharc_staff" ON core.disabilities
    FOR UPDATE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable delete for iharc_staff" ON core.disabilities
    FOR DELETE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

-- Create policies for case_management table
CREATE POLICY "Enable read access for iharc_staff" ON core.case_management
    FOR SELECT TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable insert for iharc_staff" ON core.case_management
    FOR INSERT TO authenticated
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable update for iharc_staff" ON core.case_management
    FOR UPDATE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable delete for iharc_staff" ON core.case_management
    FOR DELETE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

-- Create policies for service_barriers table
CREATE POLICY "Enable read access for iharc_staff" ON core.service_barriers
    FOR SELECT TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable insert for iharc_staff" ON core.service_barriers
    FOR INSERT TO authenticated
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable update for iharc_staff" ON core.service_barriers
    FOR UPDATE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable delete for iharc_staff" ON core.service_barriers
    FOR DELETE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

-- Create policies for support_contacts table
CREATE POLICY "Enable read access for iharc_staff" ON core.support_contacts
    FOR SELECT TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable insert for iharc_staff" ON core.support_contacts
    FOR INSERT TO authenticated
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable update for iharc_staff" ON core.support_contacts
    FOR UPDATE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable delete for iharc_staff" ON core.support_contacts
    FOR DELETE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
