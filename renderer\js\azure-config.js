// Azure Blob Storage Configuration Manager
// Handles secure storage and retrieval of Azure credentials

import { ipc<PERSON>enderer } from 'electron';

export class AzureConfigManager {
    constructor() {
        this.config = null;
        this.isInitialized = false;
    }

    async initialize() {
        if (this.isInitialized) {
            return this.config;
        }

        try {
            // Get Azure configuration from main process (secure storage)
            this.config = await ipcRenderer.invoke('get-azure-config');
            this.isInitialized = true;
            return this.config;
        } catch (error) {
            console.error('Failed to initialize Azure configuration:', error);
            throw new Error('Azure configuration not available');
        }
    }

    getConfig() {
        if (!this.isInitialized || !this.config) {
            throw new Error('Azure configuration not initialized. Call initialize() first.');
        }
        return this.config;
    }

    getStorageAccountName() {
        return this.getConfig().storageAccountName;
    }

    getContainerName() {
        return this.getConfig().containerName;
    }

    getBlobEndpoint() {
        return this.getConfig().blobEndpoint;
    }

    getCredentials() {
        const config = this.getConfig();
        return {
            tenantId: config.tenantId,
            clientId: config.clientId,
            clientSecret: config.clientSecret
        };
    }

    // Generate a secure file path for uploaded files
    generateFilePath(incidentId, originalFileName) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileExtension = originalFileName.split('.').pop();
        const sanitizedName = originalFileName.replace(/[^a-zA-Z0-9.-]/g, '_');
        
        return `incidents/${incidentId}/${timestamp}_${sanitizedName}`;
    }

    // Generate a public URL for accessing files (with SAS token if needed)
    generateFileUrl(filePath) {
        const config = this.getConfig();
        return `${config.blobEndpoint}${config.containerName}/${filePath}`;
    }
}

// Singleton instance
export const azureConfig = new AzureConfigManager();
