-- Setup Permission System for S.T.E.V.I Retro
-- This migration creates the necessary functions and tables for role-based permissions

-- Create roles table
CREATE TABLE IF NOT EXISTS public.user_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role_name TEXT NOT NULL CHECK (role_name IN ('iharc_staff', 'iharc_admin', 'iharc_supervisor', 'iharc_volunteer')),
    granted_by UUID REFERENCES auth.users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, role_name)
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS public.role_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    role_name TEXT NOT NULL CHECK (role_name IN ('iharc_staff', 'iharc_admin', 'iharc_supervisor', 'iharc_volunteer')),
    permission_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_name, permission_name)
);

-- Insert default permissions for each role
INSERT INTO public.role_permissions (role_name, permission_name) VALUES
-- Admin permissions
('iharc_admin', 'admin.access'),
('iharc_admin', 'users.read'),
('iharc_admin', 'users.create'),
('iharc_admin', 'users.update'),
('iharc_admin', 'users.delete'),
('iharc_admin', 'items.manage'),
('iharc_admin', 'system.manage'),
('iharc_admin', 'security.monitoring'),

-- Staff permissions
('iharc_staff', 'people.read'),
('iharc_staff', 'people.create'),
('iharc_staff', 'people.update'),
('iharc_staff', 'incidents.read'),
('iharc_staff', 'incidents.create'),
('iharc_staff', 'incidents.update'),
('iharc_staff', 'property.read'),
('iharc_staff', 'property.create'),
('iharc_staff', 'property.update'),
('iharc_staff', 'reports.read'),
('iharc_staff', 'reports.create'),

-- Supervisor permissions (inherits staff + additional)
('iharc_supervisor', 'people.read'),
('iharc_supervisor', 'people.create'),
('iharc_supervisor', 'people.update'),
('iharc_supervisor', 'incidents.read'),
('iharc_supervisor', 'incidents.create'),
('iharc_supervisor', 'incidents.update'),
('iharc_supervisor', 'property.read'),
('iharc_supervisor', 'property.create'),
('iharc_supervisor', 'property.update'),
('iharc_supervisor', 'reports.read'),
('iharc_supervisor', 'reports.create'),
('iharc_supervisor', 'staff.manage'),

-- Volunteer permissions (limited)
('iharc_volunteer', 'people.read'),
('iharc_volunteer', 'incidents.read'),
('iharc_volunteer', 'property.read'),
('iharc_volunteer', 'reports.read')
ON CONFLICT (role_name, permission_name) DO NOTHING;

-- Function to get user roles
CREATE OR REPLACE FUNCTION public.get_user_roles(user_uuid UUID)
RETURNS TABLE(role_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM public.user_roles 
        WHERE user_id = auth.uid() AND role_name = 'iharc_admin'
    ) THEN
        RETURN QUERY
        SELECT ur.role_name
        FROM public.user_roles ur
        WHERE ur.user_id = user_uuid;
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Function to get user permissions
CREATE OR REPLACE FUNCTION public.get_user_permissions(user_uuid UUID)
RETURNS TABLE(permission_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM public.user_roles 
        WHERE user_id = auth.uid() AND role_name = 'iharc_admin'
    ) THEN
        RETURN QUERY
        SELECT DISTINCT rp.permission_name
        FROM public.user_roles ur
        JOIN public.role_permissions rp ON ur.role_name = rp.role_name
        WHERE ur.user_id = user_uuid;
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION public.has_permission(permission_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM public.user_roles ur
        JOIN public.role_permissions rp ON ur.role_name = rp.role_name
        WHERE ur.user_id = auth.uid() AND rp.permission_name = permission_name
    );
END;
$$;

-- Function to refresh user claims (updates JWT with current roles and permissions)
CREATE OR REPLACE FUNCTION public.refresh_user_claims(user_uuid UUID DEFAULT auth.uid())
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_roles TEXT[];
    user_permissions TEXT[];
    result JSON;
BEGIN
    -- Get user roles
    SELECT ARRAY_AGG(role_name) INTO user_roles
    FROM public.get_user_roles(user_uuid);
    
    -- Get user permissions
    SELECT ARRAY_AGG(permission_name) INTO user_permissions
    FROM public.get_user_permissions(user_uuid);
    
    -- Build result
    result := json_build_object(
        'roles', COALESCE(user_roles, ARRAY[]::TEXT[]),
        'permissions', COALESCE(user_permissions, ARRAY[]::TEXT[])
    );
    
    -- Update user's app_metadata with claims
    UPDATE auth.users 
    SET raw_app_meta_data = jsonb_set(
        COALESCE(raw_app_meta_data, '{}'::jsonb),
        '{claims}',
        result::jsonb
    )
    WHERE id = user_uuid;
    
    RETURN result;
END;
$$;

-- Function to assign role to user (admin only)
CREATE OR REPLACE FUNCTION public.assign_user_role(
    target_user_id UUID,
    role_name TEXT,
    granted_by UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM public.user_roles 
        WHERE user_id = auth.uid() AND role_name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Insert or update the role
    INSERT INTO public.user_roles (user_id, role_name, granted_by)
    VALUES (target_user_id, role_name, granted_by)
    ON CONFLICT (user_id, role_name) 
    DO UPDATE SET 
        granted_by = EXCLUDED.granted_by,
        granted_at = NOW();
    
    -- Refresh the user's claims
    PERFORM public.refresh_user_claims(target_user_id);
    
    RETURN TRUE;
END;
$$;

-- Function to remove role from user (admin only)
CREATE OR REPLACE FUNCTION public.remove_user_role(
    target_user_id UUID,
    role_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM public.user_roles 
        WHERE user_id = auth.uid() AND role_name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Remove the role
    DELETE FROM public.user_roles 
    WHERE user_id = target_user_id AND role_name = role_name;
    
    -- Refresh the user's claims
    PERFORM public.refresh_user_claims(target_user_id);
    
    RETURN TRUE;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_user_roles(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.has_permission(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_user_claims(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.assign_user_role(UUID, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.remove_user_role(UUID, TEXT) TO authenticated;

-- Grant table permissions
GRANT SELECT ON public.user_roles TO authenticated;
GRANT SELECT ON public.role_permissions TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.user_roles TO authenticated;

-- Enable RLS on tables
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;

-- RLS policies for user_roles
CREATE POLICY "Users can view their own roles" ON public.user_roles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all roles" ON public.user_roles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_roles 
            WHERE user_id = auth.uid() AND role_name = 'iharc_admin'
        )
    );

CREATE POLICY "Admins can manage roles" ON public.user_roles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.user_roles 
            WHERE user_id = auth.uid() AND role_name = 'iharc_admin'
        )
    );

-- RLS policies for role_permissions
CREATE POLICY "All authenticated users can view permissions" ON public.role_permissions
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create admin <NAME_EMAIL>
-- Note: This will be executed when the user exists in auth.users
-- You'll need to run this manually after the user is created:
-- SELECT public.assign_user_role('USER_UUID_HERE', 'iharc_admin'); 