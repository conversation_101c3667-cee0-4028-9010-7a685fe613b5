-- Fix Permissions System to Follow Supabase Best Practices
-- This migration removes custom roles and uses JW<PERSON> claims instead

-- Step 1: Drop existing functions that use custom roles
DROP FUNCTION IF EXISTS public.get_user_roles(UUID);
DROP FUNCTION IF EXISTS public.get_user_permissions(UUID);
DROP FUNCTION IF EXISTS public.has_permission(TEXT);
DROP FUNCTION IF EXISTS public.refresh_user_claims(UUID);
DROP FUNCTION IF EXISTS public.assign_user_role(UUID, TEXT, UUID);
DROP FUNCTION IF EXISTS public.remove_user_role(UUID, TEXT);
DROP FUNCTION IF EXISTS public.get_users_with_roles();

-- Step 2: Drop custom role tables
DROP TABLE IF EXISTS core.user_roles;
DROP TABLE IF EXISTS core.role_permissions;
DROP TABLE IF EXISTS core.roles;
DROP TABLE IF EXISTS core.permissions;

-- Step 3: Create a simple user_permissions table in core schema
CREATE TABLE IF NOT EXISTS core.user_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    permission_name TEXT NOT NULL,
    granted_by UUID REFERENCES auth.users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, permission_name)
);

-- Step 4: Insert default permissions for admin user (<EMAIL>)
DO $$
DECLARE
    admin_user_id UUID;
BEGIN
    -- Find the user <NAME_EMAIL>
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    IF admin_user_id IS NOT NULL THEN
        -- Insert admin permissions
        INSERT INTO core.user_permissions (user_id, permission_name, granted_by) VALUES
        (admin_user_id, 'admin.access', admin_user_id),
        (admin_user_id, 'users.read', admin_user_id),
        (admin_user_id, 'users.create', admin_user_id),
        (admin_user_id, 'users.update', admin_user_id),
        (admin_user_id, 'users.delete', admin_user_id),
        (admin_user_id, 'items.manage', admin_user_id),
        (admin_user_id, 'system.manage', admin_user_id),
        (admin_user_id, 'security.monitoring', admin_user_id),
        (admin_user_id, 'people.read', admin_user_id),
        (admin_user_id, 'people.create', admin_user_id),
        (admin_user_id, 'people.update', admin_user_id),
        (admin_user_id, 'incidents.read', admin_user_id),
        (admin_user_id, 'incidents.create', admin_user_id),
        (admin_user_id, 'incidents.update', admin_user_id),
        (admin_user_id, 'property.read', admin_user_id),
        (admin_user_id, 'property.create', admin_user_id),
        (admin_user_id, 'property.update', admin_user_id),
        (admin_user_id, 'reports.read', admin_user_id),
        (admin_user_id, 'reports.create', admin_user_id)
        ON CONFLICT (user_id, permission_name) DO NOTHING;
        
        RAISE NOTICE 'Admin permissions <NAME_EMAIL>';
    ELSE
        RAISE NOTICE 'User <EMAIL> not found';
    END IF;
END $$;

-- Step 5: Create simple functions that work with JWT claims

-- Function to get user permissions
CREATE OR REPLACE FUNCTION public.get_user_permissions(user_uuid UUID DEFAULT auth.uid())
RETURNS TABLE(permission_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM core.user_permissions 
        WHERE user_id = auth.uid() AND permission_name = 'admin.access'
    ) THEN
        RETURN QUERY
        SELECT up.permission_name
        FROM core.user_permissions up
        WHERE up.user_id = user_uuid;
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION public.has_permission(permission_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM core.user_permissions
        WHERE user_id = auth.uid() AND permission_name = permission_name
    );
END;
$$;

-- Function to assign permission to user (admin only)
CREATE OR REPLACE FUNCTION public.assign_user_permission(
    target_user_id UUID,
    permission_name TEXT,
    granted_by UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_permissions 
        WHERE user_id = auth.uid() AND permission_name = 'admin.access'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Insert or update the permission
    INSERT INTO core.user_permissions (user_id, permission_name, granted_by)
    VALUES (target_user_id, permission_name, granted_by)
    ON CONFLICT (user_id, permission_name) 
    DO UPDATE SET 
        granted_by = EXCLUDED.granted_by,
        granted_at = NOW();
    
    RETURN TRUE;
END;
$$;

-- Function to remove permission from user (admin only)
CREATE OR REPLACE FUNCTION public.remove_user_permission(
    target_user_id UUID,
    permission_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_permissions 
        WHERE user_id = auth.uid() AND permission_name = 'admin.access'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Remove the permission
    DELETE FROM core.user_permissions 
    WHERE user_id = target_user_id AND permission_name = permission_name;
    
    RETURN TRUE;
END;
$$;

-- Function to get all users with their permissions (for admin interface)
CREATE OR REPLACE FUNCTION public.get_users_with_permissions()
RETURNS TABLE(
    user_id UUID,
    email TEXT,
    full_name TEXT,
    permissions TEXT[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_permissions 
        WHERE user_id = auth.uid() AND permission_name = 'admin.access'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.email,
        COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name') as full_name,
        ARRAY_AGG(up.permission_name) as permissions
    FROM auth.users u
    LEFT JOIN core.user_permissions up ON u.id = up.user_id
    WHERE u.email LIKE '%@iharc.ca'
    GROUP BY u.id, u.email, u.raw_user_meta_data
    ORDER BY u.email;
END;
$$;

-- Function to refresh user permissions in JWT (updates app_metadata)
CREATE OR REPLACE FUNCTION public.refresh_user_permissions(user_uuid UUID DEFAULT auth.uid())
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_permissions TEXT[];
    result JSON;
BEGIN
    -- Get user permissions
    SELECT ARRAY_AGG(permission_name) INTO user_permissions
    FROM public.get_user_permissions(user_uuid);
    
    -- Build result
    result := json_build_object(
        'permissions', COALESCE(user_permissions, ARRAY[]::TEXT[])
    );
    
    -- Update user's app_metadata with permissions
    UPDATE auth.users 
    SET raw_app_meta_data = jsonb_set(
        COALESCE(raw_app_meta_data, '{}'::jsonb),
        '{permissions}',
        result::jsonb
    )
    WHERE id = user_uuid;
    
    RETURN result;
END;
$$;

-- Step 6: Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.has_permission(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.assign_user_permission(UUID, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.remove_user_permission(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_users_with_permissions() TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_user_permissions(UUID) TO authenticated;

-- Step 7: Grant table permissions
GRANT SELECT ON core.user_permissions TO authenticated;
GRANT INSERT, UPDATE, DELETE ON core.user_permissions TO authenticated;

-- Step 8: Enable RLS on table
ALTER TABLE core.user_permissions ENABLE ROW LEVEL SECURITY;

-- Step 9: Create RLS policies
CREATE POLICY "Users can view their own permissions" ON core.user_permissions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all permissions" ON core.user_permissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM core.user_permissions 
            WHERE user_id = auth.uid() AND permission_name = 'admin.access'
        )
    );

CREATE POLICY "Admins can manage permissions" ON core.user_permissions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM core.user_permissions 
            WHERE user_id = auth.uid() AND permission_name = 'admin.access'
        )
    );

-- Step 10: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_core_user_permissions_user_id ON core.user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_core_user_permissions_permission_name ON core.user_permissions(permission_name);

-- Step 11: Refresh permissions for admin user
SELECT public.refresh_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
); 