-- Debug Admin <NAME_EMAIL>
-- Run this in Supabase SQL Editor to check and fix admin permissions

-- Step 1: Check if the user exists
SELECT 
    id,
    email,
    raw_app_meta_data,
    raw_user_meta_data
FROM auth.users 
WHERE email = '<EMAIL>';

-- Step 2: Check if the admin role exists
SELECT * FROM core.roles WHERE name = 'iharc_admin';

-- Step 3: Check if the user has the admin role assigned
SELECT 
    ur.id,
    u.email,
    r.name as role_name,
    ur.assigned_at
FROM core.user_roles ur
JOIN auth.users u ON ur.user_id = u.id
JOIN core.roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';

-- Step 4: Check what permissions the admin role has
SELECT 
    r.name as role_name,
    p.name as permission_name,
    p.category
FROM core.roles r
JOIN core.role_permissions rp ON r.id = rp.role_id
JOIN core.permissions p ON rp.permission_id = p.id
WHERE r.name = 'iharc_admin'
ORDER BY p.category, p.name;

-- Step 5: Check what permissions the user should have (through roles)
SELECT DISTINCT
    p.name as permission_name,
    p.category
FROM core.user_roles ur
JOIN core.roles r ON ur.role_id = r.id
JOIN core.role_permissions rp ON r.id = rp.role_id
JOIN core.permissions p ON rp.permission_id = p.id
JOIN auth.users u ON ur.user_id = u.id
WHERE u.email = '<EMAIL>'
ORDER BY p.category, p.name;

-- Step 6: Force assign admin <NAME_EMAIL> if not already assigned
DO $$
DECLARE
    admin_user_id UUID;
    admin_role_id UUID;
BEGIN
    -- Find the user <NAME_EMAIL>
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    -- Find the admin role
    SELECT id INTO admin_role_id 
    FROM core.roles 
    WHERE name = 'iharc_admin';
    
    IF admin_user_id IS NOT NULL AND admin_role_id IS NOT NULL THEN
        -- Assign admin <NAME_EMAIL>
        INSERT INTO core.user_roles (user_id, role_id, assigned_by)
        VALUES (admin_user_id, admin_role_id, admin_user_id)
        ON CONFLICT (user_id, role_id) DO NOTHING;
        
        RAISE NOTICE 'Admin role <NAME_EMAIL> (User ID: %, Role ID: %)', admin_user_id, admin_role_id;
    ELSE
        RAISE NOTICE 'User <EMAIL> or admin role not found. User ID: %, Role ID: %', admin_user_id, admin_role_id;
    END IF;
END $$;

-- Step 7: Refresh the user's permissions in JWT
SELECT public.refresh_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Step 8: Check the updated user metadata
SELECT 
    id,
    email,
    raw_app_meta_data
FROM auth.users 
WHERE email = '<EMAIL>';

-- Step 9: Test the has_permission function
SELECT 
    'admin.access' as permission,
    public.has_permission('admin.access') as has_permission
UNION ALL
SELECT 
    'users.read' as permission,
    public.has_permission('users.read') as has_permission
UNION ALL
SELECT 
    'users.create' as permission,
    public.has_permission('users.create') as has_permission; 