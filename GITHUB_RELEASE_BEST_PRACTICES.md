# 📚 GitHub Release Best Practices - Explained

## 🤔 Your Question: Tag Strategy

You asked about using version number tags vs a single "stable" tag. Here's the industry standard approach:

## ✅ **Recommended: Standard Versioned Tags**

### **How It Works**
```bash
git tag v1.0.0    # First stable release
git tag v1.1.0    # Feature update
git tag v1.1.1    # Bug fix
git tag v2.0.0    # Major version
```

### **GitHub's "Latest Release" Feature**
- GitHub **automatically** marks the newest release as "Latest"
- You can **manually override** which release is "Latest" in the UI
- API endpoint `/releases/latest` returns the "Latest" marked release
- This is the **industry standard** approach

## 🔄 **How Your Update System Works Now**

### **Workflow Triggers**
```yaml
on:
  release:
    types: [published]  # Any published release
```

### **Stable Release Detection**
```yaml
# Only builds if:
- Not a draft release
- Not marked as "pre-release"
- Is a published release
```

### **Version Extraction**
```yaml
# Gets version from tag name:
v1.0.0 → 1.0.0
v2.1.3 → 2.1.3
1.0.0  → 1.0.0
```

## 🎯 **Your Release Workflow**

### **For Stable Releases**
1. **Create release**: Tag `v1.0.0`, not marked as pre-release
2. **GitHub marks as "Latest"**: Automatically (or manually)
3. **Workflow builds**: Because it's not a pre-release
4. **App checks**: Uses GitHub's `/releases/latest` endpoint

### **For Beta/Alpha Releases**
1. **Create release**: Tag `v1.1.0-beta`, **mark as pre-release**
2. **GitHub doesn't mark as "Latest"**: Pre-releases don't become "Latest"
3. **Workflow skips**: Because it's marked as pre-release
4. **App ignores**: Only checks "Latest" releases

## 🏆 **Benefits of This Approach**

### ✅ **Industry Standard**
- Follows semantic versioning (semver.org)
- Compatible with all package managers
- Expected by developers and tools

### ✅ **Version History**
- Clear progression: v1.0.0 → v1.1.0 → v2.0.0
- Easy to rollback to specific versions
- Git history shows all releases

### ✅ **Flexible Release Types**
- **Stable**: `v1.0.0` (not pre-release)
- **Beta**: `v1.1.0-beta` (marked as pre-release)
- **Alpha**: `v2.0.0-alpha` (marked as pre-release)
- **Release Candidate**: `v1.0.0-rc1` (marked as pre-release)

### ✅ **GitHub Integration**
- Works with GitHub's "Latest Release" feature
- Compatible with GitHub's API endpoints
- Integrates with dependency management tools

## 🔧 **Your Current Implementation**

### **Update System Flow**
1. **App startup**: Checks GitHub `/releases/latest` endpoint
2. **Gets latest stable**: Only non-pre-release versions
3. **Compares versions**: Uses semver comparison
4. **Downloads if newer**: From Azure Blob Storage

### **Build System Flow**
1. **Release published**: Any published release triggers workflow
2. **Checks if stable**: Skips if pre-release or draft
3. **Builds if stable**: Creates installer and uploads to Azure
4. **Updates pointers**: Updates latest-version.txt and metadata

## 📋 **Comparison of Approaches**

| Approach | Pros | Cons | Recommendation |
|----------|------|------|----------------|
| **Versioned Tags** (v1.0.0) | ✅ Industry standard<br>✅ Version history<br>✅ GitHub integration | ❌ More tags to manage | **✅ Recommended** |
| **Single "stable" Tag** | ✅ Simple<br>✅ Clear intent | ❌ Non-standard<br>❌ No version history<br>❌ Harder rollbacks | ❌ Not recommended |
| **Branch-based** | ✅ Simple workflow | ❌ No release management<br>❌ No version control | ❌ Not recommended |

## 🚀 **Your New Release Process**

### **Creating a Stable Release**
```bash
# 1. Tag the version
git tag v1.0.0
git push stevi_dos v1.0.0

# 2. Create release on GitHub
# - Tag: v1.0.0
# - Title: "S.T.E.V.I Retro v1.0.0 - Stable Release"
# - Pre-release: ❌ Unchecked
# - Draft: ❌ Unchecked
```

### **Creating a Beta Release**
```bash
# 1. Tag the version
git tag v1.1.0-beta
git push stevi_dos v1.1.0-beta

# 2. Create release on GitHub
# - Tag: v1.1.0-beta
# - Title: "S.T.E.V.I Retro v1.1.0 Beta"
# - Pre-release: ✅ Checked
# - Draft: ❌ Unchecked
```

## 🎉 **Result: Best of Both Worlds**

✅ **Industry standard** versioned tags
✅ **Only stable releases** trigger builds
✅ **Beta/alpha releases** don't interfere
✅ **GitHub "Latest"** works automatically
✅ **Version history** preserved
✅ **Easy rollbacks** to any version
✅ **Semantic versioning** compliance

## 🔍 **Testing Your Setup**

### **Test Stable Release**
1. Create release with tag `v1.0.0` (not pre-release)
2. Should trigger build and deployment
3. Should update Azure Blob Storage
4. Should be marked as "Latest" by GitHub

### **Test Beta Release**
1. Create release with tag `v1.1.0-beta` (mark as pre-release)
2. Should NOT trigger build
3. Should NOT update Azure Blob Storage
4. Should NOT be marked as "Latest" by GitHub

This approach gives you **professional release management** while maintaining **simple, secure updates** for your users! 🎯
