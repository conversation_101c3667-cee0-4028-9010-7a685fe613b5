// Encampment Manager for S.T.E.V.I DOS Electron App
export class EncampmentManager {
    constructor(dataManager, auth) {
        this.data = dataManager;
        this.auth = auth;
    }

    // Generate a unique encampment ID
    generateEncampmentId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // Create a new encampment
    async createEncampment(encampmentData) {
        try {
            const now = new Date().toISOString();
            const currentUser = this.auth.getCurrentUser();
            
            const encampment = {
                id: this.generateEncampmentId(),
                name: encampmentData.name,
                location: encampmentData.location,
                coordinates: encampmentData.coordinates || null,
                status: encampmentData.status || 'active',
                type: encampmentData.type || null,
                estimated_population: encampmentData.estimated_population || null,
                description: encampmentData.description || null,
                safety_concerns: encampmentData.safety_concerns || null,
                services_needed: encampmentData.services_needed || null,
                last_visited: encampmentData.last_visited || null,
                created_at: now,
                updated_at: now,
                created_by: currentUser?.email || 'system',
                updated_by: currentUser?.email || 'system'
            };

            const result = await this.data.insert('encampments', encampment);
            console.log('Encampment created:', result);
            return result;
        } catch (error) {
            console.error('Error creating encampment:', error);
            throw error;
        }
    }

    // Update an existing encampment
    async updateEncampment(id, encampmentData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            
            const updateData = {
                ...encampmentData,
                updated_at: new Date().toISOString(),
                updated_by: currentUser?.email || 'system'
            };

            const result = await this.data.update('encampments', id, updateData);
            console.log('Encampment updated:', result);
            return result;
        } catch (error) {
            console.error('Error updating encampment:', error);
            throw error;
        }
    }

    // Get all encampments
    async getAllEncampments() {
        try {
            const encampments = await this.data.getAll('encampments');
            return encampments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        } catch (error) {
            console.error('Error fetching encampments:', error);
            throw error;
        }
    }

    // Get a specific encampment by ID
    async getEncampmentById(id) {
        try {
            const encampment = await this.data.getById('encampments', id);
            return encampment;
        } catch (error) {
            console.error('Error fetching encampment:', error);
            throw error;
        }
    }

    // Delete an encampment
    async deleteEncampment(id) {
        try {
            const result = await this.data.delete('encampments', id);
            console.log('Encampment deleted:', result);
            return result;
        } catch (error) {
            console.error('Error deleting encampment:', error);
            throw error;
        }
    }

    // Get encampments by status
    async getEncampmentsByStatus(status) {
        try {
            const allEncampments = await this.getAllEncampments();
            return allEncampments.filter(encampment => encampment.status === status);
        } catch (error) {
            console.error('Error fetching encampments by status:', error);
            throw error;
        }
    }

    // Validate encampment data
    validateEncampmentData(data) {
        const errors = [];

        if (!data.name || data.name.trim() === '') {
            errors.push('Encampment name is required');
        }

        if (!data.location || data.location.trim() === '') {
            errors.push('Location is required');
        }

        if (data.coordinates && !this.validateCoordinates(data.coordinates)) {
            errors.push('Coordinates must be in "latitude, longitude" format');
        }

        const validStatuses = ['active', 'inactive', 'cleared', 'monitoring'];
        if (data.status && !validStatuses.includes(data.status)) {
            errors.push('Status must be one of: ' + validStatuses.join(', '));
        }

        const validTypes = ['temporary', 'permanent', 'seasonal'];
        if (data.type && !validTypes.includes(data.type)) {
            errors.push('Type must be one of: ' + validTypes.join(', '));
        }

        if (data.estimated_population && (isNaN(data.estimated_population) || data.estimated_population < 0)) {
            errors.push('Estimated population must be a positive number');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Validate coordinate format (lat, lng)
    validateCoordinates(coordinates) {
        if (!coordinates || typeof coordinates !== 'string') {
            return false;
        }

        const parts = coordinates.split(',');
        if (parts.length !== 2) {
            return false;
        }

        const lat = parseFloat(parts[0].trim());
        const lng = parseFloat(parts[1].trim());

        return !isNaN(lat) && !isNaN(lng) && 
               lat >= -90 && lat <= 90 && 
               lng >= -180 && lng <= 180;
    }

    // Format coordinates for display
    formatCoordinates(coordinates) {
        if (!coordinates) return 'No coordinates';
        
        const parts = coordinates.split(',');
        if (parts.length !== 2) return coordinates;
        
        const lat = parseFloat(parts[0].trim());
        const lng = parseFloat(parts[1].trim());
        
        if (isNaN(lat) || isNaN(lng)) return coordinates;
        
        return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }

    // Get encampment statistics
    async getEncampmentStats() {
        try {
            const encampments = await this.getAllEncampments();
            
            const stats = {
                total: encampments.length,
                active: encampments.filter(e => e.status === 'active').length,
                inactive: encampments.filter(e => e.status === 'inactive').length,
                cleared: encampments.filter(e => e.status === 'cleared').length,
                monitoring: encampments.filter(e => e.status === 'monitoring').length,
                totalPopulation: encampments.reduce((sum, e) => sum + (e.estimated_population || 0), 0),
                withCoordinates: encampments.filter(e => e.coordinates).length
            };

            return stats;
        } catch (error) {
            console.error('Error calculating encampment stats:', error);
            throw error;
        }
    }

    // Update last visited date
    async updateLastVisited(id, visitDate = null) {
        try {
            const lastVisited = visitDate || new Date().toISOString();
            return await this.updateEncampment(id, { last_visited: lastVisited });
        } catch (error) {
            console.error('Error updating last visited:', error);
            throw error;
        }
    }

    // Search encampments by name or location
    async searchEncampments(query) {
        try {
            const allEncampments = await this.getAllEncampments();
            const searchTerm = query.toLowerCase();
            
            return allEncampments.filter(encampment => 
                encampment.name.toLowerCase().includes(searchTerm) ||
                encampment.location.toLowerCase().includes(searchTerm) ||
                (encampment.description && encampment.description.toLowerCase().includes(searchTerm))
            );
        } catch (error) {
            console.error('Error searching encampments:', error);
            throw error;
        }
    }
}
