# S.T.E.V.I Retro Installer

## Overview

The S.T.E.V.I Retro installer is a professional Windows installer created using NSIS (Nullsoft Scriptable Install System) that provides a seamless installation experience for the S.T.E.V.I Retro application on fresh Windows installations.

## Installer Features

### ✅ System Compatibility
- **Windows 10/11**: Fully supported and optimized
- **Windows 8.1**: May work but not officially supported
- **Windows 7**: Not recommended (will show warning)

### ✅ Dependency Management
- **Visual C++ Redistributable**: Automatically detected and installed if needed
- **.NET Framework**: Checked for compatibility
- **System Requirements**: Validated before installation

### ✅ Installation Options
- **Custom Installation Directory**: Users can choose where to install
- **Desktop Shortcut**: Automatically created
- **Start Menu Entry**: Added to Programs list
- **User Data Preservation**: Option to keep data during uninstall

### ✅ Security Features
- **Secure Data Directories**: User data stored in `%APPDATA%\S.T.E.V.I Retro`
- **Encrypted Configuration**: Secure config file creation
- **Proper Permissions**: User-only access to sensitive data
- **Registry Integration**: Proper Windows integration

### ✅ Auto-Update Support
- **Update Registration**: Configured for automatic updates
- **Azure Blob Storage**: Ready for cloud-based updates
- **Version Tracking**: Registry entries for update management

## File Structure

```
dist/
├── S.T.E.V.I-Retro-Installer.exe          # Main installer (89MB)
├── S.T.E.V.I-Retro-Installer.exe.blockmap # Update verification
├── win-unpacked/                          # Standalone application
│   ├── S.T.E.V.I Retro.exe               # Main executable (194MB)
│   ├── resources/                         # App resources
│   └── [other Electron files]
└── builder-effective-config.yaml          # Build configuration
```

## Building the Installer

### Quick Build
```bash
# Run the automated build script
build-installer.bat
```

### Manual Build
```bash
# Check dependencies first
npm run check-deps

# Build the installer
npm run build-win

# Or use the full build process
npm run build
```

### Build Scripts Available
- `npm run build-win`: Build Windows installer only
- `npm run build`: Build for all platforms
- `npm run clean`: Clean build artifacts
- `npm run rebuild`: Clean and rebuild

## Installation Process

### 1. System Check
The installer automatically checks:
- Windows version compatibility
- Visual C++ Redistributable availability
- .NET Framework version
- Available disk space

### 2. Installation Options
- **Installation Directory**: Default: `C:\Program Files\S.T.E.V.I Retro`
- **Desktop Shortcut**: Automatically created
- **Start Menu**: Added to "I.H.A.R.C" category
- **Run After Install**: Option to launch immediately

### 3. Data Setup
Creates secure directories in `%APPDATA%\S.T.E.V.I Retro`:
- `data/` - Application data
- `cache/` - Temporary files
- `reports/` - Generated reports
- `templates/` - Report templates
- `media/` - User media files
- `logs/` - Application logs
- `backups/` - Data backups

### 4. Configuration
- Creates encrypted `config.json` with secure settings
- Sets up Azure integration
- Configures update endpoints
- Establishes data paths

## Distribution

### Standalone Distribution
The installer is completely self-contained and can be distributed via:
- **USB Drives**: Copy `S.T.E.V.I-Retro-Installer.exe`
- **Network Shares**: Share the installer file
- **Email**: Attach installer (89MB)
- **Cloud Storage**: Upload to OneDrive, Google Drive, etc.

### Silent Installation
For enterprise deployment, the installer supports silent installation:
```cmd
S.T.E.V.I-Retro-Installer.exe /S /D=C:\CustomPath
```

### Uninstallation
- **Control Panel**: Appears in Programs and Features
- **Registry Cleanup**: Removes all registry entries
- **Data Preservation**: Option to keep user data

## Technical Details

### NSIS Configuration
- **One-Click**: Disabled (allows customization)
- **Elevation**: Allowed for system integration
- **Unicode**: Enabled for international support
- **Block Map**: Generated for update verification

### Electron Builder Settings
- **Target**: NSIS installer
- **Architecture**: x64 only
- **Code Signing**: Automatic with available certificates
- **Compression**: Optimized for size and speed

### Security Considerations
- **User Data Isolation**: Separate from application files
- **Encryption**: Configuration files are encrypted
- **Permissions**: User-only access to sensitive data
- **Update Verification**: Block map for integrity checking

## Troubleshooting

### Common Issues

**Installation Fails**
- Check Windows version compatibility
- Ensure sufficient disk space (500MB minimum)
- Run as administrator if needed

**App Won't Start**
- Check Visual C++ Redistributable installation
- Verify .NET Framework 4.8+
- Check Windows Defender exclusions

**Data Loss**
- User data is preserved during updates
- Backups are stored in `%APPDATA%\S.T.E.V.I Retro\backups`
- Uninstaller asks before removing data

### Support
For technical support, contact the development team with:
- Windows version and build number
- Installation log (if available)
- Error messages or screenshots

## Version Information

- **Current Version**: 1.3.0
- **Installer Size**: ~89MB
- **Standalone Size**: ~194MB
- **Minimum Windows**: Windows 10 (recommended)
- **Architecture**: x64 only

---

*S.T.E.V.I Retro - Supportive Technology to Enable Vulnerable Individuals*
*Developed by I.H.A.R.C - Copyright © 2024* 