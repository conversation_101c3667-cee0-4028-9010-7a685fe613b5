# S.T.E.V.I Retro Installer Builder (PowerShell)
# This script builds the Windows installer for S.T.E.V.I Retro

param(
    [switch]$Clean,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to get file size in human readable format
function Get-FileSize {
    param([string]$Path)
    if (Test-Path $Path) {
        $size = (Get-Item $Path).Length
        $units = @('B', 'KB', 'MB', 'GB')
        $index = 0
        while ($size -gt 1024 -and $index -lt $units.Length - 1) {
            $size = $size / 1024
            $index++
        }
        return "{0:N1} {1}" -f $size, $units[$index]
    }
    return "0 B"
}

# Main script
try {
    Write-ColorOutput "========================================" "Cyan"
    Write-ColorOutput "S.T.E.V.I Retro Installer Builder" "Cyan"
    Write-ColorOutput "========================================" "Cyan"
    Write-Host ""

    # Check if running from project root
    if (-not (Test-Path "package.json")) {
        Write-ColorOutput "ERROR: package.json not found. Please run this script from the project root." "Red"
        exit 1
    }

    # Check Node.js and npm
    Write-ColorOutput "Checking prerequisites..." "Yellow"
    try {
        $nodeVersion = node --version
        $npmVersion = npm --version
        Write-ColorOutput "✓ Node.js: $nodeVersion" "Green"
        Write-ColorOutput "✓ npm: $npmVersion" "Green"
    }
    catch {
        Write-ColorOutput "ERROR: Node.js or npm not found. Please install Node.js first." "Red"
        exit 1
    }

    # Clean build if requested
    if ($Clean) {
        Write-ColorOutput "Cleaning previous builds..." "Yellow"
        if (Test-Path "dist") {
            Remove-Item "dist" -Recurse -Force
            Write-ColorOutput "✓ Cleaned dist directory" "Green"
        }
        if (Test-Path "S.T.E.V.I-Retro-Installer.exe") {
            Remove-Item "S.T.E.V.I-Retro-Installer.exe" -Force
            Write-ColorOutput "✓ Cleaned installer file" "Green"
        }
    }

    # Check dependencies
    Write-ColorOutput "Checking system dependencies..." "Yellow"
    npm run check-deps
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "ERROR: Dependency check failed!" "Red"
        exit 1
    }

    # Build installer
    Write-ColorOutput "Building installer..." "Yellow"
    Write-ColorOutput "This may take several minutes..." "Yellow"
    Write-Host ""

    $startTime = Get-Date
    npm run build-win
    $buildTime = (Get-Date) - $startTime

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-ColorOutput "========================================" "Green"
        Write-ColorOutput "✅ Installer built successfully!" "Green"
        Write-ColorOutput "========================================" "Green"
        Write-Host ""

        # Display build information
        $installerPath = "dist\S.T.E.V.I-Retro-Installer.exe"
        $standalonePath = "dist\win-unpacked\S.T.E.V.I Retro.exe"

        if (Test-Path $installerPath) {
            $installerSize = Get-FileSize $installerPath
            Write-ColorOutput "Installer location: $installerPath" "White"
            Write-ColorOutput "Installer size: $installerSize" "White"
        }

        if (Test-Path $standalonePath) {
            $standaloneSize = Get-FileSize $standalonePath
            Write-ColorOutput "Standalone app: $standalonePath" "White"
            Write-ColorOutput "Standalone size: $standaloneSize" "White"
        }

        Write-Host ""
        Write-ColorOutput "Build completed in: $($buildTime.ToString('mm\:ss'))" "Cyan"
        Write-ColorOutput "Ready for distribution!" "Green"

        # Offer to open dist folder
        $openFolder = Read-Host "Open dist folder? (y/n)"
        if ($openFolder -eq 'y' -or $openFolder -eq 'Y') {
            Start-Process "dist"
        }
    }
    else {
        Write-Host ""
        Write-ColorOutput "========================================" "Red"
        Write-ColorOutput "❌ Build failed!" "Red"
        Write-ColorOutput "========================================" "Red"
        Write-Host ""
        Write-ColorOutput "Please check the error messages above." "Red"
        exit 1
    }
}
catch {
    Write-ColorOutput "ERROR: An unexpected error occurred:" "Red"
    Write-ColorOutput $_.Exception.Message "Red"
    if ($Verbose) {
        Write-ColorOutput $_.Exception.StackTrace "Red"
    }
    exit 1
}
finally {
    Write-Host ""
    Write-ColorOutput "Press any key to continue..." "Gray"
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
} 