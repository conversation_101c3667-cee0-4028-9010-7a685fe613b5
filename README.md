# S.T.E.V.I Retro

**Supportive Technology to Enable Vulnerable Individuals - Desktop Operating System**

A retro-styled dos terminal desktop application for IHARC field staff to record and query outreach data.

## Overview

S.T.E.V.I Retro is an Electron-based desktop application that simulates a retro dos terminal interface. Designed for IHARC field staff using devices like Panasonic Toughbooks, it provides:

- Professional incident reporting with guided forms
- Comprehensive record management (people, addresses, license plates)
- Offline-first operation with automatic synchronization
- Authentic retro dos styling with red-on-black color scheme
- Cross-platform desktop application (Windows, macOS, Linux)

## Features

- **🎨 Retro dos Interface**: Authentic red-on-black terminal styling with ASCII art
- **💻 Desktop Application**: Built with Electron for consistent cross-platform experience
- **📱 Professional UI**: Modal dialogs, tabbed navigation, and form management
- **🔐 Secure Authentication**: Supabase integration with session management
- **📊 Incident Reporting**: Professional forms for filing detailed incident reports
- **👥 Record Management**: Add and search people, addresses, license plates
- **🔄 Offline Support**: Full functionality offline with automatic sync when online
- **🎯 Role-Based Security**: Requires IHARC staff authentication

## Installation

### Quick Setup (Recommended)

For fresh Windows installations, use the automated dependency installer:

1. Clone or download the repository
2. Double-click `install-dependencies.bat` or run as Administrator:
   ```cmd
   install-dependencies.bat
   ```

This will automatically install:
- Node.js 18+ LTS
- .NET SDK 6.0+
- All npm dependencies

### Manual Setup

If you prefer manual installation:

#### Prerequisites

- Node.js 22+
- Windows 10+ (for deployment target)
- .NET SDK 6.0+ (for building the launcher)
- Access to IHARC Supabase instance

#### Steps

1. Clone the repository:
```bash
git clone <repository-url>
cd s.t.e.v.i_retro
```

2. Install dependencies:
```bash
npm install
```

3. Configuration is automatically created at `~/.steviretrorc` on first run.

### Project Cleanup

This project has been optimized to reduce file count from 8,647 to 5,622 files (35% reduction) while maintaining all functionality. See [CLEANUP-SUMMARY.md](CLEANUP-SUMMARY.md) for details.

### Development

Run in development mode:
```bash
npm run dev
```

### Building for Production

Build standalone binary:
```bash
npm run build
```

This creates a Windows executable in the `dist/` folder.

## Usage

### Authentication

First-time setup requires login:
```bash
steviretro login
```

### Interactive Mode

Run without arguments for interactive mode:
```bash
steviretro
```

### Command Examples

```bash
# Report a new incident
steviretro report

# Look up records
steviretro lookup person 123
steviretro lookup bike abc-123-def

# Add new records
steviretro add person
steviretro add address

# View media
steviretro view 456

# Export incident to PDF
steviretro export 789 --template default

# Get help
steviretro help
steviretro help report
```

## Database Schema

The application uses the following main tables:

- `incidents` - Incident reports
- `people` - Person records
- `addresses` - Address records
- `license_plates` - Vehicle plate records
- `bikes` - Bike registration records (existing)
- `media` - File attachments

## Configuration

Configuration file: `~/.steviretrorc`

```ini
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
CACHE_TTL=3600
OFFLINE_MODE=false
DEBUG=false
```

## Templates

PDF templates are stored in `~/.steviretro/templates/` as JSON files.
Generated reports are saved to `~/.steviretro/reports/`.

## Offline Operation

- Authentication tokens are cached securely
- Recent data is cached locally
- New records are queued for sync when offline
- Full functionality available without internet connection

## Security

- JWT tokens stored in OS keychain (Windows Credential Manager)
- Row Level Security (RLS) policies enforce access control
- Role verification on login (requires `iharc_staff` role)
- Secure configuration file handling

## Development Status

✅ **Completed:**
- Database setup with required tables
- Authentication system with role verification
- Basic CLI framework and command routing
- Core commands (login, report, lookup, add)
- Offline caching and data management
- Configuration management

🚧 **In Progress:**
- PDF generation system
- Media attachment handling
- Template management
- Advanced terminal UI features

📋 **Planned:**
- Binary packaging for Windows
- Notification system
- Command history
- LLM integration for natural language queries

## Contributing

This is an internal IHARC project. Contact the development team for contribution guidelines.

## License

Internal use only - IHARC