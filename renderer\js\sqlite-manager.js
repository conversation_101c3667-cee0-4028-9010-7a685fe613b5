// SQLite Manager for S.T.E.V.I Retro - Local Data Caching
// Note: better-sqlite3 needs to be required in Electron renderer process
const Database = window.require ? window.require('better-sqlite3') : null;

export class SQLiteManager {
    constructor(dbPath) {
        this.dbPath = dbPath;
        this.db = null;
        this.isInitialized = false;
    }

    async init() {
        try {
            if (!Database) {
                throw new Error('better-sqlite3 not available in this environment');
            }

            console.log('Initializing SQLite database at:', this.dbPath);
            this.db = new Database(this.dbPath);
            
            // Enable WAL mode for better concurrency
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('synchronous = NORMAL');
            this.db.pragma('cache_size = 1000');
            this.db.pragma('temp_store = memory');
            
            await this.createTables();
            await this.migrateTables();
            this.isInitialized = true;
            console.log('SQLite database initialized successfully');
        } catch (error) {
            console.error('Failed to initialize SQLite database:', error);
            throw error;
        }
    }

    createTables() {
        const tables = [
            // Cache metadata table
            `CREATE TABLE IF NOT EXISTS cache_metadata (
                table_name TEXT PRIMARY KEY,
                last_sync DATETIME,
                version INTEGER DEFAULT 1,
                record_count INTEGER DEFAULT 0
            )`,

            // People cache table
            `CREATE TABLE IF NOT EXISTS cache_people (
                id INTEGER PRIMARY KEY,
                created_at DATETIME,
                first_name TEXT,
                last_name TEXT,
                "First Name" TEXT,
                "Last Name" TEXT,
                date_of_birth DATE,
                "Active Homelessness" BOOLEAN,
                "Active Addictions?" BOOLEAN,
                "Age" INTEGER,
                age INTEGER,     -- Auto-calculated from date_of_birth
                phone TEXT,
                email TEXT,
                emergency_contact TEXT,
                emergency_contact_phone TEXT,
                currently_homeless BOOLEAN,
                housing_status TEXT,
                notes TEXT,
                created_by TEXT,
                updated_by TEXT,
                updated_at DATETIME,

                -- Person-specific single-value fields for homeless/addiction services
                preferred_pronouns TEXT,
                primary_language TEXT,
                has_id_documents BOOLEAN,
                veteran_status BOOLEAN,
                income_source TEXT,
                risk_level TEXT,
                last_service_date DATE,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Disabilities cache table
            `CREATE TABLE IF NOT EXISTS cache_disabilities (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                disability_type TEXT NOT NULL,
                description TEXT,
                severity TEXT,
                accommodation_needs TEXT,
                diagnosed_date DATE,
                healthcare_provider TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Case management cache table
            `CREATE TABLE IF NOT EXISTS cache_case_management (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                case_manager_name TEXT NOT NULL,
                case_manager_contact TEXT,
                agency TEXT,
                case_number TEXT,
                start_date DATE,
                end_date DATE,
                status TEXT,
                case_type TEXT,
                priority TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Service barriers cache table
            `CREATE TABLE IF NOT EXISTS cache_service_barriers (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                barrier_type TEXT NOT NULL,
                description TEXT NOT NULL,
                severity TEXT,
                status TEXT,
                identified_date DATE,
                resolved_date DATE,
                resolution_notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Support contacts cache table
            `CREATE TABLE IF NOT EXISTS cache_support_contacts (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                contact_name TEXT NOT NULL,
                relationship TEXT,
                contact_type TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                availability TEXT,
                notes TEXT,
                is_emergency_contact BOOLEAN,
                is_primary BOOLEAN,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Criminal Justice cache tables
            `CREATE TABLE IF NOT EXISTS cache_incarceration_status (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                facility_name TEXT,
                facility_type TEXT,
                status TEXT NOT NULL,
                admission_date DATE,
                release_date DATE,
                expected_release_date DATE,
                charges TEXT,
                sentence_length TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            `CREATE TABLE IF NOT EXISTS cache_bail_conditions (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                condition_type TEXT NOT NULL,
                condition_description TEXT NOT NULL,
                start_date DATE,
                end_date DATE,
                amount DECIMAL(10,2),
                surety_name TEXT,
                surety_contact TEXT,
                status TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            `CREATE TABLE IF NOT EXISTS cache_court_dates (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                court_date DATE NOT NULL,
                court_time TIME,
                court_name TEXT,
                court_address TEXT,
                case_number TEXT,
                charges TEXT,
                court_type TEXT,
                appearance_type TEXT,
                outcome TEXT,
                next_court_date DATE,
                lawyer_name TEXT,
                lawyer_contact TEXT,
                status TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            `CREATE TABLE IF NOT EXISTS cache_arrest_history (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                arrest_date DATE NOT NULL,
                arrest_time TIME,
                arresting_agency TEXT,
                location TEXT,
                charges TEXT NOT NULL,
                disposition TEXT,
                case_number TEXT,
                booking_number TEXT,
                bail_amount DECIMAL(10,2),
                release_date DATE,
                release_type TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            `CREATE TABLE IF NOT EXISTS cache_legal_contacts (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                contact_name TEXT NOT NULL,
                contact_type TEXT NOT NULL,
                organization TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                case_numbers TEXT,
                relationship_start_date DATE,
                relationship_end_date DATE,
                status TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Pets cache table
            `CREATE TABLE IF NOT EXISTS cache_pets (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                name TEXT NOT NULL,
                species TEXT,
                breed TEXT,
                age INTEGER,
                color TEXT,
                description TEXT,
                microchip_number TEXT,
                vaccination_status TEXT,
                medical_notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,
                
                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Medical issues cache table
            `CREATE TABLE IF NOT EXISTS cache_medical_issues (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                condition_name TEXT NOT NULL,
                diagnosis_date DATE,
                severity TEXT,
                treatment_status TEXT,
                healthcare_provider TEXT,
                medication TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // People activities cache table
            `CREATE TABLE IF NOT EXISTS cache_people_activities (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                activity_date DATE,
                activity_time TIME,
                location TEXT,
                staff_member TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Ranger activity log cache table
            `CREATE TABLE IF NOT EXISTS cache_ranger_activity_log (
                id INTEGER PRIMARY KEY,
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                activity_date DATE,
                activity_time TIME,
                location TEXT,
                staff_member TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Ranger activity people cache table
            `CREATE TABLE IF NOT EXISTS cache_ranger_activity_people (
                id INTEGER PRIMARY KEY,
                activity_id INTEGER,
                person_id INTEGER,
                role TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Supply provisions cache table
            `CREATE TABLE IF NOT EXISTS cache_supply_provisions (
                id INTEGER PRIMARY KEY,
                activity_id INTEGER,
                item_id TEXT,
                quantity_provided INTEGER,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Address activities cache table
            `CREATE TABLE IF NOT EXISTS cache_address_activities (
                id INTEGER PRIMARY KEY,
                address_id INTEGER,
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                activity_date DATE,
                activity_time TIME,
                staff_member TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Vehicle activities cache table
            `CREATE TABLE IF NOT EXISTS cache_vehicle_activities (
                id INTEGER PRIMARY KEY,
                vehicle_id TEXT,
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                activity_date DATE,
                activity_time TIME,
                staff_member TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Bike activities cache table
            `CREATE TABLE IF NOT EXISTS cache_bike_activities (
                id INTEGER PRIMARY KEY,
                bike_id TEXT,
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                activity_date DATE,
                activity_time TIME,
                staff_member TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Property records cache table
            `CREATE TABLE IF NOT EXISTS cache_property_records (
                id TEXT PRIMARY KEY,
                incident_id TEXT,
                property_type TEXT NOT NULL,
                description TEXT NOT NULL,
                serial_number TEXT,
                value_estimate REAL,
                status TEXT,
                location_found TEXT,
                date_found DATE,
                found_by TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Property actions cache table
            `CREATE TABLE IF NOT EXISTS cache_property_actions (
                id TEXT PRIMARY KEY,
                property_id TEXT,
                action_type TEXT NOT NULL,
                action_date DATE,
                action_time TIME,
                performed_by TEXT,
                description TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Activity logs cache table
            `CREATE TABLE IF NOT EXISTS cache_activity_logs (
                id INTEGER PRIMARY KEY,
                table_name TEXT NOT NULL,
                record_id TEXT NOT NULL,
                action TEXT NOT NULL,
                old_values TEXT,
                new_values TEXT,
                user_id TEXT,
                timestamp DATETIME,
                ip_address TEXT,
                user_agent TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Found bike reports cache table
            `CREATE TABLE IF NOT EXISTS cache_found_bike_reports (
                id TEXT PRIMARY KEY,
                bike_id TEXT,
                found_date DATE,
                found_time TIME,
                found_location TEXT,
                found_by TEXT,
                condition_description TEXT,
                recovery_method TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Recovery logs cache table
            `CREATE TABLE IF NOT EXISTS cache_recovery_logs (
                id TEXT PRIMARY KEY,
                bike_id TEXT,
                recovery_date DATE,
                recovery_time TIME,
                recovery_location TEXT,
                recovered_by TEXT,
                recovery_method TEXT,
                condition_at_recovery TEXT,
                returned_to_owner BOOLEAN,
                return_date DATE,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Incidents cache table
            `CREATE TABLE IF NOT EXISTS cache_incidents (
                id INTEGER PRIMARY KEY,
                created_at DATETIME,
                reporter_id TEXT,
                location TEXT NOT NULL,
                narrative TEXT NOT NULL,
                tags TEXT, -- JSON array as text
                is_urgent BOOLEAN,
                updated_at DATETIME,
                incident_number TEXT,
                incident_date DATE,
                incident_time TIME,
                incident_type TEXT,
                description TEXT,
                priority TEXT,
                status TEXT,
                assigned_ranger TEXT,
                coordinates TEXT,
                reported_by TEXT,
                dispatch_notes TEXT,
                log_entries TEXT, -- JSONB array as text (SQLite doesn't have native JSONB)

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Addresses cache table
            `CREATE TABLE IF NOT EXISTS cache_addresses (
                id INTEGER PRIMARY KEY,
                street_address TEXT NOT NULL,
                city TEXT,
                province TEXT,
                postal_code TEXT,
                country TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Bikes cache table
            `CREATE TABLE IF NOT EXISTS cache_bikes (
                id TEXT PRIMARY KEY, -- UUID as text
                user_id TEXT,
                owner_name TEXT NOT NULL,
                owner_email TEXT NOT NULL,
                serial_number TEXT NOT NULL,
                make TEXT NOT NULL,
                model TEXT NOT NULL,
                color TEXT NOT NULL,
                value REAL,
                photo_base64 TEXT,
                is_stolen BOOLEAN NOT NULL,
                registered_at DATETIME,
                created_at DATETIME,
                updated_at DATETIME,
                theft_date DATE,
                theft_time TIME,
                theft_location TEXT,
                suspect_details TEXT,
                theft_notes TEXT,
                reported_stolen_at DATETIME,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Encampments cache table
            `CREATE TABLE IF NOT EXISTS cache_encampments (
                id TEXT PRIMARY KEY, -- UUID as text
                name TEXT NOT NULL,
                location TEXT NOT NULL,
                coordinates TEXT, -- lat,lng format
                status TEXT NOT NULL,
                type TEXT,
                estimated_population INTEGER,
                description TEXT,
                safety_concerns TEXT,
                services_needed TEXT,
                last_visited DATETIME,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Media cache table for file attachments
            `CREATE TABLE IF NOT EXISTS cache_media (
                id INTEGER PRIMARY KEY,
                record_type TEXT NOT NULL,
                record_id INTEGER NOT NULL,
                filename TEXT NOT NULL,
                stored_at TEXT NOT NULL,
                description TEXT,
                file_size INTEGER,
                content_type TEXT,
                uploaded_at DATETIME,
                created_at DATETIME,
                updated_at DATETIME,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Items cache table
            `CREATE TABLE IF NOT EXISTS cache_items (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT NOT NULL,
                unit_type TEXT NOT NULL,
                current_stock INTEGER NOT NULL,
                minimum_threshold INTEGER,
                cost_per_unit REAL,
                supplier TEXT,
                active BOOLEAN NOT NULL,
                created_at DATETIME,
                updated_at DATETIME,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Organizations cache table
            `CREATE TABLE IF NOT EXISTS cache_organizations (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                organization_type TEXT,
                description TEXT,
                services_provided TEXT,
                address TEXT,
                city TEXT,
                province TEXT,
                postal_code TEXT,
                phone TEXT,
                email TEXT,
                website TEXT,
                contact_person TEXT,
                contact_title TEXT,
                contact_phone TEXT,
                contact_email TEXT,
                operating_hours TEXT,
                availability_notes TEXT,
                partnership_type TEXT,
                referral_process TEXT,
                special_requirements TEXT,
                status TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,
                services_tags TEXT, -- JSON as text

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Sync queue for offline operations
            `CREATE TABLE IF NOT EXISTS sync_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                operation TEXT NOT NULL,
                record_id TEXT NOT NULL,
                data TEXT, -- JSON data
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                retry_count INTEGER DEFAULT 0,
                status TEXT DEFAULT 'pending',
                error_message TEXT
            )`,

            // Conflict resolution log
            `CREATE TABLE IF NOT EXISTS conflict_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                record_id TEXT NOT NULL,
                local_version TEXT,
                remote_version TEXT,
                resolution TEXT,
                resolved_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Incident links cache table
            `CREATE TABLE IF NOT EXISTS cache_incident_links (
                id TEXT PRIMARY KEY,
                incident_id TEXT NOT NULL,
                linked_record_type TEXT NOT NULL,
                linked_record_id TEXT NOT NULL,
                link_type TEXT,
                notes TEXT,
                created_at DATETIME,
                created_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // License plates cache table
            `CREATE TABLE IF NOT EXISTS cache_license_plates (
                id TEXT PRIMARY KEY,
                license_plate TEXT NOT NULL,
                make TEXT,
                model TEXT,
                year INTEGER,
                color TEXT,
                vin TEXT,
                owner_name TEXT,
                owner_phone TEXT,
                owner_address TEXT,
                notes TEXT,
                is_stolen BOOLEAN DEFAULT FALSE,
                stolen_date DATE,
                recovery_date DATE,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`
        ];

        // Create all tables
        tables.forEach(sql => {
            this.db.exec(sql);
        });

        // Add missing columns to existing tables (for database migration)
        // Note: SQLite doesn't support DROP COLUMN, so we leave full_name and "Full Name" columns
        // but the application will ignore them in favor of first_name + last_name

        try {
            this.db.exec('ALTER TABLE cache_people ADD COLUMN age INTEGER');
        } catch (error) {
            // Column already exists, ignore
        }

        // Add new person-specific fields for homeless/addiction services
        const newColumns = [
            'preferred_pronouns TEXT',
            'primary_language TEXT',
            'has_id_documents BOOLEAN',
            'veteran_status BOOLEAN',
            'income_source TEXT',
            'risk_level TEXT',
            'last_service_date DATE'
        ];

        newColumns.forEach(column => {
            try {
                this.db.exec(`ALTER TABLE cache_people ADD COLUMN ${column}`);
            } catch (error) {
                // Column already exists, ignore
            }
        });

        // Add missing columns to cache_incidents for dispatch functionality
        const incidentColumns = [
            'incident_date DATE',
            'incident_time TIME',
            'incident_type TEXT',
            'description TEXT',
            'priority TEXT',
            'status TEXT',
            'assigned_ranger TEXT',
            'coordinates TEXT',
            'reported_by TEXT',
            'dispatch_notes TEXT'
        ];

        incidentColumns.forEach(column => {
            try {
                this.db.exec(`ALTER TABLE cache_incidents ADD COLUMN ${column}`);
            } catch (error) {
                // Column already exists, ignore
            }
        });

        // Create indexes for performance
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_people_name ON cache_people(first_name, last_name)',
            'CREATE INDEX IF NOT EXISTS idx_people_sync ON cache_people(sync_status, last_synced)',
            'CREATE INDEX IF NOT EXISTS idx_pets_person ON cache_pets(person_id)',
            'CREATE INDEX IF NOT EXISTS idx_incidents_urgent ON cache_incidents(is_urgent)',
            'CREATE INDEX IF NOT EXISTS idx_incidents_date ON cache_incidents(created_at)',
            'CREATE INDEX IF NOT EXISTS idx_bikes_stolen ON cache_bikes(is_stolen)',
            'CREATE INDEX IF NOT EXISTS idx_items_category ON cache_items(category)',
            'CREATE INDEX IF NOT EXISTS idx_items_active ON cache_items(active)',
            'CREATE INDEX IF NOT EXISTS idx_sync_queue_status ON sync_queue(status, timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_conflict_log_table ON conflict_log(table_name, record_id)',
            'CREATE INDEX IF NOT EXISTS idx_incident_links_incident ON cache_incident_links(incident_id)',
            'CREATE INDEX IF NOT EXISTS idx_incident_links_record ON cache_incident_links(linked_record_type, linked_record_id)',
            'CREATE INDEX IF NOT EXISTS idx_license_plates_plate ON cache_license_plates(license_plate)',
            'CREATE INDEX IF NOT EXISTS idx_license_plates_stolen ON cache_license_plates(is_stolen)'
        ];

        indexes.forEach(sql => {
            this.db.exec(sql);
        });

        console.log('SQLite tables and indexes created successfully');
    }

    // Generic CRUD operations
    insert(tableName, data) {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;

        // Clean and prepare data for SQLite
        const cleanedData = this.sanitizeDataForSQLite(data);
        const columns = Object.keys(cleanedData);
        const placeholders = columns.map(() => '?').join(', ');
        const values = Object.values(cleanedData);

        // Properly quote column names that might have spaces or special characters
        const quotedColumns = columns.map(col => `"${col}"`);
        const sql = `INSERT INTO ${cacheTableName} (${quotedColumns.join(', ')}) VALUES (${placeholders})`;

        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.run(...values);

            // Update cache metadata
            this.updateCacheMetadata(tableName);

            return { id: result.lastInsertRowid, ...data };
        } catch (error) {
            console.error(`Error inserting into ${cacheTableName}:`, error);
            console.error('SQL:', sql);
            console.error('Columns:', columns);
            console.error('Values:', values);
            console.error('Data types:', values.map(v => typeof v));
            throw error;
        }
    }

    // Upsert method - insert or update if exists
    upsert(tableName, data) {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;
        const sanitizedData = this.sanitizeDataForSQLite(data);

        // Check if record exists
        if (sanitizedData.id) {
            const existing = this.get(tableName, sanitizedData.id);
            if (existing) {
                // Update existing record
                return this.update(tableName, sanitizedData.id, sanitizedData);
            }
        }

        // Insert new record
        return this.insert(tableName, sanitizedData);
    }

    // Sanitize data to ensure SQLite compatibility
    sanitizeDataForSQLite(data) {
        const sanitized = {};

        for (const [key, value] of Object.entries(data)) {
            if (value === undefined) {
                sanitized[key] = null;
            } else if (value === null) {
                sanitized[key] = null;
            } else if (Array.isArray(value)) {
                sanitized[key] = JSON.stringify(value);
            } else if (value instanceof Date) {
                sanitized[key] = value.toISOString();
            } else if (typeof value === 'object') {
                sanitized[key] = JSON.stringify(value);
            } else if (typeof value === 'boolean') {
                sanitized[key] = value ? 1 : 0; // SQLite uses integers for booleans
            } else if (typeof value === 'number') {
                // Ensure it's a valid number
                sanitized[key] = isNaN(value) ? null : value;
            } else if (typeof value === 'string') {
                sanitized[key] = value;
            } else {
                // For any other type, convert to string
                sanitized[key] = String(value);
            }
        }

        return sanitized;
    }

    get(tableName, id) {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;
        const sql = `SELECT * FROM ${cacheTableName} WHERE id = ?`;

        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.get(id);

            if (result) {
                // Parse JSON fields back to their original types
                return this.parseJsonFields(result);
            }

            return result;
        } catch (error) {
            console.error(`Error getting from ${cacheTableName}:`, error);
            throw error;
        }
    }

    // Helper method to parse JSON fields back to their original types
    parseJsonFields(record) {
        const parsed = { ...record };

        // Common fields that might be JSON
        const jsonFields = ['tags', 'metadata', 'data', 'log_entries', 'services_tags'];

        for (const field of jsonFields) {
            if (parsed[field] && typeof parsed[field] === 'string') {
                try {
                    parsed[field] = JSON.parse(parsed[field]);
                } catch (e) {
                    // If parsing fails, keep as string
                }
            }
        }

        return parsed;
    }

    getAll(tableName, orderBy = 'created_at DESC') {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;
        const sql = `SELECT * FROM ${cacheTableName} ORDER BY ${orderBy}`;

        try {
            const stmt = this.db.prepare(sql);
            const results = stmt.all();

            // Parse JSON fields for all results
            return results.map(record => this.parseJsonFields(record));
        } catch (error) {
            console.error(`Error getting all from ${cacheTableName}:`, error);
            throw error;
        }
    }

    update(tableName, id, data) {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;

        // Clean and prepare data for SQLite
        const cleanedData = this.sanitizeDataForSQLite(data);
        const columns = Object.keys(cleanedData);
        const quotedColumns = columns.map(col => `"${col}" = ?`);
        const setClause = quotedColumns.join(', ');
        const values = [...Object.values(cleanedData), id];

        const sql = `UPDATE ${cacheTableName} SET ${setClause} WHERE id = ?`;

        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.run(...values);

            if (result.changes > 0) {
                this.updateCacheMetadata(tableName);
                return this.get(tableName, id);
            }
            return null;
        } catch (error) {
            console.error(`Error updating ${cacheTableName}:`, error);
            console.error('SQL:', sql);
            console.error('Columns:', columns);
            console.error('Values:', values);
            console.error('Data types:', values.map(v => typeof v));
            throw error;
        }
    }

    delete(tableName, id) {
        if (!this.isInitialized) throw new Error('Database not initialized');
        
        const cacheTableName = `cache_${tableName}`;
        const sql = `DELETE FROM ${cacheTableName} WHERE id = ?`;
        
        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.run(id);
            
            if (result.changes > 0) {
                this.updateCacheMetadata(tableName);
            }
            
            return result.changes > 0;
        } catch (error) {
            console.error(`Error deleting from ${cacheTableName}:`, error);
            throw error;
        }
    }

    // Cache management
    updateCacheMetadata(tableName) {
        const sql = `
            INSERT OR REPLACE INTO cache_metadata (table_name, last_sync, record_count)
            VALUES (?, CURRENT_TIMESTAMP, (SELECT COUNT(*) FROM cache_${tableName}))
        `;
        
        try {
            const stmt = this.db.prepare(sql);
            stmt.run(tableName);
        } catch (error) {
            console.error('Error updating cache metadata:', error);
        }
    }

    getCacheMetadata(tableName) {
        const sql = 'SELECT * FROM cache_metadata WHERE table_name = ?';
        
        try {
            const stmt = this.db.prepare(sql);
            return stmt.get(tableName);
        } catch (error) {
            console.error('Error getting cache metadata:', error);
            return null;
        }
    }

    // Sync queue operations
    addToSyncQueue(tableName, operation, recordId, data) {
        const sql = `
            INSERT INTO sync_queue (table_name, operation, record_id, data)
            VALUES (?, ?, ?, ?)
        `;
        
        try {
            const stmt = this.db.prepare(sql);
            return stmt.run(tableName, operation, recordId, JSON.stringify(data));
        } catch (error) {
            console.error('Error adding to sync queue:', error);
            throw error;
        }
    }

    getSyncQueue() {
        const sql = 'SELECT * FROM sync_queue WHERE status = ? ORDER BY timestamp ASC';
        
        try {
            const stmt = this.db.prepare(sql);
            return stmt.all('pending');
        } catch (error) {
            console.error('Error getting sync queue:', error);
            return [];
        }
    }

    updateSyncQueueStatus(id, status, errorMessage = null) {
        const sql = 'UPDATE sync_queue SET status = ?, error_message = ? WHERE id = ?';
        
        try {
            const stmt = this.db.prepare(sql);
            stmt.run(status, errorMessage, id);
        } catch (error) {
            console.error('Error updating sync queue status:', error);
        }
    }

    clearSyncQueue() {
        const sql = 'DELETE FROM sync_queue WHERE status = ?';

        try {
            const stmt = this.db.prepare(sql);
            stmt.run('completed');
        } catch (error) {
            console.error('Error clearing sync queue:', error);
        }
    }

    // Clear all data from a table
    clear(table) {
        try {
            // First check if table exists
            const checkSql = `SELECT name FROM sqlite_master WHERE type='table' AND name=?`;
            const checkStmt = this.db.prepare(checkSql);
            const tableExists = checkStmt.get(table);

            if (!tableExists) {
                console.log(`Table ${table} does not exist, skipping clear operation`);
                return 0;
            }

            const sql = `DELETE FROM ${table}`;
            const stmt = this.db.prepare(sql);
            const result = stmt.run();
            console.log(`Cleared ${result.changes} records from ${table}`);
            return result.changes;
        } catch (error) {
            console.error(`Error clearing table ${table}:`, error);
            return 0;
        }
    }

    // Transaction support
    transaction(callback) {
        if (!this.isInitialized) throw new Error('Database not initialized');
        
        const transaction = this.db.transaction(callback);
        return transaction();
    }

    // Database maintenance
    vacuum() {
        if (!this.isInitialized) return;
        
        try {
            this.db.exec('VACUUM');
            console.log('Database vacuum completed');
        } catch (error) {
            console.error('Error during database vacuum:', error);
        }
    }

    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
            this.isInitialized = false;
            console.log('SQLite database closed');
        }
    }

    // Get database statistics
    getStats() {
        if (!this.isInitialized) return null;
        
        try {
            const tables = ['people', 'pets', 'incidents', 'addresses', 'bikes'];
            const stats = {};
            
            tables.forEach(table => {
                const sql = `SELECT COUNT(*) as count FROM cache_${table}`;
                const stmt = this.db.prepare(sql);
                const result = stmt.get();
                stats[table] = result.count;
            });
            
            // Sync queue stats
            const queueSql = 'SELECT status, COUNT(*) as count FROM sync_queue GROUP BY status';
            const queueStmt = this.db.prepare(queueSql);
            const queueResults = queueStmt.all();
            
            stats.syncQueue = {};
            queueResults.forEach(row => {
                stats.syncQueue[row.status] = row.count;
            });
            
            return stats;
        } catch (error) {
            console.error('Error getting database stats:', error);
            return null;
        }
    }

    async migrateTables() {
        try {
            console.log('Running SQLite table migrations...');
            
            // Check if incidents table needs migration
            const incidentsColumns = this.db.prepare("PRAGMA table_info(cache_incidents)").all();
            const columnNames = incidentsColumns.map(col => col.name);
            
            // Add missing columns to incidents table
            const missingColumns = [
                { name: 'incident_date', type: 'DATE' },
                { name: 'incident_time', type: 'TIME' },
                { name: 'incident_type', type: 'TEXT' },
                { name: 'description', type: 'TEXT' },
                { name: 'priority', type: 'TEXT' },
                { name: 'status', type: 'TEXT' },
                { name: 'assigned_ranger', type: 'TEXT' },
                { name: 'coordinates', type: 'TEXT' },
                { name: 'reported_by', type: 'TEXT' },
                { name: 'dispatch_notes', type: 'TEXT' },
                { name: 'log_entries', type: 'TEXT' }
            ];
            
            for (const column of missingColumns) {
                if (!columnNames.includes(column.name)) {
                    console.log(`Adding column ${column.name} to cache_incidents table`);
                    this.db.prepare(`ALTER TABLE cache_incidents ADD COLUMN ${column.name} ${column.type}`).run();
                }
            }
            
            console.log('SQLite table migrations completed');
        } catch (error) {
            console.warn('Migration failed (this is normal for new databases):', error.message);
        }
    }

    /**
     * Clear all cache tables to ensure fresh data from Supabase
     */
    async clearAllCaches() {
        if (!this.isInitialized) {
            console.warn('SQLite not initialized, cannot clear caches');
            return;
        }

        try {
            console.log('🧹 Clearing all SQLite cache tables...');

            // Get all cache table names
            const tables = this.db.prepare(`
                SELECT name FROM sqlite_master
                WHERE type='table' AND name LIKE 'cache_%'
            `).all();

            // Clear each cache table
            for (const table of tables) {
                try {
                    this.db.prepare(`DELETE FROM ${table.name}`).run();
                    console.log(`✅ Cleared ${table.name}`);
                } catch (error) {
                    console.warn(`⚠️ Failed to clear ${table.name}:`, error.message);
                }
            }

            // Clear sync queue
            try {
                this.db.prepare('DELETE FROM sync_queue').run();
                console.log('✅ Cleared sync_queue');
            } catch (error) {
                console.warn('⚠️ Failed to clear sync_queue:', error.message);
            }

            // Clear conflict log
            try {
                this.db.prepare('DELETE FROM conflict_log').run();
                console.log('✅ Cleared conflict_log');
            } catch (error) {
                console.warn('⚠️ Failed to clear conflict_log:', error.message);
            }

            // Reset cache metadata
            try {
                this.db.prepare('DELETE FROM cache_metadata').run();
                console.log('✅ Cleared cache_metadata');
            } catch (error) {
                console.warn('⚠️ Failed to clear cache_metadata:', error.message);
            }

            console.log('🧹 SQLite cache clearing completed');
        } catch (error) {
            console.error('❌ Error clearing SQLite caches:', error);
        }
    }
}
