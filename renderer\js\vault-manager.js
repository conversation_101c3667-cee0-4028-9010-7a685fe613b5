export class VaultManager {
    constructor(supabaseClient) {
        this.supabase = supabaseClient;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache for secrets
    }

    /**
     * Get a secret from Supabase Vault by name
     * @param {string} secretName - The name of the secret to retrieve
     * @returns {Promise<string|null>} The decrypted secret value or null if not found
     */
    async getSecret(secretName) {
        try {
            // Check cache first
            const cacheKey = `secret_${secretName}`;
            const cached = this.cache.get(cacheKey);
            
            if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
                console.log(`Returning cached secret: ${secretName}`);
                return cached.value;
            }

            console.log(`Fetching secret from Vault: ${secretName}`);

            const { data, error } = await this.supabase.rpc('get_secret_by_name', { secret_name: secretName });

            if (error) {
                throw error;
            }

            const secretValue = data;
            
            if (secretValue) {
                // Cache the secret
                this.cache.set(cacheKey, {
                    value: secretValue,
                    timestamp: Date.now()
                });
                
                console.log(`Successfully retrieved secret: ${secretName}`);
                return secretValue;
            }

            console.warn(`Secret found but empty: ${secretName}`);
            return null;

        } catch (error) {
            console.error(`Error retrieving secret ${secretName}:`, error);
            return null;
        }
    }

    /**
     * Store a secret in Supabase Vault
     * @param {string} secretName - The name of the secret
     * @param {string} secretValue - The secret value to store
     * @param {string} description - Optional description of the secret
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    async storeSecret(secretName, secretValue, description = null) {
        try {
            console.log(`Storing secret in Vault: ${secretName}`);

            // Use the vault.create_secret() function
            const { data, error } = await this.supabase.rpc('create_secret', {
                secret: secretValue,
                name: secretName,
                description: description
            });

            if (error) {
                throw error;
            }

            console.log(`Successfully stored secret: ${secretName}`);
            
            // Clear cache for this secret to force refresh
            this.cache.delete(`secret_${secretName}`);
            
            return true;

        } catch (error) {
            console.error(`Error storing secret ${secretName}:`, error);
            return false;
        }
    }

    /**
     * Update an existing secret in Supabase Vault
     * @param {string} secretName - The name of the secret to update
     * @param {string} newSecretValue - The new secret value
     * @param {string} newDescription - Optional new description
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    async updateSecret(secretName, newSecretValue, newDescription = null) {
        try {
            console.log(`Updating secret in Vault: ${secretName}`);

            // First get the secret ID
            const { data: secretData, error: getError } = await this.supabase
                .from('vault.decrypted_secrets')
                .select('id')
                .eq('name', secretName)
                .single();

            if (getError) {
                throw getError;
            }

            if (!secretData) {
                console.warn(`Secret not found for update: ${secretName}`);
                return false;
            }

            // Use the vault.update_secret() function
            const { data, error } = await this.supabase.rpc('update_secret', {
                secret_id: secretData.id,
                secret: newSecretValue,
                name: secretName,
                description: newDescription
            });

            if (error) {
                throw error;
            }

            console.log(`Successfully updated secret: ${secretName}`);
            
            // Clear cache for this secret to force refresh
            this.cache.delete(`secret_${secretName}`);
            
            return true;

        } catch (error) {
            console.error(`Error updating secret ${secretName}:`, error);
            return false;
        }
    }

    /**
     * Delete a secret from Supabase Vault
     * @param {string} secretName - The name of the secret to delete
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    async deleteSecret(secretName) {
        try {
            console.log(`Deleting secret from Vault: ${secretName}`);

            // First get the secret ID
            const { data: secretData, error: getError } = await this.supabase
                .from('vault.decrypted_secrets')
                .select('id')
                .eq('name', secretName)
                .single();

            if (getError) {
                throw getError;
            }

            if (!secretData) {
                console.warn(`Secret not found for deletion: ${secretName}`);
                return false;
            }

            // Delete the secret
            const { error } = await this.supabase
                .from('secrets')
                .delete()
                .eq('id', secretData.id);

            if (error) {
                throw error;
            }

            console.log(`Successfully deleted secret: ${secretName}`);
            
            // Clear cache for this secret
            this.cache.delete(`secret_${secretName}`);
            
            return true;

        } catch (error) {
            console.error(`Error deleting secret ${secretName}:`, error);
            return false;
        }
    }

    /**
     * List all available secrets (names only, not values)
     * @returns {Promise<Array>} Array of secret objects with id, name, and description
     */
    async listSecrets() {
        try {
            console.log('Listing all secrets from Vault');

            const { data, error } = await this.supabase
                .from('vault.decrypted_secrets')
                .select('id, name, description, created_at, updated_at');

            if (error) {
                throw error;
            }

            return data || [];

        } catch (error) {
            console.error('Error listing secrets:', error);
            return [];
        }
    }

    /**
     * Clear the secrets cache
     */
    clearCache() {
        console.log('Clearing Vault secrets cache');
        this.cache.clear();
    }

    /**
     * Get Google API key from Vault
     * @returns {Promise<string|null>} The Google API key
     */
    async getGoogleApiKey() {
        const secret = await this.getSecret('google_api_key');
        if (secret) {
            return secret;
        }

        console.warn('Google API key not found in Vault');
        return null;
    }
}