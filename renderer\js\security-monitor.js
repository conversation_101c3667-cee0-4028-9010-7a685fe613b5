// Security Monitor for S.T.E.V.I DOS Electron App
// Monitors for suspicious activities and security threats

export class SecurityMonitor {
    constructor() {
        this.monitoringActive = false;
        this.suspiciousActivities = [];
        this.performanceBaseline = null;
        this.activityThresholds = {
            rapidClicks: 10, // clicks per second
            rapidRequests: 5, // requests per second
            memoryUsage: 100 * 1024 * 1024, // 100MB
            suspiciousPatterns: 3 // pattern matches before alert
        };
        this.monitoringIntervals = [];
        this.activityCounters = new Map();
    }

    /**
     * Start security monitoring
     */
    startMonitoring() {
        if (this.monitoringActive) {
            return;
        }

        console.log('🔍 Starting security monitoring...');
        
        this.monitoringActive = true;
        this.establishBaseline();
        this.setupActivityMonitors();
        this.setupPerformanceMonitoring();
        this.setupBehaviorAnalysis();
        
        console.log('✅ Security monitoring active');
    }

    /**
     * Stop security monitoring
     */
    stopMonitoring() {
        if (!this.monitoringActive) {
            return;
        }

        console.log('🔍 Stopping security monitoring...');
        
        this.monitoringActive = false;
        this.monitoringIntervals.forEach(interval => clearInterval(interval));
        this.monitoringIntervals = [];
        
        console.log('✅ Security monitoring stopped');
    }

    /**
     * Establish performance baseline
     */
    establishBaseline() {
        this.performanceBaseline = {
            memory: performance.memory ? performance.memory.usedJSHeapSize : 0,
            timestamp: Date.now(),
            loadTime: performance.timing ? performance.timing.loadEventEnd - performance.timing.navigationStart : 0
        };
        
        console.log('📊 Performance baseline established:', this.performanceBaseline);
    }

    /**
     * Setup activity monitors
     */
    setupActivityMonitors() {
        // Monitor rapid clicking
        let clickCount = 0;
        let clickTimer = null;
        
        document.addEventListener('click', () => {
            clickCount++;
            
            if (!clickTimer) {
                clickTimer = setTimeout(() => {
                    if (clickCount > this.activityThresholds.rapidClicks) {
                        this.reportSuspiciousActivity('RAPID_CLICKING', {
                            clickCount,
                            timeWindow: 1000
                        });
                    }
                    clickCount = 0;
                    clickTimer = null;
                }, 1000);
            }
        });

        // Monitor rapid form submissions
        let submitCount = 0;
        let submitTimer = null;
        
        document.addEventListener('submit', () => {
            submitCount++;
            
            if (!submitTimer) {
                submitTimer = setTimeout(() => {
                    if (submitCount > 3) {
                        this.reportSuspiciousActivity('RAPID_SUBMISSIONS', {
                            submitCount,
                            timeWindow: 1000
                        });
                    }
                    submitCount = 0;
                    submitTimer = null;
                }, 1000);
            }
        });

        // Monitor keyboard activity for suspicious patterns
        let keySequence = [];
        document.addEventListener('keydown', (event) => {
            keySequence.push({
                key: event.key,
                timestamp: Date.now(),
                ctrlKey: event.ctrlKey,
                altKey: event.altKey,
                shiftKey: event.shiftKey
            });
            
            // Keep only last 20 keystrokes
            if (keySequence.length > 20) {
                keySequence.shift();
            }
            
            // Check for suspicious patterns
            this.analyzeKeySequence(keySequence);
        });
    }

    /**
     * Setup performance monitoring
     */
    setupPerformanceMonitoring() {
        const performanceInterval = setInterval(() => {
            if (!this.monitoringActive) return;
            
            const currentMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            const memoryIncrease = currentMemory - this.performanceBaseline.memory;
            
            // Check for excessive memory usage
            if (memoryIncrease > this.activityThresholds.memoryUsage) {
                this.reportSuspiciousActivity('EXCESSIVE_MEMORY_USAGE', {
                    currentMemory,
                    baseline: this.performanceBaseline.memory,
                    increase: memoryIncrease
                });
            }
            
            // Check for performance anomalies
            const currentTime = Date.now();
            const timeSinceBaseline = currentTime - this.performanceBaseline.timestamp;
            
            if (timeSinceBaseline > 300000) { // 5 minutes
                // Reset baseline periodically
                this.establishBaseline();
            }
            
        }, 30000); // Check every 30 seconds
        
        this.monitoringIntervals.push(performanceInterval);
    }

    /**
     * Setup behavior analysis
     */
    setupBehaviorAnalysis() {
        // Monitor for unusual navigation patterns
        let navigationCount = 0;
        let lastNavigation = Date.now();
        
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        history.pushState = function(...args) {
            navigationCount++;
            const now = Date.now();
            
            if (now - lastNavigation < 100) { // Less than 100ms between navigations
                if (navigationCount > 10) {
                    window.app?.securityMonitor?.reportSuspiciousActivity('RAPID_NAVIGATION', {
                        navigationCount,
                        timeWindow: now - lastNavigation
                    });
                }
            }
            
            lastNavigation = now;
            return originalPushState.apply(this, args);
        };
        
        history.replaceState = function(...args) {
            return originalReplaceState.apply(this, args);
        };

        // Monitor for suspicious DOM modifications
        const observer = new MutationObserver((mutations) => {
            let suspiciousChanges = 0;
            
            mutations.forEach((mutation) => {
                // Check for script injections
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.tagName === 'SCRIPT' || node.tagName === 'IFRAME') {
                                suspiciousChanges++;
                            }
                        }
                    });
                }
            });
            
            if (suspiciousChanges > 0) {
                this.reportSuspiciousActivity('SUSPICIOUS_DOM_CHANGES', {
                    changeCount: suspiciousChanges,
                    mutationCount: mutations.length
                });
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['src', 'href', 'onclick']
        });
    }

    /**
     * Analyze key sequence for suspicious patterns
     */
    analyzeKeySequence(sequence) {
        if (sequence.length < 5) return;
        
        // Check for rapid typing (possible bot)
        const recentKeys = sequence.slice(-5);
        const timeSpan = recentKeys[recentKeys.length - 1].timestamp - recentKeys[0].timestamp;
        
        if (timeSpan < 100) { // 5 keys in less than 100ms
            this.reportSuspiciousActivity('RAPID_TYPING', {
                keyCount: recentKeys.length,
                timeSpan
            });
        }
        
        // Check for suspicious key combinations
        const hasCtrlAlt = recentKeys.some(k => k.ctrlKey && k.altKey);
        const hasF12 = recentKeys.some(k => k.key === 'F12');
        
        if (hasCtrlAlt || hasF12) {
            this.reportSuspiciousActivity('DEVELOPER_TOOLS_ACCESS', {
                keys: recentKeys.map(k => k.key)
            });
        }
    }

    /**
     * Report suspicious activity
     */
    reportSuspiciousActivity(type, details) {
        const activity = {
            type,
            details,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent
        };
        
        this.suspiciousActivities.push(activity);
        
        // Keep only last 100 activities
        if (this.suspiciousActivities.length > 100) {
            this.suspiciousActivities.shift();
        }
        
        console.warn('🚨 Suspicious activity detected:', activity);
        
        // Count occurrences of this type
        const typeCount = this.incrementActivityCount(type);
        
        // Trigger alerts for repeated suspicious activities
        if (typeCount >= this.activityThresholds.suspiciousPatterns) {
            this.triggerSecurityAlert(type, typeCount);
        }
        
        // Log to security error handler if available
        if (window.app && window.app.securityErrorHandler) {
            window.app.securityErrorHandler.logSecurityEvent('SUSPICIOUS_ACTIVITY', activity);
        }
    }

    /**
     * Increment activity count
     */
    incrementActivityCount(type) {
        const current = this.activityCounters.get(type) || 0;
        const newCount = current + 1;
        this.activityCounters.set(type, newCount);
        return newCount;
    }

    /**
     * Trigger security alert
     */
    triggerSecurityAlert(type, count) {
        console.error(`🚨 Security alert: ${type} detected ${count} times`);
        
        const alertData = {
            type,
            count,
            timestamp: new Date().toISOString(),
            recentActivities: this.suspiciousActivities.filter(a => a.type === type).slice(-5)
        };
        
        // Show user notification
        if (window.app && window.app.ui) {
            window.app.ui.showDialog(
                'Security Alert',
                `Suspicious activity detected: ${type}. Please contact support if you believe this is an error.`,
                'warning'
            );
        }
        
        // Log to security error handler
        if (window.app && window.app.securityErrorHandler) {
            window.app.securityErrorHandler.handleSecurityError('SECURITY_ALERT', alertData);
        }
    }

    /**
     * Get monitoring statistics
     */
    getMonitoringStats() {
        return {
            active: this.monitoringActive,
            suspiciousActivities: this.suspiciousActivities.length,
            activityCounts: Object.fromEntries(this.activityCounters),
            baseline: this.performanceBaseline,
            uptime: this.performanceBaseline ? Date.now() - this.performanceBaseline.timestamp : 0
        };
    }

    /**
     * Get recent suspicious activities
     */
    getRecentActivities(limit = 10) {
        return this.suspiciousActivities.slice(-limit);
    }

    /**
     * Clear activity counters
     */
    clearCounters() {
        this.activityCounters.clear();
        console.log('🔄 Security monitoring counters cleared');
    }

    /**
     * Export security report
     */
    exportSecurityReport() {
        return {
            timestamp: new Date().toISOString(),
            monitoringStats: this.getMonitoringStats(),
            suspiciousActivities: this.suspiciousActivities,
            thresholds: this.activityThresholds,
            userAgent: navigator.userAgent,
            url: window.location.href
        };
    }
}
