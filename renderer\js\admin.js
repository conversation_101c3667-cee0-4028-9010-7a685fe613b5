// Admin Manager for S.T.E.V.I DOS Electron App
export class AdminManager {
    constructor(dataManager, uiManager) {
        this.data = dataManager;
        this.ui = uiManager;
        this.items = [];
        this.filteredItems = [];
        this.currentEditingItem = null;
        this.users = [];
        this.filteredUsers = [];
        this.currentEditingUser = null;
        this.roles = [];
        this.filteredRoles = [];
        this.currentEditingRole = null;
        this.allPermissions = [];
        this.currentSection = 'items';
    }

    async initialize() {
        try {
            console.log('AdminManager.initialize() started');
            
            // Load data
            console.log('Loading items...');
            await this.loadItems();
            console.log('Items loaded successfully');
            
            console.log('Loading users...');
            await this.loadUsers();
            console.log('Users loaded successfully');

            console.log('Loading roles...');
            await this.loadRoles();
            console.log('Roles loaded successfully');

            console.log('Loading permissions...');
            await this.loadPermissions();
            console.log('Permissions loaded successfully');

            // Set up event listeners
            console.log('Setting up event listeners...');
            this.setupEventListeners();
            console.log('Event listeners set up successfully');

            // Render current section
            console.log('Rendering current section...');
            this.renderCurrentSection();
            console.log('Current section rendered successfully');

            console.log('AdminManager.initialize() completed successfully');

        } catch (error) {
            console.error('Error initializing admin manager:', error);
            this.ui.showDialog('Error', `Failed to initialize admin interface: ${error.message}`, 'error');
            throw error; // Re-throw to be caught by the calling function
        }
    }

    setupEventListeners() {
        // Admin navigation
        document.querySelectorAll('.admin-nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.target.dataset.section;
                this.switchSection(section);
            });
        });

        // Item management
        const addItemBtn = document.getElementById('add-item-btn');
        if (addItemBtn) {
            addItemBtn.addEventListener('click', () => this.showAddItemForm());
        }

        const itemSearchInput = document.getElementById('item-search');
        if (itemSearchInput) {
            itemSearchInput.addEventListener('input', (e) => this.filterItems());
        }

        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => this.filterItems());
        }

        const statusFilter = document.getElementById('status-filter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.filterItems());
        }

        // User management
        const addUserBtn = document.getElementById('add-user-btn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => this.showAddUserForm());
        }

        const userSearchInput = document.getElementById('user-search');
        if (userSearchInput) {
            userSearchInput.addEventListener('input', (e) => this.filterUsers());
        }

        const roleFilter = document.getElementById('role-filter');
        if (roleFilter) {
            roleFilter.addEventListener('change', () => this.filterUsers());
        }

        // Role management
        const addRoleBtn = document.getElementById('add-role-btn');
        if (addRoleBtn) {
            addRoleBtn.addEventListener('click', () => this.showAddRoleForm());
        }

        const roleSearchInput = document.getElementById('role-search');
        if (roleSearchInput) {
            roleSearchInput.addEventListener('input', (e) => this.filterRoles());
        }

        const roleTypeFilter = document.getElementById('role-type-filter');
        if (roleTypeFilter) {
            roleTypeFilter.addEventListener('change', () => this.filterRoles());
        }
    }

    async loadItems() {
        try {
            this.items = await this.data.getAll('items');
            this.filteredItems = [...this.items];
        } catch (error) {
            console.error('Error loading items:', error);
            this.items = [];
            this.filteredItems = [];
        }
    }

    filterItems() {
        const searchTerm = document.getElementById('item-search')?.value.toLowerCase() || '';
        const categoryFilter = document.getElementById('category-filter')?.value || '';
        const statusFilter = document.getElementById('status-filter')?.value || '';

        this.filteredItems = this.items.filter(item => {
            const matchesSearch = !searchTerm || 
                item.name.toLowerCase().includes(searchTerm) ||
                item.description?.toLowerCase().includes(searchTerm) ||
                item.category.toLowerCase().includes(searchTerm);

            const matchesCategory = !categoryFilter || item.category === categoryFilter;
            
            const matchesStatus = !statusFilter || 
                (statusFilter === 'active' && item.active) ||
                (statusFilter === 'inactive' && !item.active);

            return matchesSearch && matchesCategory && matchesStatus;
        });

        this.renderItemsList();
    }

    renderItemsList() {
        const itemsList = document.getElementById('items-list');
        if (!itemsList) return;

        if (this.filteredItems.length === 0) {
            itemsList.innerHTML = `
                <div class="no-items">
                    <p>No items found.</p>
                    ${this.items.length === 0 ? '<p>Click "ADD NEW ITEM" to create your first item.</p>' : ''}
                </div>
            `;
            return;
        }

        const tableHTML = `
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Category</th>
                        <th>Unit Type</th>
                        <th>Current Stock</th>
                        <th>Min Threshold</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.filteredItems.map(item => this.renderItemRow(item)).join('')}
                </tbody>
            </table>
        `;

        itemsList.innerHTML = tableHTML;
        this.attachItemEventListeners();
    }

    renderItemRow(item) {
        const stockClass = item.minimum_threshold && item.current_stock <= item.minimum_threshold ? 'stock-low' : 'stock-ok';
        const statusClass = item.active ? 'status-active' : 'status-inactive';
        const statusText = item.active ? 'Active' : 'Inactive';

        return `
            <tr data-item-id="${item.id}">
                <td>${item.name}</td>
                <td>${item.category}</td>
                <td>${item.unit_type}</td>
                <td class="${stockClass}">${item.current_stock}</td>
                <td>${item.minimum_threshold || 'N/A'}</td>
                <td class="${statusClass}">${statusText}</td>
                <td>
                    <div class="item-actions">
                        <button class="edit-btn" data-action="edit" data-item-id="${item.id}">Edit</button>
                        <button class="delete-btn" data-action="delete" data-item-id="${item.id}">Delete</button>
                        <button class="toggle-btn" data-action="toggle" data-item-id="${item.id}">
                            ${item.active ? 'Deactivate' : 'Activate'}
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    attachItemEventListeners() {
        // Edit buttons
        document.querySelectorAll('[data-action="edit"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const itemId = e.target.dataset.itemId;
                this.showEditItemForm(itemId);
            });
        });

        // Delete buttons
        document.querySelectorAll('[data-action="delete"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const itemId = e.target.dataset.itemId;
                this.deleteItem(itemId);
            });
        });

        // Toggle buttons
        document.querySelectorAll('[data-action="toggle"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const itemId = e.target.dataset.itemId;
                this.toggleItemStatus(itemId);
            });
        });
    }

    showAddItemForm() {
        this.currentEditingItem = null;
        this.showItemForm();
    }

    showEditItemForm(itemId) {
        this.currentEditingItem = this.items.find(item => item.id === itemId);
        if (this.currentEditingItem) {
            this.showItemForm(this.currentEditingItem);
        }
    }

    showItemForm(item = null) {
        const isEdit = !!item;
        const title = isEdit ? 'Edit Item' : 'Add New Item';
        
        const formHTML = `
            <div class="modal-overlay">
                <div class="form-modal">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="close-btn" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <form id="item-form" class="modal-form">
                        <div class="form-group">
                            <label for="item-name">Name *</label>
                            <input type="text" id="item-name" name="name" required 
                                   value="${item?.name || ''}" maxlength="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="item-description">Description</label>
                            <textarea id="item-description" name="description" rows="3" 
                                      maxlength="500">${item?.description || ''}</textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="item-category">Category *</label>
                                <select id="item-category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="food" ${item?.category === 'food' ? 'selected' : ''}>Food</option>
                                    <option value="hygiene" ${item?.category === 'hygiene' ? 'selected' : ''}>Hygiene</option>
                                    <option value="clothing" ${item?.category === 'clothing' ? 'selected' : ''}>Clothing</option>
                                    <option value="medical" ${item?.category === 'medical' ? 'selected' : ''}>Medical</option>
                                    <option value="other" ${item?.category === 'other' ? 'selected' : ''}>Other</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="item-unit-type">Unit Type *</label>
                                <input type="text" id="item-unit-type" name="unit_type" required 
                                       value="${item?.unit_type || ''}" maxlength="20"
                                       placeholder="e.g., piece, bottle, bar">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="item-current-stock">Current Stock *</label>
                                <input type="number" id="item-current-stock" name="current_stock" required 
                                       value="${item?.current_stock || 0}" min="0">
                            </div>
                            
                            <div class="form-group">
                                <label for="item-min-threshold">Minimum Threshold</label>
                                <input type="number" id="item-min-threshold" name="minimum_threshold" 
                                       value="${item?.minimum_threshold || ''}" min="0">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="item-cost">Cost per Unit</label>
                                <input type="number" id="item-cost" name="cost_per_unit" step="0.01" 
                                       value="${item?.cost_per_unit || ''}" min="0">
                            </div>
                            
                            <div class="form-group">
                                <label for="item-supplier">Supplier</label>
                                <input type="text" id="item-supplier" name="supplier" 
                                       value="${item?.supplier || ''}" maxlength="100">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="item-active" name="active" 
                                       ${item?.active !== false ? 'checked' : ''}>
                                Active
                            </label>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                            <button type="submit" class="primary-button">${isEdit ? 'Update' : 'Create'} Item</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', formHTML);
        
        // Set up form submission
        const form = document.getElementById('item-form');
        form.addEventListener('submit', (e) => this.handleItemFormSubmit(e));
        
        // Focus first input
        document.getElementById('item-name').focus();
    }

    async handleItemFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const itemData = {
            name: formData.get('name').trim(),
            description: formData.get('description').trim(),
            category: formData.get('category'),
            unit_type: formData.get('unit_type').trim(),
            current_stock: parseInt(formData.get('current_stock')),
            minimum_threshold: formData.get('minimum_threshold') ? parseInt(formData.get('minimum_threshold')) : null,
            cost_per_unit: formData.get('cost_per_unit') ? parseFloat(formData.get('cost_per_unit')) : null,
            supplier: formData.get('supplier').trim() || null,
            active: formData.has('active')
        };

        // Validation
        if (!itemData.name || !itemData.category || !itemData.unit_type) {
            this.ui.showDialog('Validation Error', 'Please fill in all required fields.', 'warning');
            return;
        }

        if (itemData.current_stock < 0) {
            this.ui.showDialog('Validation Error', 'Current stock cannot be negative.', 'warning');
            return;
        }

        try {
            const submitBtn = e.target?.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = this.currentEditingItem ? 'Updating...' : 'Creating...';
            }

            if (this.currentEditingItem) {
                // Update existing item
                await this.updateItem(this.currentEditingItem.id, itemData);
            } else {
                // Create new item
                await this.createItem(itemData);
            }

            // Close modal (with null check)
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }

            // Reload items
            await this.loadItems();
            this.filterItems();

            this.ui.showDialog('Success',
                `Item ${this.currentEditingItem ? 'updated' : 'created'} successfully!`,
                'success');

        } catch (error) {
            console.error('Error saving item:', error);
            this.ui.showDialog('Error', `Failed to save item: ${error.message}`, 'error');

            // Re-enable submit button (with null check)
            const submitBtn = e.target?.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = this.currentEditingItem ? 'Update Item' : 'Create Item';
            }
        }
    }

    async createItem(itemData) {
        // Add timestamps
        const now = new Date().toISOString();
        itemData.created_at = now;
        itemData.updated_at = now;

        // Generate ID
        itemData.id = this.generateId();

        return await this.data.insert('items', itemData);
    }

    async updateItem(itemId, itemData) {
        // Add update timestamp
        itemData.updated_at = new Date().toISOString();

        return await this.data.update('items', itemId, itemData);
    }

    async deleteItem(itemId) {
        const item = this.items.find(i => i.id === itemId);
        if (!item) return;

        const confirmed = await this.ui.showConfirmDialog(
            'Delete Item',
            `Are you sure you want to delete "${item.name}"? This action cannot be undone.`,
            'Delete',
            'Cancel'
        );

        if (!confirmed) return;

        try {
            await this.data.delete('items', itemId);

            // Reload items
            await this.loadItems();
            this.filterItems();

            this.ui.showDialog('Success', 'Item deleted successfully!', 'success');

        } catch (error) {
            console.error('Error deleting item:', error);
            this.ui.showDialog('Error', `Failed to delete item: ${error.message}`, 'error');
        }
    }

    async toggleItemStatus(itemId) {
        const item = this.items.find(i => i.id === itemId);
        if (!item) return;

        try {
            const newStatus = !item.active;
            await this.updateItem(itemId, { active: newStatus });

            // Reload items
            await this.loadItems();
            this.filterItems();

            this.ui.showDialog('Success',
                `Item ${newStatus ? 'activated' : 'deactivated'} successfully!`,
                'success');

        } catch (error) {
            console.error('Error toggling item status:', error);
            this.ui.showDialog('Error', `Failed to update item status: ${error.message}`, 'error');
        }
    }

    generateId() {
        // Generate a simple UUID-like ID
        return 'item_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Section Management
    switchSection(section) {
        this.currentSection = section;

        // Update navigation buttons
        document.querySelectorAll('.admin-nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // Show/hide sections
        document.getElementById('items-section').style.display = section === 'items' ? 'block' : 'none';
        document.getElementById('users-section').style.display = section === 'users' ? 'block' : 'none';
        document.getElementById('roles-section').style.display = section === 'roles' ? 'block' : 'none';

        // Render current section
        this.renderCurrentSection();
    }

    renderCurrentSection() {
        if (this.currentSection === 'items') {
            this.renderItemsList();
        } else if (this.currentSection === 'users') {
            this.renderUsersList();
        } else if (this.currentSection === 'roles') {
            this.renderRolesList();
        }
    }

    // User Management Methods
    async loadUsers() {
        try {
            console.log('loadUsers() started');
            
            // Get auth token for API calls
            const supabase = this.data.getSupabaseClient();
            if (!supabase) {
                console.warn('No Supabase client available for user management');
                this.users = [];
                this.filteredUsers = [];
                return;
            }

            console.log('Getting session...');
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
                console.warn('No active session for admin API');
                this.users = [];
                this.filteredUsers = [];
                return;
            }
            console.log('Session obtained successfully');

            // Debug: Check current user details
            const { data: { user: currentUser } } = await supabase.auth.getUser();
            if (currentUser) {
                const currentUserRole = this.getUserRole(currentUser);
                console.log('Current user details:', {
                    email: currentUser.email,
                    role: currentUserRole,
                    user_metadata: currentUser.user_metadata,
                    app_metadata: currentUser.app_metadata,
                    raw_user_meta_data: currentUser.raw_user_meta_data,
                    raw_app_meta_data: currentUser.raw_app_meta_data
                });
            }

            // Try to use the new get_users_with_roles function first
            try {
                console.log('Trying get_users_with_roles_debug function...');
                const { data: usersWithRoles, error: functionError } = await supabase
                    .rpc('get_users_with_roles_debug');

                if (!functionError && usersWithRoles) {
                    console.log('Loaded users via get_users_with_roles function:', usersWithRoles);
                    this.users = usersWithRoles.map(user => ({
                        id: user.user_id,
                        email: user.email,
                        user_metadata: { full_name: user.full_name },
                        roles: user.roles,
                        permissions: user.permissions
                    }));
                    this.filteredUsers = [...this.users];
                    console.log('Users loaded successfully via function');
                    return;
                } else {
                    console.warn('get_users_with_roles function error:', functionError);
                }
            } catch (functionError) {
                console.warn('get_users_with_roles function failed, falling back to Edge Function:', functionError);
            }

            // Fallback to Edge Function
            console.log('Falling back to Edge Function...');
            console.log('Admin API URL:', this.getAdminApiUrl());
            
            const response = await fetch(this.getAdminApiUrl(), {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Edge Function error response:', errorData);
                throw new Error(errorData.error || `Failed to fetch users (${response.status})`);
            }

            const responseData = await response.json();
            console.log('Edge Function response:', responseData);

            const { users } = responseData;
            this.users = users || [];
            this.filteredUsers = [...this.users];

            console.log(`Loaded ${this.users.length} IHARC users via Edge Function`);

            // Debug: log user details
            this.users.forEach(user => {
                const role = this.getUserRole(user);
                console.log(`User: ${user.email}, Role: ${role}, Metadata:`, user.user_metadata, user.app_metadata);
            });
        } catch (error) {
            console.error('Error loading users:', error);

            // Fallback: try to get at least the current user
            try {
                const supabase = this.data.getSupabaseClient();
                const { data: { user } } = await supabase.auth.getUser();
                if (user) {
                    const userRole = this.getUserRole(user);
                    if (userRole === 'iharc_staff' || userRole === 'iharc_admin' || userRole === 'iharc_supervisor') {
                        this.users = [user];
                    } else {
                        this.users = [];
                    }
                } else {
                    this.users = [];
                }
            } catch (fallbackError) {
                console.error('Fallback user loading failed:', fallbackError);
                this.users = [];
            }
            this.filteredUsers = [...this.users];
        }
    }

    getAdminApiUrl() {
        // Use Supabase Edge Function for admin operations
        const supabaseUrl = this.data.config.get('supabase.url');
        return `${supabaseUrl}/functions/v1/admin-user-management`;
    }

    getUserRole(user) {
        // Helper method to extract role from user object (standardized approach)
        
        // First check if user has roles array (from new system)
        if (user.roles && user.roles.length > 0) {
            // Return the highest priority role
            if (user.roles.includes('iharc_admin')) return 'iharc_admin';
            if (user.roles.includes('iharc_supervisor')) return 'iharc_supervisor';
            if (user.roles.includes('iharc_staff')) return 'iharc_staff';
            if (user.roles.includes('iharc_volunteer')) return 'iharc_volunteer';
            return user.roles[0]; // Return first role if none match
        }

        // Check direct role assignment (legacy)
        if (user.role) {
            return user.role;
        }

        // Check user metadata (legacy)
        const userMetadata = user.user_metadata || user.raw_user_meta_data || {};
        if (userMetadata.role) {
            return userMetadata.role;
        }

        // Check app metadata (legacy)
        const appMetadata = user.app_metadata || user.raw_app_meta_data || {};
        if (appMetadata.role) {
            return appMetadata.role;
        }

        // Check JWT roles (new system)
        if (appMetadata.roles && appMetadata.roles.length > 0) {
            const roles = appMetadata.roles;
            if (roles.includes('iharc_admin')) return 'iharc_admin';
            if (roles.includes('iharc_supervisor')) return 'iharc_supervisor';
            if (roles.includes('iharc_staff')) return 'iharc_staff';
            if (roles.includes('iharc_volunteer')) return 'iharc_volunteer';
            return roles[0];
        }

        return 'unknown';
    }

    filterUsers() {
        const searchTerm = document.getElementById('user-search')?.value.toLowerCase() || '';
        const roleFilter = document.getElementById('role-filter')?.value || '';

        this.filteredUsers = this.users.filter(user => {
            const matchesSearch = !searchTerm ||
                user.email.toLowerCase().includes(searchTerm) ||
                (user.user_metadata?.full_name || '').toLowerCase().includes(searchTerm);

            const userRole = this.getUserRole(user);
            const matchesRole = !roleFilter || userRole === roleFilter;

            return matchesSearch && matchesRole;
        });

        this.renderUsersList();
    }

    renderUsersList() {
        const usersList = document.getElementById('users-list');
        if (!usersList) return;

        if (this.filteredUsers.length === 0) {
            usersList.innerHTML = `
                <div class="no-items">
                    <p>No users found.</p>
                    <p><strong>Note:</strong> User management uses Supabase Edge Function with service role key.</p>
                    <p>If you're seeing this message, the Edge Function may not be deployed or configured.</p>
                    <p>Click "ADD NEW USER" to create a new user account.</p>
                </div>
            `;
            return;
        }

        const tableHTML = `
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Email</th>
                        <th>Name</th>
                        <th>Staff ID</th>
                        <th>Ranger ID</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Last Sign In</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.filteredUsers.map(user => this.renderUserRow(user)).join('')}
                </tbody>
            </table>
        `;

        usersList.innerHTML = tableHTML;
        this.attachUserEventListeners();
    }

    renderUserRow(user) {
        const role = this.getUserRole(user);
        const roleClass = role === 'iharc_admin' ? 'role-admin' : 'role-staff';
        const name = user.user_metadata?.full_name || user.user_metadata?.name || 'N/A';
        const staffId = user.iharc_staff_id || 'Not assigned';
        const rangerId = user.ranger_id || 'Not assigned';
        const created = new Date(user.created_at).toLocaleDateString();
        const lastSignIn = user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : 'Never';

        // Determine role elevation button
        const isCurrentlyAdmin = role === 'iharc_admin';
        const elevationAction = isCurrentlyAdmin ? 'demote-user' : 'promote-user';
        const elevationText = isCurrentlyAdmin ? 'Demote to Staff' : 'Promote to Admin';
        const elevationClass = isCurrentlyAdmin ? 'demote-btn' : 'promote-btn';

        return `
            <tr data-user-id="${user.id}">
                <td>${user.email}</td>
                <td>${name}</td>
                <td>${staffId}</td>
                <td>${rangerId}</td>
                <td class="${roleClass}">${role}</td>
                <td>${created}</td>
                <td>${lastSignIn}</td>
                <td>
                    <div class="user-actions">
                        <button class="edit-btn" data-action="edit-user" data-user-id="${user.id}">Edit</button>
                        <button class="${elevationClass}" data-action="${elevationAction}" data-user-id="${user.id}">${elevationText}</button>
                        <button class="reset-btn" data-action="reset-password" data-user-id="${user.id}">Reset Password</button>
                        <button class="delete-btn" data-action="delete-user" data-user-id="${user.id}">Delete</button>
                    </div>
                </td>
            </tr>
        `;
    }

    attachUserEventListeners() {
        // Edit user buttons
        document.querySelectorAll('[data-action="edit-user"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.target.dataset.userId;
                this.showEditUserForm(userId);
            });
        });

        // Promote user buttons
        document.querySelectorAll('[data-action="promote-user"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.target.dataset.userId;
                this.promoteUser(userId);
            });
        });

        // Demote user buttons
        document.querySelectorAll('[data-action="demote-user"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.target.dataset.userId;
                this.demoteUser(userId);
            });
        });

        // Reset password buttons
        document.querySelectorAll('[data-action="reset-password"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.target.dataset.userId;
                this.resetUserPassword(userId);
            });
        });

        // Delete user buttons
        document.querySelectorAll('[data-action="delete-user"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const userId = e.target.dataset.userId;
                this.deleteUser(userId);
            });
        });
    }

    showAddUserForm() {
        this.currentEditingUser = null;
        this.showUserForm();
    }

    showEditUserForm(userId) {
        this.currentEditingUser = this.users.find(user => user.id === userId);
        if (this.currentEditingUser) {
            this.showUserForm(this.currentEditingUser);
        }
    }

    showUserForm(user = null) {
        const isEdit = !!user;
        const title = isEdit ? 'Edit User' : 'Add New User';

        const formHTML = `
            <div class="modal-overlay">
                <div class="form-modal">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="close-btn" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <form id="user-form" class="modal-form">
                        <div class="form-group">
                            <label for="user-email">Email *</label>
                            <input type="email" id="user-email" name="email" required
                                   value="${user?.email || ''}" ${isEdit ? 'readonly' : ''}>
                        </div>

                        <div class="form-group">
                            <label for="user-full-name">Full Name</label>
                            <input type="text" id="user-full-name" name="full_name"
                                   value="${user?.user_metadata?.full_name || ''}" maxlength="100">
                        </div>

                        <div class="form-group">
                            <label for="user-staff-id">IHARC Staff ID</label>
                            <input type="text" id="user-staff-id" name="iharc_staff_id"
                                   value="${user?.iharc_staff_id || ''}" maxlength="20" placeholder="e.g., IHARC001">
                            <small>Unique identifier for IHARC staff member</small>
                        </div>

                        <div class="form-group">
                            <label for="user-ranger-id">Ranger ID</label>
                            <input type="text" id="user-ranger-id" name="ranger_id"
                                   value="${user?.ranger_id || ''}" maxlength="10" placeholder="e.g., 101">
                            <small>Ranger identification number (admin only)</small>
                        </div>

                        <div class="form-group">
                            <label for="user-role">Role *</label>
                            <select id="user-role" name="role" required>
                                <option value="iharc_staff" ${!user || this.getUserRole(user) === 'iharc_staff' ? 'selected' : ''}>Staff (Default)</option>
                                <option value="iharc_admin" ${user && this.getUserRole(user) === 'iharc_admin' ? 'selected' : ''}>Admin</option>
                            </select>
                        </div>

                        ${!isEdit ? `
                        <div class="form-group">
                            <label for="user-password">Password *</label>
                            <input type="password" id="user-password" name="password" required minlength="6">
                        </div>

                        <div class="form-group">
                            <label for="user-confirm-password">Confirm Password *</label>
                            <input type="password" id="user-confirm-password" name="confirm_password" required minlength="6">
                        </div>
                        ` : ''}

                        <div class="form-actions">
                            <button type="button" class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                            <button type="submit" class="primary-button">${isEdit ? 'Update' : 'Create'} User</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', formHTML);

        // Set up form submission
        const form = document.getElementById('user-form');
        form.addEventListener('submit', (e) => this.handleUserFormSubmit(e));

        // Focus first input
        document.getElementById('user-email').focus();
    }

    async handleUserFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const userData = {
            email: formData.get('email').trim(),
            full_name: formData.get('full_name').trim(),
            iharc_staff_id: formData.get('iharc_staff_id').trim(),
            ranger_id: formData.get('ranger_id').trim(),
            role: formData.get('role')
        };

        if (!this.currentEditingUser) {
            userData.password = formData.get('password');
            userData.confirm_password = formData.get('confirm_password');

            // Validation for new users
            if (userData.password !== userData.confirm_password) {
                this.ui.showDialog('Validation Error', 'Passwords do not match.', 'warning');
                return;
            }
        }

        // Validation
        if (!userData.email || !userData.role) {
            this.ui.showDialog('Validation Error', 'Please fill in all required fields.', 'warning');
            return;
        }

        try {
            const submitBtn = e.target?.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = this.currentEditingUser ? 'Updating...' : 'Creating...';
            }

            if (this.currentEditingUser) {
                // Update existing user
                await this.updateUser(this.currentEditingUser.id, userData);
            } else {
                // Create new user
                await this.createUser(userData);
            }

            // Close modal (with null check)
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }

            // Reload users
            await this.loadUsers();
            this.filterUsers();

            // Show detailed success message
            const action = this.currentEditingUser ? 'updated' : 'created';
            const roleText = userData.role === 'iharc_admin' ? 'Administrator' : 'Staff';

            if (action === 'created') {
                this.ui.showDialog('Success',
                    `User ${userData.email} created successfully!\n\nRole: ${roleText}\nThe user has been automatically assigned the correct permissions and can now log in.`,
                    'success');
            } else {
                this.ui.showDialog('Success',
                    `User ${userData.email} updated successfully!\n\nRole: ${roleText}`,
                    'success');
            }

        } catch (error) {
            console.error('Error saving user:', error);
            this.ui.showDialog('Error', `Failed to save user: ${error.message}`, 'error');

            // Re-enable submit button (with null check)
            const submitBtn = e.target?.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = this.currentEditingUser ? 'Update User' : 'Create User';
            }
        }
    }

    async createUser(userData) {
        console.log('Creating user with data:', userData);

        const supabase = this.data.getSupabaseClient();
        if (!supabase) {
            throw new Error('Supabase client not available');
        }

        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
            throw new Error('No active session');
        }

        const requestBody = {
            email: userData.email,
            password: userData.password,
            full_name: userData.full_name,
            iharc_staff_id: userData.iharc_staff_id,
            ranger_id: userData.ranger_id,
            role: userData.role
        };

        console.log('Sending request to Edge Function:', requestBody);

        // Call Supabase Edge Function
        const response = await fetch(this.getAdminApiUrl(), {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${session.access_token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('Edge Function response status:', response.status);

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Edge Function error:', errorData);
            throw new Error(errorData.error || 'Failed to create user');
        }

        const responseData = await response.json();
        console.log('User created successfully:', responseData);

        return responseData.user;
    }

    async updateUser(userId, userData) {
        const supabase = this.data.getSupabaseClient();
        if (!supabase) {
            throw new Error('Supabase client not available');
        }

        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
            throw new Error('No active session');
        }

        // Call Supabase Edge Function
        const response = await fetch(`${this.getAdminApiUrl()}/${userId}`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${session.access_token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                full_name: userData.full_name,
                iharc_staff_id: userData.iharc_staff_id,
                ranger_id: userData.ranger_id,
                role: userData.role
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to update user');
        }

        const { user } = await response.json();
        return user;
    }

    async promoteUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        const currentRole = this.getUserRole(user);
        if (currentRole === 'iharc_admin') {
            this.ui.showDialog('Info', `${user.email} is already an admin.`, 'info');
            return;
        }

        const confirmed = await this.ui.showConfirmDialog(
            'Promote User',
            `Promote "${user.email}" to Admin role? This will give them full administrative privileges.`,
            'Promote to Admin',
            'Cancel'
        );

        if (!confirmed) return;

        try {
            await this.updateUser(userId, {
                full_name: user.user_metadata?.full_name || '',
                role: 'iharc_admin'
            });

            // Reload users to reflect changes
            await this.loadUsers();
            this.filterUsers();

            this.ui.showDialog('Success', `${user.email} has been promoted to Admin.`, 'success');

        } catch (error) {
            console.error('Error promoting user:', error);
            this.ui.showDialog('Error', `Failed to promote user: ${error.message}`, 'error');
        }
    }

    async demoteUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        const currentRole = this.getUserRole(user);
        if (currentRole === 'iharc_staff') {
            this.ui.showDialog('Info', `${user.email} is already staff.`, 'info');
            return;
        }

        const confirmed = await this.ui.showConfirmDialog(
            'Demote User',
            `Demote "${user.email}" to Staff role? This will remove their administrative privileges.`,
            'Demote to Staff',
            'Cancel'
        );

        if (!confirmed) return;

        try {
            await this.updateUser(userId, {
                full_name: user.user_metadata?.full_name || '',
                role: 'iharc_staff'
            });

            // Reload users to reflect changes
            await this.loadUsers();
            this.filterUsers();

            this.ui.showDialog('Success', `${user.email} has been demoted to Staff.`, 'success');

        } catch (error) {
            console.error('Error demoting user:', error);
            this.ui.showDialog('Error', `Failed to demote user: ${error.message}`, 'error');
        }
    }

    async deleteUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        const confirmed = await this.ui.showConfirmDialog(
            'Delete User',
            `Are you sure you want to delete user "${user.email}"? This action cannot be undone.`,
            'Delete',
            'Cancel'
        );

        if (!confirmed) return;

        try {
            const supabase = this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
                throw new Error('No active session');
            }

            // Call Supabase Edge Function
            const response = await fetch(`${this.getAdminApiUrl()}/${userId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to delete user');
            }

            // Reload users to reflect changes
            await this.loadUsers();
            this.filterUsers();

            this.ui.showDialog('Success', `User ${user.email} deleted successfully!`, 'success');

        } catch (error) {
            console.error('Error deleting user:', error);
            this.ui.showDialog('Error', `Failed to delete user: ${error.message}`, 'error');
        }
    }

    async resetUserPassword(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        const confirmed = await this.ui.showConfirmDialog(
            'Reset Password',
            `Send password reset email to "${user.email}"?`,
            'Send Reset Email',
            'Cancel'
        );

        if (!confirmed) return;

        try {
            const supabase = this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
                throw new Error('No active session');
            }

            // Call Supabase Edge Function
            const response = await fetch(`${this.getAdminApiUrl()}/${userId}/reset-password`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to reset password');
            }

            this.ui.showDialog('Success', `Password reset email sent to ${user.email}`, 'success');

        } catch (error) {
            console.error('Error resetting password:', error);
            this.ui.showDialog('Error', `Failed to send reset email: ${error.message}`, 'error');
        }
    }

    // Role Management Methods
    async loadRoles() {
        try {
            const supabase = this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data, error } = await supabase.rpc('get_all_roles_for_admin');

            if (error) {
                console.error('Error loading roles:', error);
                throw error;
            }

            this.roles = data || [];
            this.filteredRoles = [...this.roles];
            console.log('Loaded roles:', this.roles);

        } catch (error) {
            console.error('Error in loadRoles:', error);
            this.ui.showDialog('Error', `Failed to load roles: ${error.message}`, 'error');
            this.roles = [];
            this.filteredRoles = [];
        }
    }

    async loadPermissions() {
        try {
            const supabase = this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data, error } = await supabase.rpc('get_all_permissions');

            if (error) {
                console.error('Error loading permissions:', error);
                throw error;
            }

            this.allPermissions = data || [];
            console.log('Loaded permissions:', this.allPermissions);

        } catch (error) {
            console.error('Error in loadPermissions:', error);
            this.ui.showDialog('Error', `Failed to load permissions: ${error.message}`, 'error');
            this.allPermissions = [];
        }
    }

    renderRolesList() {
        const rolesList = document.getElementById('roles-list');
        if (!rolesList) return;

        if (this.filteredRoles.length === 0) {
            rolesList.innerHTML = '<div class="no-data">No roles found</div>';
            return;
        }

        const rolesHtml = this.filteredRoles.map(role => `
            <div class="role-item" data-role-id="${role.id}">
                <div class="role-info">
                    <div class="role-name">
                        <strong>${role.display_name || role.name}</strong>
                        ${role.is_system_role ? '<span class="system-badge">SYSTEM</span>' : '<span class="custom-badge">CUSTOM</span>'}
                    </div>
                    <div class="role-details">
                        <span class="role-description">${role.description || 'No description'}</span>
                    </div>
                    <div class="role-stats">
                        <span class="permission-count">${role.permission_count} permissions</span>
                        <span class="user-count">${role.user_count} users</span>
                    </div>
                </div>
                <div class="role-actions">
                    <button class="secondary-button view-role-btn" data-role-id="${role.id}">VIEW</button>
                    ${!role.is_system_role ? `
                        <button class="primary-button edit-role-btn" data-role-id="${role.id}">EDIT</button>
                        <button class="danger-button delete-role-btn" data-role-id="${role.id}"
                                ${role.user_count > 0 ? 'disabled title="Cannot delete role with assigned users"' : ''}>DELETE</button>
                    ` : ''}
                </div>
            </div>
        `).join('');

        rolesList.innerHTML = rolesHtml;

        // Add event listeners
        rolesList.querySelectorAll('.view-role-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const roleId = e.target.dataset.roleId;
                this.viewRole(roleId);
            });
        });

        rolesList.querySelectorAll('.edit-role-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const roleId = e.target.dataset.roleId;
                this.editRole(roleId);
            });
        });

        rolesList.querySelectorAll('.delete-role-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const roleId = e.target.dataset.roleId;
                this.deleteRole(roleId);
            });
        });
    }

    filterRoles() {
        const searchTerm = document.getElementById('role-search')?.value.toLowerCase() || '';
        const typeFilter = document.getElementById('role-type-filter')?.value || '';

        this.filteredRoles = this.roles.filter(role => {
            const matchesSearch = role.name.toLowerCase().includes(searchTerm) ||
                                role.display_name?.toLowerCase().includes(searchTerm) ||
                                role.description?.toLowerCase().includes(searchTerm);

            const matchesType = !typeFilter ||
                              (typeFilter === 'system' && role.is_system_role) ||
                              (typeFilter === 'custom' && !role.is_system_role);

            return matchesSearch && matchesType;
        });

        this.renderRolesList();
    }

    showAddRoleForm() {
        // TODO: Implement role creation form
        this.ui.showDialog('Info', 'Role creation form coming soon!', 'info');
    }

    viewRole(roleId) {
        // TODO: Implement role viewing
        this.ui.showDialog('Info', `View role ${roleId} - coming soon!`, 'info');
    }

    editRole(roleId) {
        // TODO: Implement role editing
        this.ui.showDialog('Info', `Edit role ${roleId} - coming soon!`, 'info');
    }

    async deleteRole(roleId) {
        // TODO: Implement role deletion
        this.ui.showDialog('Info', `Delete role ${roleId} - coming soon!`, 'info');
    }
}
