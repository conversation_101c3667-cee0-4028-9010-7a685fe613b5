// Azure Blob Storage Service for S.T.E.V.I Retro
// Handles secure file uploads and downloads to Azure Blob Storage

import { BlobServiceClient } from '@azure/storage-blob';
import { ClientSecretCredential } from '@azure/identity';
import { azureConfig } from './azure-config.js';

export class AzureStorageService {
    constructor() {
        this.blobServiceClient = null;
        this.containerClient = null;
        this.isInitialized = false;
    }

    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            // Get Azure configuration
            await azureConfig.initialize();
            const config = azureConfig.getConfig();
            const credentials = azureConfig.getCredentials();

            // Create credential using service principal
            const credential = new ClientSecretCredential(
                credentials.tenantId,
                credentials.clientId,
                credentials.clientSecret
            );

            // Create blob service client
            this.blobServiceClient = new BlobServiceClient(
                config.blobEndpoint,
                credential
            );

            // Get container client
            this.containerClient = this.blobServiceClient.getContainerClient(config.containerName);

            // Verify container exists
            const exists = await this.containerClient.exists();
            if (!exists) {
                throw new Error(`Container '${config.containerName}' does not exist`);
            }

            this.isInitialized = true;
            console.log('Azure Storage Service initialized successfully');

        } catch (error) {
            console.error('Failed to initialize Azure Storage Service:', error);
            throw new Error(`Azure Storage initialization failed: ${error.message}`);
        }
    }

    async uploadFile(file, incidentId, progressCallback = null) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            // Generate secure file path
            const filePath = azureConfig.generateFilePath(incidentId, file.name);
            
            // Get blob client
            const blobClient = this.containerClient.getBlockBlobClient(filePath);

            // Set blob properties
            const blobOptions = {
                blobHTTPHeaders: {
                    blobContentType: file.type || 'application/octet-stream'
                },
                metadata: {
                    originalName: file.name,
                    incidentId: incidentId.toString(),
                    uploadedAt: new Date().toISOString(),
                    fileSize: file.size.toString()
                }
            };

            // Upload with progress tracking
            const uploadResponse = await blobClient.uploadData(
                await file.arrayBuffer(),
                {
                    ...blobOptions,
                    onProgress: progressCallback ? (progress) => {
                        const percentage = Math.round((progress.loadedBytes / file.size) * 100);
                        progressCallback({
                            percentage,
                            loadedBytes: progress.loadedBytes,
                            totalBytes: file.size,
                            fileName: file.name
                        });
                    } : undefined
                }
            );

            // Return file metadata
            return {
                id: this.generateFileId(),
                fileName: file.name,
                filePath: filePath,
                fileSize: file.size,
                contentType: file.type || 'application/octet-stream',
                uploadedAt: new Date().toISOString(),
                url: azureConfig.generateFileUrl(filePath),
                etag: uploadResponse.etag,
                incidentId: incidentId
            };

        } catch (error) {
            console.error('File upload failed:', error);
            throw new Error(`Upload failed: ${error.message}`);
        }
    }

    async uploadMultipleFiles(files, incidentId, progressCallback = null) {
        const results = [];
        const errors = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            try {
                const fileProgressCallback = progressCallback ? (progress) => {
                    progressCallback({
                        ...progress,
                        fileIndex: i,
                        totalFiles: files.length,
                        overallProgress: Math.round(((i + (progress.percentage / 100)) / files.length) * 100)
                    });
                } : null;

                const result = await this.uploadFile(file, incidentId, fileProgressCallback);
                results.push(result);

            } catch (error) {
                console.error(`Failed to upload file ${file.name}:`, error);
                errors.push({
                    fileName: file.name,
                    error: error.message
                });
            }
        }

        return {
            successful: results,
            failed: errors,
            totalUploaded: results.length,
            totalFailed: errors.length
        };
    }

    async downloadFile(filePath) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const blobClient = this.containerClient.getBlockBlobClient(filePath);
            const downloadResponse = await blobClient.download();
            
            return {
                content: await this.streamToBuffer(downloadResponse.readableStreamBody),
                properties: downloadResponse
            };

        } catch (error) {
            console.error('File download failed:', error);
            throw new Error(`Download failed: ${error.message}`);
        }
    }

    async deleteFile(filePath) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const blobClient = this.containerClient.getBlockBlobClient(filePath);
            await blobClient.delete();
            return true;

        } catch (error) {
            console.error('File deletion failed:', error);
            throw new Error(`Delete failed: ${error.message}`);
        }
    }

    async getFileMetadata(filePath) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const blobClient = this.containerClient.getBlockBlobClient(filePath);
            const properties = await blobClient.getProperties();

            return {
                fileName: properties.metadata?.originalName || 'Unknown',
                fileSize: properties.contentLength,
                contentType: properties.contentType,
                uploadedAt: properties.metadata?.uploadedAt,
                incidentId: properties.metadata?.incidentId,
                lastModified: properties.lastModified,
                etag: properties.etag
            };

        } catch (error) {
            console.error('Failed to get file metadata:', error);
            throw new Error(`Metadata retrieval failed: ${error.message}`);
        }
    }

    async generateSecureFileUrl(filePath, expiryMinutes = 60) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const blobClient = this.containerClient.getBlockBlobClient(filePath);

            // For now, return the basic URL since SAS token generation requires additional setup
            // In production, you would generate a SAS token here
            const config = azureConfig.getConfig();
            return `${config.blobEndpoint}${config.containerName}/${filePath}`;

        } catch (error) {
            console.error('Failed to generate secure file URL:', error);
            throw new Error(`URL generation failed: ${error.message}`);
        }
    }

    // Helper methods
    generateFileId() {
        return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async streamToBuffer(readableStream) {
        return new Promise((resolve, reject) => {
            const chunks = [];
            readableStream.on('data', (data) => {
                chunks.push(data instanceof Buffer ? data : Buffer.from(data));
            });
            readableStream.on('end', () => {
                resolve(Buffer.concat(chunks));
            });
            readableStream.on('error', reject);
        });
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    isImageFile(contentType) {
        return contentType && contentType.startsWith('image/');
    }

    isPdfFile(contentType) {
        return contentType === 'application/pdf';
    }

    isDocumentFile(contentType) {
        const documentTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
            'text/csv'
        ];
        return documentTypes.includes(contentType);
    }
}

// Singleton instance
export const azureStorage = new AzureStorageService();
