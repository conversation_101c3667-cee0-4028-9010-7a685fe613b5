-- Fix Admin Role Assignment
-- This <NAME_EMAIL> has the admin role and permissions

-- Step 1: Check current state
SELECT 'Current user state:' as info;
SELECT 
    u.email,
    r.name as role_name
FROM auth.users u
LEFT JOIN core.user_roles ur ON u.id = ur.user_id
LEFT JOIN core.roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';

-- Step 2: Ensure admin role exists
INSERT INTO core.roles (name, display_name, description, is_system_role)
VALUES ('iharc_admin', 'IHARC Administrator', 'Full system administrator with all permissions', true)
ON CONFLICT (name) DO NOTHING;

-- Step 3: Ensure admin permissions exist
INSERT INTO core.permissions (name, description, category)
VALUES 
    ('admin.access', 'Access to admin interface', 'admin'),
    ('users.read', 'Read user information', 'users'),
    ('users.create', 'Create new users', 'users'),
    ('users.update', 'Update user information', 'users'),
    ('users.delete', 'Delete users', 'users')
ON CONFLICT (name) DO NOTHING;

-- Step 4: Assign all permissions to admin role
INSERT INTO core.role_permissions (role_id, permission_id)
SELECT 
    r.id as role_id,
    p.id as permission_id
FROM core.roles r
CROSS JOIN core.permissions p
WHERE r.name = 'iharc_admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Step 5: Remove any existing <NAME_EMAIL> and assign admin role
DO $$
DECLARE
    admin_user_id UUID;
    admin_role_id UUID;
BEGIN
    -- Find the user <NAME_EMAIL>
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    -- Find the admin role
    SELECT id INTO admin_role_id 
    FROM core.roles 
    WHERE name = 'iharc_admin';
    
    IF admin_user_id IS NOT NULL AND admin_role_id IS NOT NULL THEN
        -- Remove any existing roles for this user
        DELETE FROM core.user_roles WHERE user_id = admin_user_id;
        
        -- Assign admin <NAME_EMAIL>
        INSERT INTO core.user_roles (user_id, role_id, assigned_by)
        VALUES (admin_user_id, admin_role_id, admin_user_id);
        
        RAISE NOTICE 'Admin role <NAME_EMAIL> (User ID: %, Role ID: %)', admin_user_id, admin_role_id;
    ELSE
        RAISE NOTICE 'User <EMAIL> or admin role not found. User ID: %, Role ID: %', admin_user_id, admin_role_id;
    END IF;
END $$;

-- Step 6: Verify the assignment
SELECT 'Verification - User roles after assignment:' as info;
SELECT 
    u.email,
    r.name as role_name
FROM auth.users u
LEFT JOIN core.user_roles ur ON u.id = ur.user_id
LEFT JOIN core.roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';

-- Step 7: Test the has_permission function
SELECT 'Testing has_permission function:' as info;
SELECT 
    'admin.access' as permission,
    core.has_permission('admin.access') as has_permission;

-- Step 8: Refresh user permissions in JWT
SELECT 'Refreshing user permissions...' as info;
SELECT core.refresh_user_permissions(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>')
);

-- Step 9: Test get_users_with_roles function
SELECT 'Testing get_users_with_roles function:' as info;
SELECT * FROM core.get_users_with_roles(); 