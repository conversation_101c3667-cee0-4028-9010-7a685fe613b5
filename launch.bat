@echo off
title S.T.E.V.I Retro Launcher
echo.
echo ===============================================
echo  S.T.E.V.I Retro - Desktop Application Launcher
echo ===============================================
echo.
echo Starting S.T.E.V.I Retro...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install --ignore-scripts
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
    echo Installing Electron...
    npm install electron --save-dev
    if errorlevel 1 (
        echo ERROR: Failed to install Electron
        pause
        exit /b 1
    )
)

REM Launch the application
echo Launching S.T.E.V.I Retro...
npm start

REM If we get here, the app has closed
echo.
echo S.T.E.V.I Retro has closed.
pause
