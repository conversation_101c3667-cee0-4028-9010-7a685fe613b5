-- Migration: Move Permission System from public to core schema
-- This script migrates the existing roles, permissions, and role_permissions tables
-- from the public schema to the core schema following Supabase best practices

-- Step 1: Create the core schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS core;

-- Step 2: Create tables in core schema with the same structure
CREATE TABLE core.roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE core.permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    category TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE core.role_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    role_id UUID REFERENCES core.roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES core.permissions(id) ON DELETE CASCADE,
    granted_by UUID REFERENCES auth.users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(role_id, permission_id)
);

-- Step 3: Migrate data from public schema to core schema
INSERT INTO core.roles (id, name, display_name, description, is_system_role, created_at, updated_at)
SELECT id, name, display_name, description, is_system_role, created_at, updated_at
FROM public.roles
ON CONFLICT (id) DO NOTHING;

INSERT INTO core.permissions (id, name, description, category, created_at, updated_at)
SELECT id, name, description, category, created_at, updated_at
FROM public.permissions
ON CONFLICT (id) DO NOTHING;

INSERT INTO core.role_permissions (id, role_id, permission_id, granted_by, granted_at)
SELECT id, role_id, permission_id, granted_by, granted_at
FROM public.role_permissions
ON CONFLICT (id) DO NOTHING;

-- Step 4: Create user_roles table in core schema for direct user-role assignments
CREATE TABLE core.user_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES core.roles(id) ON DELETE CASCADE,
    granted_by UUID REFERENCES auth.users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, role_id)
);

-- Step 5: Drop existing functions first (if they exist)
DROP FUNCTION IF EXISTS public.get_user_roles(UUID);
DROP FUNCTION IF EXISTS public.get_user_permissions(UUID);
DROP FUNCTION IF EXISTS public.has_permission(TEXT);
DROP FUNCTION IF EXISTS public.refresh_user_claims(UUID);
DROP FUNCTION IF EXISTS public.assign_user_role(UUID, TEXT, UUID);
DROP FUNCTION IF EXISTS public.remove_user_role(UUID, TEXT);
DROP FUNCTION IF EXISTS public.get_users_with_roles();

-- Step 6: Create updated functions in public schema that reference core tables

-- Function to get user roles
CREATE OR REPLACE FUNCTION public.get_user_roles(user_uuid UUID)
RETURNS TABLE(role_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RETURN QUERY
        SELECT r.name
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = user_uuid;
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Function to get user permissions
CREATE OR REPLACE FUNCTION public.get_user_permissions(user_uuid UUID)
RETURNS TABLE(permission_name TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user is the same as the target user or is an admin
    IF auth.uid() = user_uuid OR EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RETURN QUERY
        SELECT DISTINCT p.name
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        JOIN core.role_permissions rp ON r.id = rp.role_id
        JOIN core.permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = user_uuid;
    ELSE
        -- Return empty result for unauthorized access
        RETURN;
    END IF;
END;
$$;

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION public.has_permission(permission_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        JOIN core.role_permissions rp ON r.id = rp.role_id
        JOIN core.permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = auth.uid() AND p.name = permission_name
    );
END;
$$;

-- Function to refresh user claims (updates JWT with current roles and permissions)
CREATE OR REPLACE FUNCTION public.refresh_user_claims(user_uuid UUID DEFAULT auth.uid())
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_roles TEXT[];
    user_permissions TEXT[];
    result JSON;
BEGIN
    -- Get user roles
    SELECT ARRAY_AGG(role_name) INTO user_roles
    FROM public.get_user_roles(user_uuid);
    
    -- Get user permissions
    SELECT ARRAY_AGG(permission_name) INTO user_permissions
    FROM public.get_user_permissions(user_uuid);
    
    -- Build result
    result := json_build_object(
        'roles', COALESCE(user_roles, ARRAY[]::TEXT[]),
        'permissions', COALESCE(user_permissions, ARRAY[]::TEXT[])
    );
    
    -- Update user's app_metadata with claims
    UPDATE auth.users 
    SET raw_app_meta_data = jsonb_set(
        COALESCE(raw_app_meta_data, '{}'::jsonb),
        '{claims}',
        result::jsonb
    )
    WHERE id = user_uuid;
    
    RETURN result;
END;
$$;

-- Function to assign role to user (admin only)
CREATE OR REPLACE FUNCTION public.assign_user_role(
    target_user_id UUID,
    role_name TEXT,
    granted_by UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    role_uuid UUID;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Get role UUID
    SELECT id INTO role_uuid FROM core.roles WHERE name = role_name;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Role not found: %', role_name;
    END IF;
    
    -- Insert or update the role assignment
    INSERT INTO core.user_roles (user_id, role_id, granted_by)
    VALUES (target_user_id, role_uuid, granted_by)
    ON CONFLICT (user_id, role_id) 
    DO UPDATE SET 
        granted_by = EXCLUDED.granted_by,
        granted_at = NOW();
    
    -- Refresh the user's claims
    PERFORM public.refresh_user_claims(target_user_id);
    
    RETURN TRUE;
END;
$$;

-- Function to remove role from user (admin only)
CREATE OR REPLACE FUNCTION public.remove_user_role(
    target_user_id UUID,
    role_name TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    role_uuid UUID;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    -- Get role UUID
    SELECT id INTO role_uuid FROM core.roles WHERE name = role_name;
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Role not found: %', role_name;
    END IF;
    
    -- Remove the role assignment
    DELETE FROM core.user_roles 
    WHERE user_id = target_user_id AND role_id = role_uuid;
    
    -- Refresh the user's claims
    PERFORM public.refresh_user_claims(target_user_id);
    
    RETURN TRUE;
END;
$$;

-- Function to get all users with their roles (for admin interface)
CREATE OR REPLACE FUNCTION public.get_users_with_roles()
RETURNS TABLE(
    user_id UUID,
    email TEXT,
    full_name TEXT,
    roles TEXT[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM core.user_roles ur
        JOIN core.roles r ON ur.role_id = r.id
        WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
    ) THEN
        RAISE EXCEPTION 'Permission denied: admin access required';
    END IF;
    
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.email,
        COALESCE(u.raw_user_meta_data->>'full_name', u.raw_user_meta_data->>'name') as full_name,
        ARRAY_AGG(r.name) as roles
    FROM auth.users u
    LEFT JOIN core.user_roles ur ON u.id = ur.user_id
    LEFT JOIN core.roles r ON ur.role_id = r.id
    WHERE u.email LIKE '%@iharc.ca'
    GROUP BY u.id, u.email, u.raw_user_meta_data
    ORDER BY u.email;
END;
$$;

-- Step 7: Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_user_roles(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_permissions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.has_permission(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_user_claims(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.assign_user_role(UUID, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.remove_user_role(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_users_with_roles() TO authenticated;

-- Step 8: Grant table permissions
GRANT SELECT ON core.roles TO authenticated;
GRANT SELECT ON core.permissions TO authenticated;
GRANT SELECT ON core.role_permissions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON core.user_roles TO authenticated;

-- Step 9: Enable RLS on core tables
ALTER TABLE core.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.user_roles ENABLE ROW LEVEL SECURITY;

-- Step 10: Create RLS policies for core tables

-- RLS policies for core.roles
CREATE POLICY "All authenticated users can view roles" ON core.roles
    FOR SELECT USING (auth.role() = 'authenticated');

-- RLS policies for core.permissions
CREATE POLICY "All authenticated users can view permissions" ON core.permissions
    FOR SELECT USING (auth.role() = 'authenticated');

-- RLS policies for core.role_permissions
CREATE POLICY "All authenticated users can view role permissions" ON core.role_permissions
    FOR SELECT USING (auth.role() = 'authenticated');

-- RLS policies for core.user_roles
CREATE POLICY "Users can view their own roles" ON core.user_roles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all user roles" ON core.user_roles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM core.user_roles ur
            JOIN core.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
        )
    );

CREATE POLICY "Admins can manage user roles" ON core.user_roles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM core.user_roles ur
            JOIN core.roles r ON ur.role_id = r.id
            WHERE ur.user_id = auth.uid() AND r.name = 'iharc_admin'
        )
    );

-- Step 11: Create admin <NAME_EMAIL>
-- This will assign the iharc_admin role to the user <NAME_EMAIL>
DO $$
DECLARE
    admin_user_id UUID;
    admin_role_id UUID;
BEGIN
    -- Find the user <NAME_EMAIL>
    SELECT id INTO admin_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    -- Find the iharc_admin role
    SELECT id INTO admin_role_id 
    FROM core.roles 
    WHERE name = 'iharc_admin';
    
    -- Assign admin role if both user and role exist
    IF admin_user_id IS NOT NULL AND admin_role_id IS NOT NULL THEN
        INSERT INTO core.user_roles (user_id, role_id, granted_by)
        VALUES (admin_user_id, admin_role_id, admin_user_id)
        ON CONFLICT (user_id, role_id) DO NOTHING;
        
        -- Refresh the user's claims
        PERFORM public.refresh_user_claims(admin_user_id);
        
        RAISE NOTICE 'Admin role <NAME_EMAIL>';
    ELSE
        RAISE NOTICE 'User <EMAIL> or iharc_admin role not found';
    END IF;
END $$;

-- Step 12: Clean up old tables (optional - uncomment when ready)
-- DROP TABLE IF EXISTS public.role_permissions;
-- DROP TABLE IF EXISTS public.permissions;
-- DROP TABLE IF EXISTS public.roles;

-- Step 13: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_core_user_roles_user_id ON core.user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_core_user_roles_role_id ON core.user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_core_role_permissions_role_id ON core.role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_core_role_permissions_permission_id ON core.role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_core_roles_name ON core.roles(name);
CREATE INDEX IF NOT EXISTS idx_core_permissions_name ON core.permissions(name); 