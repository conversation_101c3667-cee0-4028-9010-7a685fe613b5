# People Records Not Updating - Root Cause and Fix

## Issue Analysis

Based on the console output:
```
data.js:826 🔄 Refreshing people cache with 2 records
app.js:4781 🔄 Real-time UI update: refresh in people undefined
```

### Root Cause
The issue has two parts:

1. **Undefined Table Parameter**: The `emitDataChange` method is being called with an undefined table parameter, causing the real-time UI update to fail.

2. **Timing Issue**: The People Management UI loads before the cache is populated:
   - User navigates to People Management
   - `getAll('people')` returns empty SQLite cache immediately
   - UI renders with empty data
   - Background refresh fetches 2 records from Supabase
   - Background refresh tries to emit dataChange event but table is undefined
   - UI never gets updated with the fetched data

### Technical Details
The sequence is:
1. `loadPeopleManagementContent()` → `search('people', {})` → `getAll('people')`
2. `getAll()` returns empty SQLite cache immediately for responsive UI
3. `getAll()` triggers background `refreshTableCache()` 
4. Background refresh fetches 2 records successfully
5. `refreshTableCache()` calls `emitDataChange(table, 'refresh', data)`
6. But `table` parameter becomes undefined somewhere in the async chain
7. `dataChange` event fires with undefined table name
8. `refreshPeopleList()` is never called because table !== 'people'

## Fix Applied

### 1. Robust Parameter Validation
Enhanced both `refreshTableCache()` and `emitDataChange()` methods with robust validation:

```javascript
// In refreshTableCache()
if (!table || typeof table !== 'string' || table.trim() === '') {
    console.error('❌ refreshTableCache called with invalid table parameter!');
    return;
}
const cleanTable = table.toString().trim();

// In emitDataChange()  
if (!table || typeof table !== 'string' || table.trim() === '') {
    console.error('❌ emitDataChange called with invalid table parameter!');
    return;
}
const cleanTable = table.toString().trim();
```

### 2. Parameter Sanitization
Ensured table parameters are properly sanitized throughout the async chain:
- Convert to string and trim whitespace
- Use `cleanTable` consistently throughout methods
- Prevent undefined/null values from propagating

### 3. Enhanced Error Reporting
Added detailed logging to identify exactly where undefined parameters originate:
- Stack traces for debugging
- Parameter type checking
- Consistent logging format

## Files Modified
- `renderer/js/data.js` - Enhanced parameter validation in `refreshTableCache()` and `emitDataChange()`

## Expected Results
After this fix:

1. **No more undefined table errors**: The validation will catch and prevent undefined table parameters
2. **Proper real-time updates**: The `dataChange` event will fire with correct table name
3. **UI refresh works**: `refreshPeopleList()` will be called when people data changes
4. **Better debugging**: Clear error messages if issues persist

## Testing
Deploy the updated `renderer/js/data.js` and:

1. Navigate to Records → Manage People
2. Console should show:
   ```
   🔄 Starting refreshTableCache for: "people"
   🔄 Refreshing people cache with 2 records
   📡 Emitting dataChange event: refresh in people
   🔄 refreshPeopleList called. Current screen: people-management
   ```
3. People records should appear in the UI
4. No more "undefined" table names in console

## Root Cause Summary
The issue was caused by the `table` parameter becoming undefined in the async execution chain of `refreshTableCache()`, causing the `dataChange` event to fire with an undefined table name, which prevented the UI refresh from being triggered. The fix adds robust parameter validation and sanitization to prevent this corruption.
