-- Add Criminal Justice Tables for People Records
-- This migration creates tables to track criminal justice information

-- Create incarceration_status table for current and past incarceration
CREATE TABLE IF NOT EXISTS core.incarceration_status (
    id BIGSERIAL PRIMARY KEY,
    person_id BIGINT NOT NULL REFERENCES core.people(id) ON DELETE CASCADE,
    facility_name TEXT,
    facility_type TEXT CHECK (facility_type IN ('Jail', 'Prison', 'Detention Center', 'Remand', 'Other')),
    status TEXT CHECK (status IN ('Currently Incarcerated', 'Released', 'Transferred', 'Unknown')) NOT NULL,
    admission_date DATE,
    release_date DATE,
    expected_release_date DATE,
    charges TEXT,
    sentence_length TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT
);

-- Create bail_conditions table for bail and release conditions
CREATE TABLE IF NOT EXISTS core.bail_conditions (
    id BIGSERIAL PRIMARY KEY,
    person_id BIGINT NOT NULL REFERENCES core.people(id) ON DELETE CASCADE,
    condition_type TEXT CHECK (condition_type IN ('Bail', 'Recognizance', 'Probation', 'Parole', 'Other')) NOT NULL,
    condition_description TEXT NOT NULL,
    start_date DATE,
    end_date DATE,
    amount DECIMAL(10,2),
    surety_name TEXT,
    surety_contact TEXT,
    status TEXT CHECK (status IN ('Active', 'Completed', 'Breached', 'Cancelled')) DEFAULT 'Active',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT
);

-- Create court_dates table for past and future court appearances
CREATE TABLE IF NOT EXISTS core.court_dates (
    id BIGSERIAL PRIMARY KEY,
    person_id BIGINT NOT NULL REFERENCES core.people(id) ON DELETE CASCADE,
    court_date DATE NOT NULL,
    court_time TIME,
    court_name TEXT,
    court_address TEXT,
    case_number TEXT,
    charges TEXT,
    court_type TEXT CHECK (court_type IN ('Criminal', 'Family', 'Civil', 'Traffic', 'Other')),
    appearance_type TEXT CHECK (appearance_type IN ('First Appearance', 'Arraignment', 'Preliminary Hearing', 'Trial', 'Sentencing', 'Review', 'Other')),
    outcome TEXT,
    next_court_date DATE,
    lawyer_name TEXT,
    lawyer_contact TEXT,
    status TEXT CHECK (status IN ('Scheduled', 'Completed', 'Missed', 'Cancelled', 'Rescheduled')) DEFAULT 'Scheduled',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT
);

-- Create arrest_history table for arrest records
CREATE TABLE IF NOT EXISTS core.arrest_history (
    id BIGSERIAL PRIMARY KEY,
    person_id BIGINT NOT NULL REFERENCES core.people(id) ON DELETE CASCADE,
    arrest_date DATE NOT NULL,
    arrest_time TIME,
    arresting_agency TEXT,
    location TEXT,
    charges TEXT NOT NULL,
    disposition TEXT,
    case_number TEXT,
    booking_number TEXT,
    bail_amount DECIMAL(10,2),
    release_date DATE,
    release_type TEXT CHECK (release_type IN ('Bail', 'Own Recognizance', 'Charges Dropped', 'Served Time', 'Other')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT
);

-- Create legal_contacts table for lawyers, probation officers, etc.
CREATE TABLE IF NOT EXISTS core.legal_contacts (
    id BIGSERIAL PRIMARY KEY,
    person_id BIGINT NOT NULL REFERENCES core.people(id) ON DELETE CASCADE,
    contact_name TEXT NOT NULL,
    contact_type TEXT CHECK (contact_type IN ('Lawyer', 'Public Defender', 'Probation Officer', 'Parole Officer', 'Legal Aid', 'Other')) NOT NULL,
    organization TEXT,
    phone TEXT,
    email TEXT,
    address TEXT,
    case_numbers TEXT,
    relationship_start_date DATE,
    relationship_end_date DATE,
    status TEXT CHECK (status IN ('Active', 'Inactive', 'Terminated')) DEFAULT 'Active',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by TEXT,
    updated_by TEXT
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_incarceration_status_person_id ON core.incarceration_status(person_id);
CREATE INDEX IF NOT EXISTS idx_bail_conditions_person_id ON core.bail_conditions(person_id);
CREATE INDEX IF NOT EXISTS idx_court_dates_person_id ON core.court_dates(person_id);
CREATE INDEX IF NOT EXISTS idx_court_dates_court_date ON core.court_dates(court_date);
CREATE INDEX IF NOT EXISTS idx_arrest_history_person_id ON core.arrest_history(person_id);
CREATE INDEX IF NOT EXISTS idx_arrest_history_arrest_date ON core.arrest_history(arrest_date);
CREATE INDEX IF NOT EXISTS idx_legal_contacts_person_id ON core.legal_contacts(person_id);

-- Add comments for documentation
COMMENT ON TABLE core.incarceration_status IS 'Current and past incarceration information';
COMMENT ON TABLE core.bail_conditions IS 'Bail, probation, parole and other release conditions';
COMMENT ON TABLE core.court_dates IS 'Past and future court appearances';
COMMENT ON TABLE core.arrest_history IS 'Historical arrest records';
COMMENT ON TABLE core.legal_contacts IS 'Legal representation and supervision contacts';

-- Enable RLS for all new tables
ALTER TABLE core.incarceration_status ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.bail_conditions ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.court_dates ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.arrest_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE core.legal_contacts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for incarceration_status table
CREATE POLICY "Enable read access for iharc_staff" ON core.incarceration_status
    FOR SELECT TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable insert for iharc_staff" ON core.incarceration_status
    FOR INSERT TO authenticated
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable update for iharc_staff" ON core.incarceration_status
    FOR UPDATE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable delete for iharc_staff" ON core.incarceration_status
    FOR DELETE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

-- Create RLS policies for bail_conditions table
CREATE POLICY "Enable read access for iharc_staff" ON core.bail_conditions
    FOR SELECT TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable insert for iharc_staff" ON core.bail_conditions
    FOR INSERT TO authenticated
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable update for iharc_staff" ON core.bail_conditions
    FOR UPDATE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable delete for iharc_staff" ON core.bail_conditions
    FOR DELETE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

-- Create RLS policies for court_dates table
CREATE POLICY "Enable read access for iharc_staff" ON core.court_dates
    FOR SELECT TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable insert for iharc_staff" ON core.court_dates
    FOR INSERT TO authenticated
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable update for iharc_staff" ON core.court_dates
    FOR UPDATE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable delete for iharc_staff" ON core.court_dates
    FOR DELETE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

-- Create RLS policies for arrest_history table
CREATE POLICY "Enable read access for iharc_staff" ON core.arrest_history
    FOR SELECT TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable insert for iharc_staff" ON core.arrest_history
    FOR INSERT TO authenticated
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable update for iharc_staff" ON core.arrest_history
    FOR UPDATE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable delete for iharc_staff" ON core.arrest_history
    FOR DELETE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

-- Create RLS policies for legal_contacts table
CREATE POLICY "Enable read access for iharc_staff" ON core.legal_contacts
    FOR SELECT TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable insert for iharc_staff" ON core.legal_contacts
    FOR INSERT TO authenticated
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable update for iharc_staff" ON core.legal_contacts
    FOR UPDATE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'))
    WITH CHECK ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));

CREATE POLICY "Enable delete for iharc_staff" ON core.legal_contacts
    FOR DELETE TO authenticated
    USING ((auth.jwt() ->> 'role') IN ('iharc_staff', 'iharc_admin'));
