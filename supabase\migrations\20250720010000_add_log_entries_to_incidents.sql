-- Migration: Add log_entries column to incidents table
-- This column will store JSONB array of log entries for incident activity tracking

-- Add log_entries column to incidents table using JSONB type
ALTER TABLE case_mgmt.incidents 
ADD COLUMN IF NOT EXISTS log_entries JSONB DEFAULT '[]'::jsonb;

-- Add comment to document the column purpose
COMMENT ON COLUMN case_mgmt.incidents.log_entries IS 'JSONB array of log entries for incident activity tracking. Each entry contains timestamp, user, entry_type, and content.';

-- Create GIN index on log_entries for better query performance
CREATE INDEX IF NOT EXISTS idx_incidents_log_entries ON case_mgmt.incidents USING GIN (log_entries);

-- Add trigger to update updated_at when log_entries changes
CREATE OR REPLACE FUNCTION update_incident_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_update_incident_updated_at ON case_mgmt.incidents;

-- <PERSON>reate trigger to automatically update updated_at
CREATE TRIGGER trigger_update_incident_updated_at
    BEFORE UPDATE ON case_mgmt.incidents
    FOR EACH ROW
    EXECUTE FUNCTION update_incident_updated_at(); 