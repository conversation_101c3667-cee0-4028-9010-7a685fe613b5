{"name": "steviretro", "version": "1.3.0", "description": "S.T.E.V.I Retro - Supportive Technology to Enable Vulnerable Individuals - Retro Interface for I.H.A.R.C field staff", "main": "electron/main.js", "type": "module", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "npm run check-deps && electron-builder", "build-win": "npm run check-deps && electron-builder --win", "pack": "electron-builder --dir", "dist": "electron-builder", "postinstall": "electron-builder install-app-deps", "check-deps": "node build/check-dependencies.cjs", "installer": "npm run build-win", "standalone": "npm run clean && npm run check-deps && electron-builder --win", "clean": "rimraf dist S.T.E.V.I-Retro-Installer.exe", "rebuild": "npm run clean && npm run build"}, "keywords": ["cli", "offline", "iharc", "field-staff", "incident-reporting"], "author": "IHARC", "license": "MIT", "dependencies": {"@azure/identity": "^4.10.2", "@azure/storage-blob": "^12.27.0", "@supabase/supabase-js": "^2.50.5", "better-sqlite3": "^12.2.0", "node-fetch": "^3.3.2", "semver": "^7.7.2", "uuid": "^11.1.0"}, "devDependencies": {"electron": "^37.2.1", "electron-builder": "^26.0.12", "electron-reload": "^2.0.0-alpha.1"}, "build": {"appId": "ca.iharc.steviretro", "productName": "S.T.E.V.I Retro", "copyright": "Copyright © 2024 I.H.A.R.C. All rights reserved.", "directories": {"output": "dist", "buildResources": "build"}, "files": ["electron/**/*", "src/**/*", "renderer/**/*", "node_modules/**/*", "templates/**/*", "assets/**/*"], "extraResources": [{"from": "templates", "to": "templates", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "legalTrademarks": "I.H.A.R.C. - Supportive Technology to Enable Vulnerable Individuals"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "S.T.E.V.I Retro", "runAfterFinish": true, "menuCategory": "I.H.A.R.C", "artifactName": "S.T.E.V.I-Retro-Installer.${ext}", "deleteAppDataOnUninstall": false, "perMachine": false, "packElevateHelper": true, "unicode": true, "warningsAsErrors": false}, "publish": null}}