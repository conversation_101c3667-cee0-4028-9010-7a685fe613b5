// Database Migration Manager for S.T.E.V.I Retro
export class MigrationManager {
    constructor(dataManager, supabaseClient) {
        this.data = dataManager;
        this.supabase = supabaseClient;
        this.currentVersion = null;
        this.targetVersion = null;
        this.migrations = new Map();
        this.initializeMigrations();
    }

    initializeMigrations() {
        // Define all migrations in chronological order
        this.migrations.set('1.0.0', {
            version: '1.0.0',
            description: 'Initial schema setup',
            up: async () => {
                // Base tables should already exist
                console.log('Base schema already exists');
                return true;
            },
            down: async () => {
                console.log('Cannot rollback initial schema');
                return false;
            }
        });

        this.migrations.set('1.1.0', {
            version: '1.1.0',
            description: 'Add encampments functionality',
            up: async () => {
                return await this.migrateToEncampments();
            },
            down: async () => {
                return await this.rollbackEncampments();
            },
            requiredTables: ['encampments'],
            newFeatures: ['encampments']
        });

        // Future migrations can be added here
        // this.migrations.set('1.2.0', { ... });
    }

    async getCurrentSchemaVersion() {
        try {
            // TEMPORARY FIX: Return 1.1.0 directly to enable encampments feature
            // TODO: Fix the underlying JWT/authentication issue
            console.log('🔧 Using hardcoded schema version 1.1.0 (temporary fix)');
            return '1.1.0';

            // Original code (commented out temporarily):
            /*
            // Check if version tracking table exists
            const { data, error } = await this.supabase
                .from('core.schema_versions')
                .select('version, applied_at')
                .order('applied_at', { ascending: false })
                .limit(1);

            if (error && error.code === '42P01') {
                // Table doesn't exist, this is a fresh install or pre-migration system
                console.log('Schema version table not found, assuming base version');
                return '1.0.0';
            }

            if (error) {
                console.error('Error checking schema version:', error);
                return '1.0.0';
            }

            return data && data.length > 0 ? data[0].version : '1.0.0';
            */
        } catch (error) {
            console.error('Error getting current schema version:', error);
            return '1.1.0'; // Return 1.1.0 even on error (temporary fix)
        }
    }

    async getAppVersion() {
        // Get version from package.json or app metadata
        try {
            if (window.electronAPI) {
                return await window.electronAPI.invoke('get-app-version');
            }
            // Fallback for development
            return '1.1.0'; // Current version with encampments
        } catch (error) {
            console.error('Error getting app version:', error);
            return '1.1.0';
        }
    }

    async checkMigrationsNeeded() {
        this.currentVersion = await this.getCurrentSchemaVersion();
        this.targetVersion = await this.getAppVersion();

        console.log(`Schema version: ${this.currentVersion}, App version: ${this.targetVersion}`);

        const currentIndex = this.getVersionIndex(this.currentVersion);
        const targetIndex = this.getVersionIndex(this.targetVersion);

        return targetIndex > currentIndex;
    }

    getVersionIndex(version) {
        const versions = Array.from(this.migrations.keys());
        const index = versions.indexOf(version);
        return index === -1 ? 0 : index;
    }

    async runMigrations() {
        try {
            console.log('🔄 Starting database migrations...');

            const migrationsNeeded = await this.checkMigrationsNeeded();
            if (!migrationsNeeded) {
                console.log('✅ No migrations needed');
                return { success: true, migrationsRun: [] };
            }

            // Create version tracking table if it doesn't exist
            await this.ensureVersionTable();

            const currentIndex = this.getVersionIndex(this.currentVersion);
            const targetIndex = this.getVersionIndex(this.targetVersion);
            const versions = Array.from(this.migrations.keys());
            const migrationsToRun = versions.slice(currentIndex + 1, targetIndex + 1);

            console.log(`Running migrations: ${migrationsToRun.join(' → ')}`);

            const results = [];
            for (const version of migrationsToRun) {
                const migration = this.migrations.get(version);
                console.log(`🔄 Running migration ${version}: ${migration.description}`);

                try {
                    const success = await migration.up();
                    if (success) {
                        await this.recordMigration(version, migration.description);
                        results.push({ version, success: true });
                        console.log(`✅ Migration ${version} completed`);
                    } else {
                        throw new Error(`Migration ${version} returned false`);
                    }
                } catch (error) {
                    console.error(`❌ Migration ${version} failed:`, error);
                    results.push({ version, success: false, error: error.message });
                    
                    // Attempt rollback of this migration
                    try {
                        await migration.down();
                        console.log(`🔄 Rolled back migration ${version}`);
                    } catch (rollbackError) {
                        console.error(`❌ Rollback failed for ${version}:`, rollbackError);
                    }
                    
                    throw new Error(`Migration ${version} failed: ${error.message}`);
                }
            }

            console.log('✅ All migrations completed successfully');
            return { success: true, migrationsRun: results };

        } catch (error) {
            console.error('❌ Migration process failed:', error);
            return { success: false, error: error.message };
        }
    }

    async ensureVersionTable() {
        try {
            const { error } = await this.supabase.rpc('create_schema_version_table');
            
            if (error && !error.message.includes('already exists')) {
                // Fallback: try to create directly
                const createTableSQL = `
                    CREATE TABLE IF NOT EXISTS core.schema_versions (
                        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                        version TEXT NOT NULL,
                        description TEXT,
                        applied_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                        applied_by TEXT
                    );
                `;
                
                const { error: directError } = await this.supabase.rpc('exec_sql', { 
                    sql: createTableSQL 
                });
                
                if (directError) {
                    console.warn('Could not create schema_versions table:', directError);
                }
            }
        } catch (error) {
            console.warn('Error ensuring version table:', error);
        }
    }

    async recordMigration(version, description) {
        try {
            const { error } = await this.supabase
                .from('core.schema_versions')
                .insert({
                    version,
                    description,
                    applied_by: 'migration-system'
                });

            if (error) {
                console.warn('Could not record migration:', error);
            }
        } catch (error) {
            console.warn('Error recording migration:', error);
        }
    }

    async migrateToEncampments() {
        try {
            console.log('🏕️ Creating encampments table...');

            // Check if table already exists
            const { data: existingTable } = await this.supabase
                .from('information_schema.tables')
                .select('table_name')
                .eq('table_schema', 'core')
                .eq('table_name', 'encampments');

            if (existingTable && existingTable.length > 0) {
                console.log('✅ Encampments table already exists');
                return true;
            }

            // Create the encampments table
            const createTableSQL = `
                CREATE TABLE core.encampments (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    name TEXT NOT NULL,
                    location TEXT NOT NULL,
                    coordinates TEXT,
                    status TEXT NOT NULL DEFAULT 'active',
                    type TEXT,
                    estimated_population INTEGER,
                    description TEXT,
                    safety_concerns TEXT,
                    services_needed TEXT,
                    last_visited TIMESTAMPTZ,
                    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMPTZ,
                    created_by TEXT,
                    updated_by TEXT
                );
            `;

            const { error: createError } = await this.supabase.rpc('exec_sql', { 
                sql: createTableSQL 
            });

            if (createError) {
                throw createError;
            }

            // Enable RLS
            const rlsSQL = `
                ALTER TABLE core.encampments ENABLE ROW LEVEL SECURITY;

                CREATE POLICY "Staff can view encampments" ON core.encampments
                    FOR SELECT USING (
                        auth.jwt() ->> 'role' = 'iharc_staff' OR
                        auth.jwt() ->> 'role' = 'iharc_admin'
                    );

                CREATE POLICY "Staff can insert encampments" ON core.encampments
                    FOR INSERT WITH CHECK (
                        auth.jwt() ->> 'role' = 'iharc_staff' OR
                        auth.jwt() ->> 'role' = 'iharc_admin'
                    );

                CREATE POLICY "Staff can update encampments" ON core.encampments
                    FOR UPDATE USING (
                        auth.jwt() ->> 'role' = 'iharc_staff' OR
                        auth.jwt() ->> 'role' = 'iharc_admin'
                    );

                CREATE POLICY "Staff can delete encampments" ON core.encampments
                    FOR DELETE USING (
                        auth.jwt() ->> 'role' = 'iharc_staff' OR
                        auth.jwt() ->> 'role' = 'iharc_admin'
                    );
            `;

            const { error: rlsError } = await this.supabase.rpc('exec_sql', { 
                sql: rlsSQL 
            });

            if (rlsError) {
                console.warn('RLS setup warning:', rlsError);
                // Don't fail the migration for RLS issues
            }

            console.log('✅ Encampments table created successfully');
            return true;

        } catch (error) {
            console.error('❌ Failed to create encampments table:', error);
            return false;
        }
    }

    async rollbackEncampments() {
        try {
            console.log('🔄 Rolling back encampments table...');

            const { error } = await this.supabase.rpc('exec_sql', { 
                sql: 'DROP TABLE IF EXISTS core.encampments CASCADE;' 
            });

            if (error) {
                console.error('Error dropping encampments table:', error);
                return false;
            }

            console.log('✅ Encampments table rolled back');
            return true;

        } catch (error) {
            console.error('❌ Failed to rollback encampments:', error);
            return false;
        }
    }

    async validateSchema() {
        try {
            const version = await this.getCurrentSchemaVersion();
            const migration = this.migrations.get(version);
            
            if (!migration || !migration.requiredTables) {
                return { valid: true, missingTables: [] };
            }

            const missingTables = [];
            for (const tableName of migration.requiredTables) {
                const { data } = await this.supabase
                    .from('information_schema.tables')
                    .select('table_name')
                    .eq('table_schema', 'core')
                    .eq('table_name', tableName);

                if (!data || data.length === 0) {
                    missingTables.push(tableName);
                }
            }

            return {
                valid: missingTables.length === 0,
                missingTables,
                version
            };

        } catch (error) {
            console.error('Error validating schema:', error);
            return { valid: false, error: error.message };
        }
    }
}
