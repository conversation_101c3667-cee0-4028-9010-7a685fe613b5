{"name": "stevi-retro-update-proxy", "version": "1.0.0", "description": "Secure proxy service for S.T.E.V.I Retro updates", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js"}, "keywords": ["stevi-retro", "update-proxy", "github-releases"], "author": "IHARC", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "rate-limiter-flexible": "^3.0.8"}, "engines": {"node": ">=18.0.0"}}