// Authentication Manager for S.T.E.V.I DOS Electron App
import { SecureConfigManager } from './secure-config.js';
import { SecureSessionManager } from './secure-session.js';

export class AuthManager {
    constructor() {
        this.config = new SecureConfigManager();
        this.sessionManager = new SecureSessionManager();
        this.currentUser = null;
        this.session = null;
        this.supabase = null;
        this.isTestMode = false; // Will be determined after config loads
        this.initialized = false;

        // Set up session callbacks
        this.sessionManager.setSessionExpiredCallback(() => {
            this.handleSessionExpiry();
        });

        this.sessionManager.setSessionRefreshCallback(async () => {
            return await this.refreshSession();
        });

        console.log('AuthManager created, awaiting secure configuration...');
    }

    async initializeSupabase() {
        try {
            console.log('=== SUPABASE INITIALIZATION START ===');

            // Initialize secure configuration first
            if (!this.config.configLoaded) {
                console.log('Step 1: Loading secure configuration...');
                await this.config.initialize();
            }

            // Validate configuration
            const validation = this.config.validateConfiguration();
            if (!validation.valid) {
                throw new Error(`Configuration validation failed: ${validation.issues.join(', ')}`);
            }

            if (validation.warnings.length > 0) {
                console.warn('Configuration warnings:', validation.warnings);
            }

            // Set test mode based on configuration
            this.isTestMode = this.config.isTestMode();
            console.log(`Test mode: ${this.isTestMode ? 'ENABLED (debug mode)' : 'DISABLED (production mode)'}`);

            console.log('Step 2: Loading Supabase client from CDN...');

            // Load Supabase from CDN instead of npm package
            if (!window.supabase) {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/dist/umd/supabase.js';
                document.head.appendChild(script);

                // Wait for script to load
                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                });

                console.log('Step 3: Supabase CDN script loaded');
            } else {
                console.log('Step 3: Supabase already available');
            }

            if (!window.supabase || !window.supabase.createClient) {
                throw new Error('Supabase client not available after loading');
            }

            const createClient = window.supabase.createClient;
            console.log('Step 4: Supabase createClient function available');

            // Get configuration from secure config manager
            const url = this.config.getSupabaseUrl();
            const key = this.config.getSupabaseAnonKey();

            if (!url || !key) {
                throw new Error('Missing Supabase configuration');
            }

            console.log('Step 5: Using secure configuration');
            console.log('URL:', url);
            console.log('Key prefix:', key.substring(0, 20) + '...');

            console.log('Step 6: Creating Supabase client...');
            this.supabase = createClient(url, key, {
                auth: {
                    autoRefreshToken: true,
                    persistSession: true,
                    detectSessionInUrl: false
                }
            });

            console.log('Step 7: Supabase client created successfully');
            console.log('Client type:', typeof this.supabase);
            console.log('Has auth:', !!this.supabase.auth);
            console.log('Auth type:', typeof this.supabase.auth);

            // Initialize VaultManager for secure API key management
            this.config.initializeVault(this.supabase);
            console.log('Step 8: VaultManager initialized');

            // Initialize global app config if available
            if (window.app && window.app.config) {
                window.app.config.initializeVault(this.supabase);
                console.log('Step 9: Global VaultManager initialized');
            }

            this.initialized = true;
            console.log('=== SUPABASE INITIALIZATION COMPLETE ===');
            return true;

        } catch (error) {
            console.error('=== SUPABASE INITIALIZATION FAILED ===');
            console.error('Error:', error);
            console.error('Stack:', error.stack);
            // Don't throw - let the app continue and show login screen
            console.warn('Continuing without Supabase - login will show appropriate error');
        }
    }

    async checkAuthentication() {
        // Check if we have a valid stored session
        const sessionData = this.sessionManager.getSession();
        if (sessionData) {
            console.log('Found valid stored session');
            this.currentUser = sessionData.user;
            this.session = sessionData;

            // Update activity to extend session if needed
            this.sessionManager.updateActivity();

            return true;
        }

        console.log('No valid authentication found - login required');
        return false;
    }

    isSupabaseReady() {
        const ready = !!(this.supabase && this.supabase.auth);
        console.log('Supabase ready check:', ready);
        if (!ready) {
            console.log('Supabase client:', this.supabase);
            console.log('Supabase auth:', this.supabase?.auth);
        }
        return ready;
    }

    clearStoredSession() {
        this.sessionManager.clearSession();
        localStorage.removeItem('stevidos_cache');
        this.currentUser = null;
        this.session = null;
    }

    async login(email, password) {
        try {
            // Ensure Supabase is initialized
            if (!this.initialized) {
                await this.initializeSupabase();
            }

            // Real Supabase authentication
            if (!this.supabase) {
                console.error('Supabase client is null - attempting to reinitialize...');
                try {
                    await this.initializeSupabase();
                    if (!this.supabase) {
                        throw new Error('Failed to initialize authentication service. Please check your internet connection and try again.');
                    }
                } catch (initError) {
                    console.error('Reinitialize failed:', initError);
                    throw new Error('Authentication service unavailable. Please check your internet connection and try again.');
                }
            }

            const { data, error } = await this.supabase.auth.signInWithPassword({
                email,
                password
            });

            if (error) {
                throw new Error(`Login failed: ${error.message}`);
            }

            // Verify user has required role
            const isAuthorized = await this.verifyRole(data.user);
            if (!isAuthorized) {
                await this.supabase.auth.signOut();
                throw new Error('Access denied. You must be an IHARC staff member to use this application.');
            }

            this.currentUser = data.user;
            this.session = data.session;

            // Determine user role
            const userRole = this.getUserRoleFromUser(data.user);

            // Refresh user permissions to ensure they're up to date (non-blocking)
            this.refreshUserPermissions(data.user.id).catch(error => {
                console.warn('Failed to refresh user permissions:', error);
            });

            // Also refresh permissions for current user immediately
            this.refreshUserPermissions().catch(error => {
                console.warn('Failed to refresh current user permissions:', error);
            });

            // Store session securely
            const sessionData = {
                user: {
                    email: data.user.email,
                    role: userRole,
                    name: data.user.email.split('@')[0],
                    id: data.user.id
                },
                token: data.session.access_token,
                refreshToken: data.session.refresh_token,
                supabaseSession: data.session
            };

            // Store session with 24 hour expiry
            this.sessionManager.storeSession(sessionData, 1440); // 24 hours in minutes

            return { success: true, user: sessionData.user };

        } catch (error) {
            console.error('Login error:', error);
            return {
                success: false,
                error: error.message || 'Authentication failed. Please try again.'
            };
        }
    }

    async verifyRole(user) {
        // Check if user has iharc_staff or iharc_admin role
        // First check if role is directly available in user object
        if (user.role === 'iharc_staff' || user.role === 'iharc_admin') {
            return true;
        }

        // Check user metadata (raw_user_meta_data and raw_app_meta_data)
        const userMetadata = user.user_metadata || user.raw_user_meta_data || {};
        const appMetadata = user.app_metadata || user.raw_app_meta_data || {};

        return (
            userMetadata.role === 'iharc_staff' ||
            userMetadata.role === 'iharc_admin' ||
            appMetadata.role === 'iharc_staff' ||
            appMetadata.role === 'iharc_admin' ||
            userMetadata.roles?.includes('iharc_staff') ||
            userMetadata.roles?.includes('iharc_admin') ||
            appMetadata.roles?.includes('iharc_staff') ||
            appMetadata.roles?.includes('iharc_admin')
        );
    }

    isAdmin() {
        if (!this.currentUser) {
            return false;
        }

        // Check if current user has admin role
        const user = this.currentUser;

        // First check if role is directly available in user object
        if (user.role === 'iharc_admin') {
            return true;
        }

        // Check user metadata (raw_user_meta_data and raw_app_meta_data)
        const userMetadata = user.user_metadata || user.raw_user_meta_data || {};
        const appMetadata = user.app_metadata || user.raw_app_meta_data || {};

        return (
            userMetadata.role === 'iharc_admin' ||
            appMetadata.role === 'iharc_admin' ||
            userMetadata.roles?.includes('iharc_admin') ||
            appMetadata.roles?.includes('iharc_admin')
        );
    }

    getUserRole() {
        return this.getUserRoleFromUser(this.currentUser);
    }

    getUserRoleFromUser(user) {
        // Extract primary role from user object using new permission system
        if (!user) return null;

        // Check app_metadata for claims first (new system)
        const appMetadata = user.app_metadata || user.raw_app_meta_data || {};
        if (appMetadata.claims && appMetadata.claims.roles && appMetadata.claims.roles.length > 0) {
            // Return the highest priority role
            const roles = appMetadata.claims.roles;
            if (roles.includes('iharc_admin')) return 'iharc_admin';
            if (roles.includes('iharc_supervisor')) return 'iharc_supervisor';
            if (roles.includes('iharc_staff')) return 'iharc_staff';
            if (roles.includes('iharc_volunteer')) return 'iharc_volunteer';
            return roles[0]; // Return first role if none match
        }

        // Fallback to user_metadata (legacy)
        const userMetadata = user.user_metadata || user.raw_user_meta_data || {};
        if (userMetadata.role) {
            return userMetadata.role;
        }

        // Fallback to app_metadata role (legacy)
        if (appMetadata.role) {
            return appMetadata.role;
        }

        return 'unknown';
    }

    /**
     * Get all user roles from claims
     */
    getUserRoles(user = this.currentUser) {
        if (!user) return [];

        // First try to get roles from JWT app_metadata (most efficient)
        const appMetadata = user.app_metadata || user.raw_app_meta_data || {};
        if (appMetadata.roles && Array.isArray(appMetadata.roles)) {
            return appMetadata.roles;
        }

        // Fallback to database call if roles not available
        if (this.supabase) {
            // This will be handled asynchronously, but for now return empty
            // The roles should be refreshed on login
            console.warn('User roles not found in JWT, should refresh permissions');
        }

        return [];
    }

    /**
     * Get user permissions from claims
     */
    getUserPermissions(user = this.currentUser) {
        if (!user) return [];

        // First try to get permissions from JWT app_metadata (most efficient)
        const appMetadata = user.app_metadata || user.raw_app_meta_data || {};
        if (appMetadata.permissions && Array.isArray(appMetadata.permissions)) {
            return appMetadata.permissions;
        }

        // Fallback to database call if permissions not available
        if (this.supabase) {
            // This will be handled asynchronously, but for now return empty
            // The permissions should be refreshed on login
            console.warn('User permissions not found in JWT, should refresh permissions');
        }

        return [];
    }

    /**
     * Check if user has specific permission
     */
    hasPermission(permission, user = this.currentUser) {
        const permissions = this.getUserPermissions(user);
        return permissions.includes(permission);
    }

    /**
     * Check if user has any permission in category
     */
    hasPermissionInCategory(category, user = this.currentUser) {
        const permissions = this.getUserPermissions(user);
        return permissions.some(perm => perm.startsWith(category + '.'));
    }

    /**
     * Check if user has specific role
     */
    hasRole(role, user = this.currentUser) {
        const roles = this.getUserRoles(user);
        return roles.includes(role);
    }

    /**
     * Refresh user permissions from database
     */
    async refreshUserPermissions(userId = null) {
        try {
            const targetUserId = userId || this.currentUser?.id;
            if (!targetUserId) {
                console.warn('No user ID provided for permissions refresh');
                return;
            }

            console.log('Refreshing user permissions for user:', targetUserId);
            // Try calling the function with schema prefix
            const { data, error } = await this.supabase.rpc('refresh_user_permissions', {
                user_uuid: targetUserId
            });

            if (error) {
                console.error('Error refreshing user permissions:', error);
                return;
            }

            console.log('User permissions refreshed successfully:', data);
            
            // Update the current user object with new permissions
            if (this.currentUser && targetUserId === this.currentUser.id) {
                this.currentUser = {
                    ...this.currentUser,
                    app_metadata: {
                        ...this.currentUser.app_metadata,
                        permissions: data.permissions || []
                    }
                };
                console.log('Updated current user with new permissions:', this.currentUser.app_metadata);
            }
            
            return data;
        } catch (error) {
            console.error('Error refreshing user permissions:', error);
        }
    }

    async logout() {
        try {
            // Sign out from Supabase if we have an active session
            if (this.supabase && this.session) {
                console.log('Signing out from Supabase...');
                await this.supabase.auth.signOut();
            }
        } catch (error) {
            console.error('Error signing out from Supabase:', error);
            // Continue with local cleanup even if Supabase signout fails
        }

        // Clear local state
        this.currentUser = null;
        this.session = null;
        localStorage.removeItem('stevidos_session');

        // Clear any other stored data if needed
        localStorage.removeItem('stevidos_cache');

        console.log('Logout completed - all sessions cleared');
    }

    async refreshSession() {
        try {
            const supabase = this.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            console.log('🔄 Refreshing user session...');
            const { data, error } = await supabase.auth.refreshSession();

            if (error) {
                console.error('Session refresh failed:', error);
                throw error;
            }

            if (data.user) {
                console.log('✅ Session refreshed for user:', data.user.email);
                console.log('🔐 User metadata:', data.user.user_metadata);
                this.currentUser = data.user;
                this.session = data.session;
                return { success: true, user: data.user };
            } else {
                throw new Error('No user in refreshed session');
            }
        } catch (error) {
            console.error('Failed to refresh session:', error);
            return { success: false, error: error.message };
        }
    }

    getCurrentUser() {
        return this.currentUser;
    }

    getSession() {
        return this.session;
    }

    isAuthenticated() {
        return this.currentUser !== null;
    }

    // Utility method for simulating delays
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Debug function to check and fix user permissions
    async debugUserPermissions() {
        try {
            console.log('=== DEBUG USER PERMISSIONS ===');
            console.log('Current user:', this.currentUser?.email);
            console.log('Current user ID:', this.currentUser?.id);
            
            // Check current permissions in JWT
            const currentPermissions = this.currentUser?.app_metadata?.permissions;
            console.log('Current permissions in JWT:', currentPermissions);
            
            // Check current roles (derived from permissions)
            const currentRoles = this.getUserRoles();
            console.log('Current roles (derived):', currentRoles);
            
            // Check current permissions (from our function)
            const currentPermissionsFromFunction = this.getUserPermissions();
            console.log('Current permissions (from function):', currentPermissionsFromFunction);
            
            // Try to refresh permissions
            console.log('Refreshing permissions...');
            const refreshedPermissions = await this.refreshUserPermissions();
            console.log('Refreshed permissions:', refreshedPermissions);
            
            // Check if user has admin permission in database
            if (this.currentUser?.id) {
                const { data: dbPermissions, error: dbPermError } = await this.supabase.rpc('get_user_permissions', {
                    user_uuid: this.currentUser.id
                });
                console.log('Database permissions:', dbPermissions, 'Error:', dbPermError);
                
                // Check if user has admin permission by checking the database directly
                const { data: userPermissions, error: permError } = await this.supabase.rpc('get_user_permissions', {
                    user_uuid: this.currentUser.id
                });
                console.log('Database user permissions:', userPermissions, 'Error:', permError);
                
                const hasAdminAccess = userPermissions && userPermissions.some(p => p.permission_name === 'admin.access');
                console.log('Has admin.access permission:', hasAdminAccess);
            }
            
            console.log('=== END DEBUG ===');
            
        } catch (error) {
            console.error('Error in debugUserPermissions:', error);
        }
    }

    // Password reset functionality
    async resetPassword(email) {
        try {
            const supabase = this.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { error } = await supabase.auth.resetPasswordForEmail(email, {
                redirectTo: window.location.origin + '/reset-password'
            });

            if (error) {
                throw error;
            }

            return { success: true, message: 'Password reset email sent successfully' };

        } catch (error) {
            console.error('Password reset error:', error);
            return { success: false, message: error.message };
        }
    }

    // Update user profile
    async updateProfile(updates) {
        try {

            const supabase = this.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data, error } = await supabase.auth.updateUser({
                data: updates
            });

            if (error) {
                throw error;
            }

            // Update local user data
            this.currentUser = { ...this.currentUser, ...data.user.user_metadata };

            return { success: true, message: 'Profile updated successfully' };

        } catch (error) {
            console.error('Profile update error:', error);
            return { success: false, message: error.message };
        }
    }

    // Change password
    async changePassword(newPassword) {
        try {

            const supabase = this.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { error } = await supabase.auth.updateUser({
                password: newPassword
            });

            if (error) {
                throw error;
            }

            return { success: true, message: 'Password changed successfully' };

        } catch (error) {
            console.error('Password change error:', error);
            return { success: false, message: error.message };
        }
    }

    // Method to refresh session if needed
    async refreshSession() {
        if (!this.session || !this.supabase) {
            return false;
        }

        try {
            console.log('🔄 Refreshing user session...');

            // Use Supabase to refresh the session
            const { data, error } = await this.supabase.auth.refreshSession();

            if (error) {
                console.error('Session refresh failed:', error);
                return false;
            }

            if (data.user && data.session) {
                console.log('✅ Session refreshed successfully');

                // Update stored session
                const sessionData = {
                    user: {
                        email: data.user.email,
                        role: this.getUserRoleFromUser(data.user),
                        name: data.user.email.split('@')[0],
                        id: data.user.id
                    },
                    token: data.session.access_token,
                    refreshToken: data.session.refresh_token,
                    supabaseSession: data.session
                };

                // Store refreshed session
                this.sessionManager.storeSession(sessionData, 1440); // 24 hours
                this.currentUser = sessionData.user;
                this.session = sessionData;

                return true;
            }

            return false;
        } catch (error) {
            console.error('Session refresh error:', error);
            return false;
        }
    }

    /**
     * Handle session expiry
     */
    handleSessionExpiry() {
        console.warn('Session expired - redirecting to login');
        this.clearStoredSession();

        // Notify the app that session has expired
        if (window.app && window.app.showLogin) {
            window.app.showLogin();
        }

        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('sessionExpired'));
    }

    /**
     * Update user activity to extend session
     */
    updateActivity() {
        if (this.sessionManager.isSessionValid()) {
            this.sessionManager.updateActivity();
        }
    }

    /**
     * Get session metadata
     */
    getSessionMetadata() {
        return this.sessionManager.getSessionMetadata();
    }

    // Method to check if session is about to expire
    isSessionExpiringSoon() {
        if (!this.session || !this.session.expiresAt) {
            return true;
        }

        const expirationTime = new Date(this.session.expiresAt);
        const currentTime = new Date();
        const timeUntilExpiration = expirationTime - currentTime;
        
        // Return true if session expires in less than 1 hour
        return timeUntilExpiration < (60 * 60 * 1000);
    }
}
