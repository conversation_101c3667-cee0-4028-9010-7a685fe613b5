# 🚀 S.T.E.V.I Retro Release Guide

## How to Create a Stable Release

The GitHub Actions workflow will **only build and deploy** stable releases (non-prerelease, non-draft). This prevents unnecessary builds on beta/alpha versions.

### Method 1: GitHub Web Interface (Recommended)

1. **Go to your repository**: https://github.com/iharc-jordan/stevi_dos
2. **Click "Releases"** (right sidebar or `/releases`)
3. **Click "Create a new release"**
4. **Fill out the release form**:
   - **Tag version**: `v1.0.0` (semantic version with 'v' prefix)
   - **Release title**: `S.T.E.V.I Retro v1.0.0 - Stable Release`
   - **Description**: Add your release notes (will be included in the app)
   - **Pre-release**: ❌ **Leave unchecked** for stable releases
   - **Draft**: ❌ **Leave unchecked** to publish immediately
5. **Click "Publish release"**

### Method 2: Command Line

```bash
# Create and push a version tag
git tag v1.0.0
git push stevi_dos v1.0.0

# Then create the release on GitHub web interface
# Or use GitHub CLI:
gh release create v1.0.0 --title "S.T.E.V.I Retro v1.0.0 - Stable Release" --notes "Your release notes here"
```

## What Happens When You Create a Release

### ✅ Automatic Workflow Trigger
1. **Detects "stable" tag** - Only builds releases with "stable" in tag or title
2. **Builds Windows installer** - Creates `.exe` file with electron-builder
3. **Generates metadata** - Version info, checksums, download URLs
4. **Uploads to Azure** - Deploys to your blob storage container
5. **Updates latest pointers** - Updates latest-version.txt and latest-metadata.json

### 📁 Files Created in Azure Blob Storage
```
https://iharcpublicappblob.blob.core.windows.net/stevi/stevi retro/
├── latest-version.txt                           # "1.0.0"
├── latest-metadata.json                         # Latest version metadata
└── v1.0.0/                                     # Version-specific folder
    ├── S.T.E.V.I-Retro-Setup-1.0.0.exe        # Windows installer
    ├── metadata.json                           # Version metadata
    └── release-notes.md                        # Formatted release notes
```

## Release Naming Convention

### ✅ Stable Releases (Will Build)
- `v1.0.0` (not marked as pre-release)
- `v2.1.3` (not marked as pre-release)
- `1.0.0` (not marked as pre-release)

### ❌ Pre-releases/Drafts (Will Skip)
- `v1.0.0-beta` (marked as pre-release)
- `v1.0.0-alpha` (marked as pre-release)
- `v1.0.0-rc1` (marked as pre-release)
- Any draft release

**Note**: The workflow builds any published release that is **not** marked as "pre-release" and **not** a draft.

## Release Notes Best Practices

Your release notes will be displayed in the app, so make them user-friendly:

```markdown
## 🚀 What's New in v1.0.0

### ✨ New Features
- Enhanced incident reporting with photo attachments
- Improved offline synchronization
- New dashboard with real-time statistics

### 🔧 Improvements
- Faster startup time (50% improvement)
- Better error handling and user feedback
- Updated security protocols

### 🐛 Bug Fixes
- Fixed issue with data sync on slow connections
- Resolved crash when uploading large files
- Corrected timezone handling in reports

### 🔒 Security
- Updated authentication system
- Enhanced data encryption
- Improved access control validation
```

## Testing a Release

### Before Publishing
1. **Test locally**: Ensure the app builds and runs correctly
2. **Check version**: Verify package.json version matches your release
3. **Review changes**: Make sure all intended changes are included

### After Publishing
1. **Monitor workflow**: Check GitHub Actions for build status
2. **Verify deployment**: Test the Azure endpoints
3. **Test update system**: Try the update functionality in the app

```bash
# Test Azure endpoints after deployment
node test-azure-endpoints.js

# Check specific version
curl "https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/latest-version.txt"
```

## Manual Testing (For Development)

If you need to test the workflow without creating a public release:

1. **Go to GitHub Actions**
2. **Select "Build and Deploy S.T.E.V.I Retro"**
3. **Click "Run workflow"**
4. **Enter version number** (e.g., "1.0.0-test")
5. **Click "Run workflow"**

This will build and deploy without creating a public release.

## Troubleshooting

### Workflow Doesn't Trigger
- ✅ Check that release tag contains "stable"
- ✅ Verify release is published (not draft)
- ✅ Ensure GitHub Actions are enabled

### Build Fails
- ✅ Check GitHub Actions logs for errors
- ✅ Verify Azure connection string is set in secrets
- ✅ Test local build with `npm run build-win`

### Deployment Fails
- ✅ Verify Azure storage connection string
- ✅ Check container permissions
- ✅ Review Azure CLI logs in workflow

## Version Management

### Semantic Versioning
- **Major** (1.0.0 → 2.0.0): Breaking changes
- **Minor** (1.0.0 → 1.1.0): New features, backward compatible
- **Patch** (1.0.0 → 1.0.1): Bug fixes, backward compatible

### Update package.json
The workflow automatically updates package.json version to match your release tag, but you can also update it manually:

```json
{
  "version": "1.0.0"
}
```

## Next Steps

1. **Create your first stable release** using the guide above
2. **Monitor the deployment** in GitHub Actions
3. **Test the update system** in the application
4. **Iterate and improve** based on user feedback

---

**🎯 Remember**: Only releases tagged with "stable" will trigger builds and deployments!
