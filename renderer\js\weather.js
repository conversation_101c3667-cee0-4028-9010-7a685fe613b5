// Weather Service for S.T.E.V.I Retro - Google Weather API Integration
import { ConfigManager } from './config.js';

export class WeatherService {
    constructor(configManager = null) {
        this.config = configManager || new ConfigManager();
        this.apiKey = null; // Will be loaded asynchronously from Vault
        this.location = 'Cobourg, Ontario, Canada';
        this.coordinates = { lat: 43.9589, lng: -78.1648 }; // Cobourg coordinates
        this.cache = new Map();
        this.cacheTimeout = 10 * 60 * 1000; // 10 minutes
    }

    /**
     * Get Google API key from Vault or fallback
     * @returns {Promise<string>} The Google API key
     */
    async getApiKey() {
        if (!this.apiKey) {
            try {
                console.log('WeatherService: Attempting to get Google API key from vault...');
                console.log('WeatherService: Config has vaultManager:', !!this.config.vaultManager);
                this.apiKey = await this.config.getGoogleApiKey();
                console.log('WeatherService: Successfully retrieved API key from vault:', !!this.apiKey);
            } catch (error) {
                console.error('WeatherService: Error getting Google API key from vault:', error);
                console.log('WeatherService: Attempting fallback sync method...');
                this.apiKey = this.config.getGoogleApiKeySync();
                console.log('WeatherService: Fallback API key result:', !!this.apiKey);
            }
        }
        return this.apiKey;
    }

    /**
     * Get current weather for Cobourg, Ontario
     * @returns {Promise<Object>} Weather data
     */
    async getCurrentWeather() {
        const cacheKey = 'current_weather';
        const cached = this.cache.get(cacheKey);
        
        // Return cached data if still valid
        if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
            console.log('Returning cached weather data');
            return cached.data;
        }

        try {
            console.log('Fetching fresh weather data for Cobourg, ON');

            // Try to get the API key first
            const apiKey = await this.getApiKey();
            if (!apiKey) {
                console.warn('No API key available, using fallback weather data');
                return this.getFallbackWeatherData('Weather data temporarily unavailable - API key not configured');
            }

            // Use Google Weather API with the provided API key
            const weatherData = await this.fetchGoogleWeatherData(apiKey);
            
            // Cache the result
            this.cache.set(cacheKey, {
                data: weatherData,
                timestamp: Date.now()
            });

            return weatherData;
        } catch (error) {
            console.error('Error fetching weather data:', error);
            
            // Return cached data if available, even if expired
            if (cached) {
                console.log('Returning expired cached weather data due to error');
                return cached.data;
            }
            
            // Return fallback data with error message
            return this.getFallbackWeatherData(`Weather data temporarily unavailable: ${error.message}`);
        }
    }

    /**
     * Fetch weather data from Google Weather API
     * @returns {Promise<Object>} Weather data
     */
    async fetchGoogleWeatherData(apiKey) {
        if (!apiKey) {
            throw new Error('Google API key not available');
        }

        const url = `https://weather.googleapis.com/v1/currentConditions:lookup?key=${apiKey}&location.latitude=${this.coordinates.lat}&location.longitude=${this.coordinates.lng}&unitsSystem=METRIC`;

        console.log('Fetching weather from Google Weather API for Cobourg, ON');

        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`Google Weather API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        // Transform Google Weather API response to our format
        return {
            location: 'Cobourg, ON',
            temperature: Math.round(data.temperature?.degrees || 0),
            condition: this.mapGoogleConditionToText(data.weatherCondition?.type, data.weatherCondition?.description?.text),
            feelsLike: Math.round(data.feelsLikeTemperature?.degrees || data.temperature?.degrees || 0),
            windSpeed: Math.round(data.wind?.speed?.value || 0),
            humidity: data.relativeHumidity || 0,
            alert: this.generateWeatherAlert(data),
            timestamp: data.currentTime || new Date().toISOString(),
            source: 'Google Weather API',
            uvIndex: data.uvIndex || 0,
            visibility: data.visibility?.distance || 0,
            pressure: data.airPressure?.meanSeaLevelMillibars || 0,
            cloudCover: data.cloudCover || 0,
            isDaytime: data.isDaytime || true
        };
    }

    /**
     * Map Google Weather API condition types to readable text
     * @param {string} type - Google weather condition type
     * @param {string} description - Google weather description
     * @returns {string} Readable condition text
     */
    mapGoogleConditionToText(type, description) {
        if (description) return description;

        const conditionMap = {
            'CLEAR': 'Clear',
            'CLOUDY': 'Cloudy',
            'PARTLY_CLOUDY': 'Partly Cloudy',
            'OVERCAST': 'Overcast',
            'RAIN': 'Rain',
            'LIGHT_RAIN': 'Light Rain',
            'HEAVY_RAIN': 'Heavy Rain',
            'SNOW': 'Snow',
            'LIGHT_SNOW': 'Light Snow',
            'HEAVY_SNOW': 'Heavy Snow',
            'THUNDERSTORM': 'Thunderstorm',
            'FOG': 'Fog',
            'MIST': 'Mist'
        };

        return conditionMap[type] || 'Unknown';
    }

    /**
     * Generate weather alerts based on Google Weather API data
     * @param {Object} data - Google Weather API response
     * @returns {string|null} Alert message
     */
    generateWeatherAlert(data) {
        const temp = data.temperature?.degrees || 0;
        const feelsLike = data.feelsLikeTemperature?.degrees || temp;
        const windSpeed = data.wind?.speed?.value || 0;
        const condition = data.weatherCondition?.type || '';

        // Extreme cold warning
        if (feelsLike <= -15) {
            return 'Extreme cold warning - Warming centers open. Limit outdoor exposure.';
        }

        // Cold weather alert
        if (feelsLike <= -5) {
            return 'Cold weather alert - Extra blankets recommended for outreach.';
        }

        // Snow advisory
        if (condition.includes('SNOW') && condition.includes('HEAVY')) {
            return 'Heavy snow warning - Hazardous driving conditions expected.';
        }

        if (condition.includes('SNOW')) {
            return 'Snow advisory in effect - Slippery conditions expected.';
        }

        // High wind warning
        if (windSpeed >= 50) {
            return 'High wind warning - Secure loose objects and use caution outdoors.';
        }

        // Thunderstorm warning
        if (condition.includes('THUNDERSTORM')) {
            return 'Thunderstorm warning - Seek shelter immediately.';
        }

        // Heat warning
        if (temp >= 30) {
            return 'Heat warning - Stay hydrated and seek shade during outreach.';
        }

        return null; // No alerts
    }

    /**
     * Get fallback weather data when API is unavailable
     * @returns {Object} Fallback weather data
     */
    getFallbackWeatherData(message = 'Weather data temporarily unavailable') {
        return {
            location: 'Cobourg, ON',
            temperature: -2,
            condition: 'Data Unavailable',
            feelsLike: -5,
            windSpeed: 15,
            humidity: 70,
            alert: message,
            timestamp: new Date().toISOString(),
            source: 'Fallback Data',
            uvIndex: 0,
            visibility: 0,
            pressure: 0,
            cloudCover: 0,
            isDaytime: true
        };
    }

    /**
     * Format weather data for display
     * @param {Object} weatherData - Raw weather data
     * @returns {Object} Formatted weather data
     */
    formatWeatherData(weatherData) {
        return {
            temperature: `${weatherData.temperature}°C`,
            condition: weatherData.condition,
            feelsLike: `${weatherData.feelsLike}°C`,
            wind: `${weatherData.windSpeed} km/h`,
            humidity: `${weatherData.humidity}%`,
            alert: weatherData.alert,
            location: weatherData.location,
            lastUpdated: new Date(weatherData.timestamp).toLocaleTimeString(),
            source: weatherData.source
        };
    }

    /**
     * Get weather icon based on condition
     * @param {string} condition - Weather condition
     * @returns {string} Weather emoji
     */
    getWeatherIcon(condition) {
        const iconMap = {
            'Clear': '☀️',
            'Partly Cloudy': '⛅',
            'Overcast': '☁️',
            'Cloudy': '☁️',
            'Snow': '❄️',
            'Light Snow': '🌨️',
            'Rain': '🌧️',
            'Light Rain': '🌦️',
            'Thunderstorm': '⛈️',
            'Fog': '🌫️',
            'Data Unavailable': '❓'
        };

        return iconMap[condition] || '🌤️';
    }

    /**
     * Get weather alert severity
     * @param {string} alert - Alert message
     * @returns {string} Alert severity level
     */
    getAlertSeverity(alert) {
        if (!alert) return 'none';
        
        const lowerAlert = alert.toLowerCase();
        if (lowerAlert.includes('warning') || lowerAlert.includes('extreme')) {
            return 'severe';
        } else if (lowerAlert.includes('advisory') || lowerAlert.includes('alert')) {
            return 'moderate';
        } else {
            return 'low';
        }
    }

    /**
     * Clear weather cache
     */
    clearCache() {
        this.cache.clear();
        console.log('Weather cache cleared');
    }

    /**
     * Get cache status
     * @returns {Object} Cache information
     */
    getCacheStatus() {
        const cached = this.cache.get('current_weather');
        if (!cached) {
            return { cached: false, age: 0 };
        }

        const age = Date.now() - cached.timestamp;
        return {
            cached: true,
            age: Math.floor(age / 1000), // Age in seconds
            valid: age < this.cacheTimeout
        };
    }
}
