// Bike Manager for S.T.E.V.I Retro - Bike Registration and Theft Management
export class BikeManager {
    constructor(dataManager, authManager) {
        this.data = dataManager;
        this.auth = authManager;
        this.currentUser = null;
    }

    // Generate unique bike registration number
    generateBikeNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const time = now.getTime().toString().slice(-4);
        return `BIKE-${year}${month}${day}-${time}`;
    }

    // Register a new bike
    async registerBike(bikeData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const now = new Date();

            const bikeRecord = {
                user_id: currentUser?.id || null,
                owner_name: bikeData.owner_name,
                owner_email: bikeData.owner_email || currentUser?.email,
                serial_number: bikeData.serial_number,
                make: bikeData.make,
                model: bikeData.model,
                color: bikeData.color,
                value: bikeData.value || null,
                photo_base64: bikeData.photo_base64 || null,
                is_stolen: false,
                registered_at: now.toISOString(),
                created_at: now.toISOString(),
                updated_at: now.toISOString()
            };

            const result = await this.data.insert('bikes', bikeRecord);
            
            // Log the registration activity
            if (result && result.id) {
                await this.logBikeActivity(result.id, 'registration', 'Bike registered', 
                    `Bike registered by ${currentUser?.email || 'System'}`);
            }

            return result;
        } catch (error) {
            console.error('Error registering bike:', error);
            throw error;
        }
    }

    // Report a bike as stolen
    async reportStolen(bikeId, theftData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const now = new Date();

            const updateData = {
                is_stolen: true,
                theft_date: theftData.theft_date,
                theft_time: theftData.theft_time,
                theft_location: theftData.theft_location,
                suspect_details: theftData.suspect_details || null,
                theft_notes: theftData.theft_notes || null,
                reported_stolen_at: now.toISOString(),
                updated_at: now.toISOString()
            };

            const result = await this.data.update('bikes', bikeId, updateData);
            
            // Log the theft report activity
            await this.logBikeActivity(bikeId, 'theft_report', 'Bike reported stolen', 
                `Theft reported at ${theftData.theft_location} on ${theftData.theft_date}`);

            return result;
        } catch (error) {
            console.error('Error reporting bike stolen:', error);
            throw error;
        }
    }

    // Mark bike as recovered
    async markRecovered(bikeId, recoveryData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const now = new Date();

            const updateData = {
                is_stolen: false,
                updated_at: now.toISOString()
            };

            const result = await this.data.update('bikes', bikeId, updateData);
            
            // Log the recovery activity
            await this.logBikeActivity(bikeId, 'recovery', 'Bike recovered', 
                `Bike recovered at ${recoveryData.location || 'Unknown location'}`);

            return result;
        } catch (error) {
            console.error('Error marking bike as recovered:', error);
            throw error;
        }
    }

    // Log bike activity
    async logBikeActivity(bikeId, activityType, title, description, additionalData = {}) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const now = new Date();

            const activityRecord = {
                bike_id: bikeId,
                activity_type: activityType,
                title: title,
                description: description,
                location: additionalData.location || null,
                activity_date: additionalData.activity_date || now.toISOString().split('T')[0],
                activity_time: additionalData.activity_time || now.toTimeString().split(' ')[0],
                staff_member: currentUser?.email || 'System',
                officer_badge: additionalData.officer_badge || null,
                citation_number: additionalData.citation_number || null,
                outcome: additionalData.outcome || null,
                follow_up_required: additionalData.follow_up_required || false,
                follow_up_date: additionalData.follow_up_date || null,
                priority: additionalData.priority || null,
                tags: additionalData.tags || null,
                attachments: additionalData.attachments || null,
                created_at: now.toISOString(),
                created_by: currentUser?.email || 'System'
            };

            return await this.data.insert('bike_activities', activityRecord);
        } catch (error) {
            console.error('Error logging bike activity:', error);
            throw error;
        }
    }

    // Get all bikes with optional filtering
    async getAllBikes(filters = {}) {
        try {
            let bikes = await this.data.getAll('bikes');
            
            if (!bikes) return [];

            // Apply filters
            if (filters.is_stolen !== undefined) {
                bikes = bikes.filter(b => b.is_stolen === filters.is_stolen);
            }
            
            if (filters.owner_name) {
                bikes = bikes.filter(b => 
                    b.owner_name.toLowerCase().includes(filters.owner_name.toLowerCase())
                );
            }
            
            if (filters.serial_number) {
                bikes = bikes.filter(b => 
                    b.serial_number.toLowerCase().includes(filters.serial_number.toLowerCase())
                );
            }

            // Sort by most recent first
            return bikes.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        } catch (error) {
            console.error('Error getting bikes:', error);
            throw error;
        }
    }

    // Search bikes
    async searchBikes(searchTerm) {
        try {
            const bikes = await this.data.getAll('bikes');
            
            if (!bikes || !searchTerm) return bikes || [];

            const term = searchTerm.toLowerCase();
            
            return bikes.filter(bike => 
                bike.serial_number.toLowerCase().includes(term) ||
                bike.make.toLowerCase().includes(term) ||
                bike.model.toLowerCase().includes(term) ||
                bike.color.toLowerCase().includes(term) ||
                bike.owner_name.toLowerCase().includes(term) ||
                (bike.owner_email && bike.owner_email.toLowerCase().includes(term))
            );
        } catch (error) {
            console.error('Error searching bikes:', error);
            throw error;
        }
    }

    // Get bike by ID
    async getBike(bikeId) {
        try {
            return await this.data.get('bikes', bikeId);
        } catch (error) {
            console.error('Error getting bike:', error);
            throw error;
        }
    }

    // Get bike activities
    async getBikeActivities(bikeId) {
        try {
            const activities = await this.data.getAll('bike_activities');
            if (!activities) return [];
            
            return activities
                .filter(activity => activity.bike_id === bikeId)
                .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        } catch (error) {
            console.error('Error getting bike activities:', error);
            throw error;
        }
    }

    // Get stolen bikes statistics
    async getStolenBikesStats() {
        try {
            const bikes = await this.data.getAll('bikes');
            
            if (!bikes) return {};

            const stats = {
                total: bikes.length,
                stolen: bikes.filter(b => b.is_stolen).length,
                recovered: bikes.filter(b => !b.is_stolen && b.reported_stolen_at).length,
                registered: bikes.filter(b => !b.is_stolen && !b.reported_stolen_at).length
            };

            return stats;
        } catch (error) {
            console.error('Error getting stolen bikes stats:', error);
            return {};
        }
    }

    // Update bike information
    async updateBike(bikeId, updateData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const now = new Date();

            updateData.updated_at = now.toISOString();

            const result = await this.data.update('bikes', bikeId, updateData);
            
            // Log the update activity
            await this.logBikeActivity(bikeId, 'update', 'Bike information updated', 
                `Bike details updated by ${currentUser?.email || 'System'}`);

            return result;
        } catch (error) {
            console.error('Error updating bike:', error);
            throw error;
        }
    }

    // Delete bike
    async deleteBike(bikeId) {
        try {
            const currentUser = this.auth.getCurrentUser();
            
            // Log the deletion before actually deleting
            await this.logBikeActivity(bikeId, 'deletion', 'Bike record deleted', 
                `Bike record deleted by ${currentUser?.email || 'System'}`);

            return await this.data.delete('bikes', bikeId);
        } catch (error) {
            console.error('Error deleting bike:', error);
            throw error;
        }
    }
}
