// Data Manager for S.T.E.V.I DOS Electron App
import { ConfigManager } from './config.js';
import { SchemaManager } from './schema.js';

export class DataManager {
    constructor(authManager = null) {
        this.config = new ConfigManager();
        this.auth = authManager;
        this.cache = new Map();
        this.isOnline = navigator.onLine;
        this.isTestMode = this.config.isTestMode();
        this.schema = new SchemaManager(this);
        this.syncInProgress = false;
        this.syncInterval = null;

        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncPendingData();
            this.startPeriodicSync();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.stopPeriodicSync();
        });

        // Start periodic sync if online
        if (this.isOnline) {
            this.startPeriodicSync();
        }
    }

    async initialize() {
        await this.schema.initialize();
    }

    getSupabaseClient() {
        return this.auth?.supabase || null;
    }

    getFullTableName(tableName) {
        // Map table names to their appropriate schemas
        const coreSchema = [
            'people', 'pets', 'medical_issues', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items',
            'supply_provisions', 'addresses', 'address_activities',
            'organizations', 'media', 'vehicle_activities', 'bikes', 'bike_activities',
            'encampments'
        ];

        const auditSchema = [
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        const caseMgmtSchema = [
            'incidents', 'incident_links', 'property_records',
            'property_actions', 'license_plates'
        ];

        if (coreSchema.includes(tableName)) {
            return `core.${tableName}`;
        } else if (auditSchema.includes(tableName)) {
            return `audit.${tableName}`;
        } else if (caseMgmtSchema.includes(tableName)) {
            return `case_mgmt.${tableName}`;
        } else {
            // Default to public for bikes and AI tables
            return tableName;
        }
    }

    // Generic data operations
    async get(table, id) {
        const cacheKey = `${table}_${id}`;

        try {
            // When online, always fetch fresh data from Supabase
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    const fullTableName = this.getFullTableName(table);
                    const { data, error } = await supabase
                        .from(fullTableName)
                        .select('*')
                        .eq('id', id)
                        .single();

                    if (!error && data) {
                        // Update cache and local storage with fresh data
                        this.cache.set(cacheKey, data);
                        this.saveToLocalStorage(table, data);
                        return data;
                    }
                }
            }

            // When offline, check cache first, then localStorage
            if (this.cache.has(cacheKey)) {
                return this.cache.get(cacheKey);
            }

            // Fallback to localStorage
            const data = this.getFromLocalStorage(table, id);

            if (data) {
                this.cache.set(cacheKey, data);
            }

            return data;
        } catch (error) {
            console.error('Error fetching data:', error);
            // Try cache first, then localStorage as final fallback
            if (this.cache.has(cacheKey)) {
                return this.cache.get(cacheKey);
            }
            return this.getFromLocalStorage(table, id);
        }
    }

    async insert(table, data) {
        try {
            // Don't generate ID - let Supabase handle it with BIGSERIAL
            const record = {
                ...data,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            // Try Supabase first if online and available
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    const fullTableName = this.getFullTableName(table);
                    const { data: result, error } = await supabase
                        .from(fullTableName)
                        .insert(record)
                        .select();

                    if (!error && result && result.length > 0) {
                        // Cache the result
                        const cacheKey = `${table}_${result[0].id}`;
                        this.cache.set(cacheKey, result[0]);

                        // Store locally as backup
                        this.saveToLocalStorage(table, result[0]);

                        return result[0]; // Return the single record, not array
                    }
                    // If Supabase failed, fall through to local storage
                }
            }

            // Fallback to local storage (offline mode or Supabase failed)
            // Generate a temporary ID for offline storage
            const tempId = this.generateId();
            const recordWithTempId = { ...record, id: tempId };

            this.saveToLocalStorage(table, recordWithTempId);

            // Cache it
            const cacheKey = `${table}_${tempId}`;
            this.cache.set(cacheKey, recordWithTempId);

            // Queue for sync when online
            if (!this.isOnline || this.isTestMode) {
                this.queueForSync(table, 'insert', recordWithTempId);
            }

            return recordWithTempId;
        } catch (error) {
            console.error('Error inserting data:', error);

            // Fallback to local storage and queue for sync
            const id = data.id || this.generateId();
            const record = {
                id: id,
                ...data,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            this.saveToLocalStorage(table, record);
            this.queueForSync(table, 'insert', record);

            return [record];
        }
    }

    async update(table, id, data) {
        try {
            const existing = await this.get(table, id);
            if (!existing) {
                throw new Error('Record not found');
            }

            const updated = {
                ...existing,
                ...data,
                updated_at: new Date().toISOString()
            };

            // Try Supabase first if online and available
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    const fullTableName = this.getFullTableName(table);
                    const { data: result, error } = await supabase
                        .from(fullTableName)
                        .update(data)
                        .eq('id', id)
                        .select();

                    if (!error && result && result.length > 0) {
                        // Update cache
                        const cacheKey = `${table}_${id}`;
                        this.cache.set(cacheKey, result[0]);

                        // Store locally as backup
                        this.saveToLocalStorage(table, result[0]);

                        return result[0];
                    }
                    // If Supabase failed, fall through to local storage
                }
            }

            // Fallback to local storage
            this.saveToLocalStorage(table, updated);

            // Update cache
            const cacheKey = `${table}_${id}`;
            this.cache.set(cacheKey, updated);

            // Queue for sync if offline
            if (!this.isOnline) {
                this.queueForSync(table, 'update', updated);
            }

            return updated;
        } catch (error) {
            console.error('Error updating data:', error);
            throw error;
        }
    }

    async delete(table, id) {
        try {
            // Try Supabase first if online and available
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    const fullTableName = this.getFullTableName(table);
                    const { error } = await supabase
                        .from(fullTableName)
                        .delete()
                        .eq('id', id);

                    if (!error) {
                        // Remove from cache
                        const cacheKey = `${table}_${id}`;
                        this.cache.delete(cacheKey);

                        // Remove from localStorage
                        this.removeFromLocalStorage(table, id);

                        return true;
                    }
                    // If Supabase failed, fall through to local storage
                }
            }

            // Fallback to local storage
            this.removeFromLocalStorage(table, id);

            // Remove from cache
            const cacheKey = `${table}_${id}`;
            this.cache.delete(cacheKey);

            // Queue for sync if offline
            if (!this.isOnline) {
                this.queueForSync(table, 'delete', { id });
            }

            return true;
        } catch (error) {
            console.error('Error deleting data:', error);
            throw error;
        }
    }

    async getAll(table) {
        try {
            // When online, always fetch fresh data from Supabase
            if (this.isOnline && !this.isTestMode) {
                const supabase = this.getSupabaseClient();
                if (supabase) {
                    const fullTableName = this.getFullTableName(table);
                    const { data, error } = await supabase
                        .from(fullTableName)
                        .select('*')
                        .order('created_at', { ascending: false });

                    if (!error && data) {
                        // Update cache and local storage with fresh data
                        data.forEach(record => {
                            const cacheKey = `${table}_${record.id}`;
                            this.cache.set(cacheKey, record);
                            this.saveToLocalStorage(table, record);
                        });

                        return data;
                    }
                }
            }

            // When offline, fallback to localStorage
            return this.getAllFromLocalStorage(table);
        } catch (error) {
            console.error('Error fetching all data:', error);
            // Try localStorage as final fallback
            return this.getAllFromLocalStorage(table);
        }
    }

    async search(table, query) {
        try {
            let allRecords;

            // When online, get fresh data; when offline, use local data
            if (this.isOnline && !this.isTestMode) {
                // Try to get fresh data from Supabase
                allRecords = await this.getAll(table);
            } else {
                // Get all records from localStorage and deduplicate
                allRecords = this.getAllFromLocalStorage(table);

                // Check for duplicates and clean them up if found
                const uniqueIds = new Set();
                const hasDuplicates = allRecords.some(record => {
                    if (uniqueIds.has(record.id)) {
                        return true;
                    }
                    uniqueIds.add(record.id);
                    return false;
                });

                if (hasDuplicates) {
                    console.log(`Duplicates detected in ${table}, cleaning up...`);
                    allRecords = this.deduplicateRecords(table);
                }
            }

            if (!allRecords) {
                return [];
            }

            if (!query || Object.keys(query).length === 0) {
                return allRecords;
            }

            // Simple search implementation
            return allRecords.filter(record => {
                return Object.entries(query).every(([key, value]) => {
                    if (!record[key]) return false;

                    const recordValue = record[key].toString().toLowerCase();
                    const searchValue = value.toString().toLowerCase();

                    return recordValue.includes(searchValue);
                });
            });
        } catch (error) {
            console.error('Error searching data:', error);
            return [];
        }
    }

    // Local storage helpers
    getFromLocalStorage(table, id) {
        const key = `stevidos_${table}`;
        const data = localStorage.getItem(key);
        
        if (data) {
            const records = JSON.parse(data);
            return records.find(record => record.id === id);
        }
        
        return null;
    }

    getAllFromLocalStorage(table) {
        const key = `stevidos_${table}`;
        const data = localStorage.getItem(key);
        
        if (data) {
            return JSON.parse(data);
        }
        
        return [];
    }

    saveToLocalStorage(table, record) {
        const key = `stevidos_${table}`;
        const existing = this.getAllFromLocalStorage(table);
        
        // Update existing record or add new one
        const index = existing.findIndex(r => r.id === record.id);
        if (index >= 0) {
            existing[index] = record;
        } else {
            existing.push(record);
        }
        
        localStorage.setItem(key, JSON.stringify(existing));
    }

    removeFromLocalStorage(table, id) {
        const key = `stevidos_${table}`;
        const existing = this.getAllFromLocalStorage(table);
        const filtered = existing.filter(r => r.id !== id);

        localStorage.setItem(key, JSON.stringify(filtered));
    }

    // Clean up duplicate records
    deduplicateRecords(table) {
        const key = `stevidos_${table}`;
        const existing = this.getAllFromLocalStorage(table);

        // Create a map to track unique records by ID
        const uniqueRecords = new Map();

        existing.forEach(record => {
            if (record.id) {
                // Keep the record with more complete data (more properties)
                const existingRecord = uniqueRecords.get(record.id);
                if (!existingRecord || Object.keys(record).length > Object.keys(existingRecord).length) {
                    uniqueRecords.set(record.id, record);
                }
            }
        });

        // Convert back to array and save
        const deduplicatedRecords = Array.from(uniqueRecords.values());
        localStorage.setItem(key, JSON.stringify(deduplicatedRecords));

        console.log(`Deduplicated ${table}: ${existing.length} -> ${deduplicatedRecords.length} records`);
        return deduplicatedRecords;
    }

    // Sync helpers
    queueForSync(table, operation, data) {
        const syncQueue = JSON.parse(localStorage.getItem('stevidos_sync_queue') || '[]');
        
        syncQueue.push({
            table,
            operation,
            data,
            timestamp: new Date().toISOString()
        });
        
        localStorage.setItem('stevidos_sync_queue', JSON.stringify(syncQueue));
    }

    async syncPendingData() {
        if (this.syncInProgress) {
            console.log('Sync already in progress, skipping...');
            return;
        }

        const syncQueue = JSON.parse(localStorage.getItem('stevidos_sync_queue') || '[]');

        if (syncQueue.length === 0) {
            return;
        }

        console.log(`Syncing ${syncQueue.length} pending operations...`);
        this.syncInProgress = true;

        const supabase = this.getSupabaseClient();
        if (!supabase) {
            console.warn('No Supabase client available for sync');
            this.syncInProgress = false;
            return;
        }

        const successfulSyncs = [];
        const failedSyncs = [];

        for (const syncItem of syncQueue) {
            try {
                const { table, operation, data, timestamp } = syncItem;
                const fullTableName = this.getFullTableName(table);

                switch (operation) {
                    case 'insert':
                        const { data: insertResult, error: insertError } = await supabase
                            .from(fullTableName)
                            .insert(data)
                            .select();

                        if (insertError) throw insertError;

                        // Update local storage with server-generated data
                        if (insertResult && insertResult.length > 0) {
                            this.saveToLocalStorage(table, insertResult[0]);
                            const cacheKey = `${table}_${insertResult[0].id}`;
                            this.cache.set(cacheKey, insertResult[0]);
                        }
                        break;

                    case 'update':
                        const { data: updateResult, error: updateError } = await supabase
                            .from(fullTableName)
                            .update(data)
                            .eq('id', data.id)
                            .select();

                        if (updateError) throw updateError;

                        // Update local storage and cache
                        if (updateResult && updateResult.length > 0) {
                            this.saveToLocalStorage(table, updateResult[0]);
                            const cacheKey = `${table}_${updateResult[0].id}`;
                            this.cache.set(cacheKey, updateResult[0]);
                        }
                        break;

                    case 'delete':
                        const { error: deleteError } = await supabase
                            .from(fullTableName)
                            .delete()
                            .eq('id', data.id);

                        if (deleteError) throw deleteError;

                        // Remove from local storage and cache
                        this.removeFromLocalStorage(table, data.id);
                        const cacheKey = `${table}_${data.id}`;
                        this.cache.delete(cacheKey);
                        break;

                    default:
                        console.warn(`Unknown sync operation: ${operation}`);
                }

                successfulSyncs.push(syncItem);
                console.log(`Successfully synced ${operation} for ${table}`);

            } catch (error) {
                console.error(`Failed to sync ${syncItem.operation} for ${syncItem.table}:`, error);
                failedSyncs.push(syncItem);
            }
        }

        // Update sync queue with only failed items
        localStorage.setItem('stevidos_sync_queue', JSON.stringify(failedSyncs));

        console.log(`Sync completed: ${successfulSyncs.length} successful, ${failedSyncs.length} failed`);
        this.syncInProgress = false;

        // If there are failed syncs, we might want to retry later
        if (failedSyncs.length > 0) {
            console.warn(`${failedSyncs.length} sync operations failed and will be retried later`);
        }
    }

    // Manual sync trigger
    async forceSyncPendingData() {
        console.log('Manually triggering sync...');
        await this.syncPendingData();
    }

    // Periodic sync management
    startPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }

        // Sync every 5 minutes when online
        this.syncInterval = setInterval(() => {
            if (this.isOnline && !this.isTestMode) {
                this.syncPendingData();
            }
        }, 5 * 60 * 1000); // 5 minutes

        console.log('Periodic sync started (every 5 minutes)');
    }

    stopPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
            console.log('Periodic sync stopped');
        }
    }

    // Utility methods
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    clearCache() {
        this.cache.clear();
    }

    getStats() {
        return {
            cacheSize: this.cache.size,
            isOnline: this.isOnline,
            pendingSync: JSON.parse(localStorage.getItem('stevidos_sync_queue') || '[]').length
        };
    }
}
