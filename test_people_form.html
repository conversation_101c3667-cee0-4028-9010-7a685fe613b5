<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test People Form</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        textarea { height: 80px; resize: vertical; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .test-result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <h1>Test People Form - Updated Schema</h1>
    <p>This form tests the updated people schema without full_name field and with new homeless/addiction service fields.</p>
    
    <form id="testForm">
        <div class="form-group">
            <label for="first_name">First Name:</label>
            <input type="text" id="first_name" name="first_name" required>
        </div>
        
        <div class="form-group">
            <label for="last_name">Last Name:</label>
            <input type="text" id="last_name" name="last_name" required>
        </div>
        
        <div class="form-group">
            <label for="date_of_birth">Date of Birth:</label>
            <input type="date" id="date_of_birth" name="date_of_birth">
        </div>
        
        <div class="form-group">
            <label for="phone">Phone:</label>
            <input type="tel" id="phone" name="phone">
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email">
        </div>
        
        <div class="form-group">
            <label for="preferred_pronouns">Preferred Pronouns:</label>
            <input type="text" id="preferred_pronouns" name="preferred_pronouns" placeholder="e.g., he/him, she/her, they/them">
        </div>
        
        <div class="form-group">
            <label for="primary_language">Primary Language:</label>
            <select id="primary_language" name="primary_language">
                <option value="">Select...</option>
                <option value="English">English</option>
                <option value="French">French</option>
                <option value="Spanish">Spanish</option>
                <option value="Indigenous Language">Indigenous Language</option>
                <option value="Other">Other</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="housing_status">Housing Status:</label>
            <select id="housing_status" name="housing_status">
                <option value="">Select...</option>
                <option value="Housed">Housed</option>
                <option value="Temporarily Housed">Temporarily Housed</option>
                <option value="Unsheltered">Unsheltered</option>
                <option value="Emergency Shelter">Emergency Shelter</option>
                <option value="Transitional Housing">Transitional Housing</option>
                <option value="Unknown">Unknown</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="income_source">Income Source:</label>
            <select id="income_source" name="income_source">
                <option value="">Select...</option>
                <option value="Employment">Employment</option>
                <option value="Social Assistance">Social Assistance</option>
                <option value="Disability Benefits">Disability Benefits</option>
                <option value="Pension">Pension</option>
                <option value="Employment Insurance">Employment Insurance</option>
                <option value="None">None</option>
                <option value="Other">Other</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="risk_level">Risk Level:</label>
            <select id="risk_level" name="risk_level">
                <option value="">Select...</option>
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="has_id_documents">Has ID Documents:</label>
            <select id="has_id_documents" name="has_id_documents">
                <option value="">Select...</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="veteran_status">Veteran Status:</label>
            <select id="veteran_status" name="veteran_status">
                <option value="">Select...</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="mental_health_concerns">Mental Health Concerns:</label>
            <textarea id="mental_health_concerns" name="mental_health_concerns" placeholder="Brief notes about mental health status or concerns"></textarea>
        </div>
        
        <div class="form-group">
            <label for="medications">Current Medications:</label>
            <textarea id="medications" name="medications" placeholder="List current medications"></textarea>
        </div>
        
        <div class="form-group">
            <label for="chronic_conditions">Chronic Conditions:</label>
            <textarea id="chronic_conditions" name="chronic_conditions" placeholder="Ongoing medical conditions"></textarea>
        </div>
        
        <div class="form-group">
            <label for="disabilities">Disabilities:</label>
            <textarea id="disabilities" name="disabilities" placeholder="Physical or cognitive disabilities"></textarea>
        </div>
        
        <div class="form-group">
            <label for="case_manager">Case Manager:</label>
            <input type="text" id="case_manager" name="case_manager" placeholder="Assigned case manager or social worker">
        </div>
        
        <div class="form-group">
            <label for="service_barriers">Service Barriers:</label>
            <textarea id="service_barriers" name="service_barriers" placeholder="Barriers to accessing services"></textarea>
        </div>
        
        <div class="form-group">
            <label for="support_network">Support Network:</label>
            <textarea id="support_network" name="support_network" placeholder="Family, friends, or other support people"></textarea>
        </div>
        
        <div class="form-group">
            <label for="last_service_date">Last Service Date:</label>
            <input type="date" id="last_service_date" name="last_service_date">
        </div>
        
        <div class="form-group">
            <label for="notes">Notes:</label>
            <textarea id="notes" name="notes" placeholder="Additional notes"></textarea>
        </div>
        
        <button type="submit">Test Form Submission</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // Calculate age if date_of_birth is provided
            if (data.date_of_birth) {
                const birthDate = new Date(data.date_of_birth);
                const today = new Date();
                let age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();
                
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }
                
                data.age = age >= 0 ? age : null;
            }
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="test-result success">
                    <h3>Form Data (would be sent to database):</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                    <p><strong>Note:</strong> Full name field is no longer present. Age is auto-calculated from date of birth: ${data.age || 'N/A'}</p>
                </div>
            `;
        });
    </script>
</body>
</html>
