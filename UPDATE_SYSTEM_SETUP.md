# S.T.E.V.I Retro Update System Setup Guide

## Overview
The update system has been completely rewritten to use Azure Blob Storage for secure, scalable updates:
- ✅ Development environment protection
- ✅ Secure Azure Blob Storage integration (no embedded tokens)
- ✅ Automated GitHub Actions workflow for building and deployment
- ✅ Checksum verification for download integrity
- ✅ Comprehensive metadata and release notes
- ✅ User-friendly update dialogs
- ✅ Automatic update checking on startup

## Architecture

### Azure Blob Storage Structure
```
https://iharcpublicappblob.blob.core.windows.net/stevi/stevi retro/
├── latest-version.txt                    # Current stable version number
├── latest-metadata.json                  # Metadata for latest version
└── v{version}/                          # Version-specific folders
    ├── S.T.E.V.I-Retro-Setup-{version}.exe  # Windows installer
    ├── metadata.json                     # Version metadata
    └── release-notes.md                  # Release notes
```

## Required Setup Steps

### 1. Azure Storage Account Configuration

- **Storage Account**: `iharcpublicappblob`
- **Container**: `stevi`
- **Base Path**: `stevi retro`
- **Access**: Public read access (no authentication required)

### 2. GitHub Secrets Configuration

Add the following secret to your GitHub repository:

1. Go to your repository → Settings → Secrets and variables → Actions
2. Add new repository secret:
   - **Name**: `AZURE_STORAGE_CONNECTION_STRING`
   - **Value**: Your Azure Storage connection string

To get the connection string:
1. Go to Azure Portal → Storage Accounts → iharcpublicappblob
2. Access keys → Show keys → Copy connection string

### 3. GitHub Actions Workflow

The workflow is triggered by:
- **Automatic**: Push tags matching `v*-stable` (e.g., `v1.0.0-stable`)
- **Manual**: Workflow dispatch with version input

### 4. Creating a Release

To create a new release:

#### Option A: Automatic (Recommended)
1. Update version in `package.json` if needed
2. Commit your changes
3. Create and push a tag:
   ```bash
   git tag v1.0.0-stable
   git push origin v1.0.0-stable
   ```
4. GitHub Actions will automatically build and deploy

#### Option B: Manual
1. Go to GitHub → Actions → "Build and Deploy S.T.E.V.I Retro"
2. Click "Run workflow"
3. Enter the version number (e.g., "1.0.0")
4. Click "Run workflow"

### 5. Monitoring Deployments

Check deployment status:
1. GitHub → Actions → View the running workflow
2. Monitor build and deploy jobs
3. Check deployment summary for download links

## Features Implemented

### Development Environment Protection
- Automatically detects development environments
- Prevents accidental updates that could overwrite local changes
- Checks for: dev flags, package.json, node_modules, .git directory
- Provides override option for advanced users

### Update Process
1. **Automatic Check**: App checks for updates 5 seconds after startup
2. **Manual Check**: Users can check via System → Check for Updates
3. **Notification**: Shows update dialog if newer stable version available
4. **Download**: Downloads update with progress indicator
5. **Installation**: Launches installer and closes current app

### Release Notes
- Accessible via System → Release Notes
- Shows current version information
- Displays formatted release notes from GitHub
- Fallback content when GitHub is unavailable

### User Interface
- Retro-styled update dialogs matching app theme
- Progress indicators for downloads
- Clear error messages and warnings
- Development mode notifications

## Security Considerations

### GitHub Token Security
- Token has minimal permissions (read-only repository access)
- Token is embedded in the application (acceptable for this use case)
- Consider rotating the token periodically

### Update Verification
- Downloads are verified by file size
- Future enhancement: Add checksum verification

## Testing the Update System

### In Development Environment
1. Run the app normally - updates will be disabled
2. Check System → Check for Updates - should show development mode dialog
3. Use "Force Enable" option to test update functionality (advanced)

### In Production Environment
1. Build and install the production version
2. Updates will be enabled automatically
3. Test the complete update flow

## Troubleshooting

### Common Issues

**"Updates are disabled in development environment"**
- This is expected when running from source
- Use the production build to test updates
- Or use "Force Enable" option for testing

**"GitHub API error: 404"**
- Check if the repository name is correct
- Verify the GitHub token has proper permissions
- Ensure the repository exists and is accessible

**"No stable release found"**
- Create a release tagged with "stable"
- Check that the release is published (not draft)

**Download fails**
- Verify release assets are attached
- Check internet connection
- Ensure proper file permissions

### Debug Information
The update system logs detailed information to the console:
- Development environment detection
- GitHub API responses
- Download progress
- Error details

## Future Enhancements

Potential improvements for the update system:
- [ ] Checksum verification for downloads
- [ ] Automatic updates (with user permission)
- [ ] Update scheduling
- [ ] Rollback functionality
- [ ] Delta updates for smaller downloads
- [ ] Update notifications in system tray

## File Structure

The update system consists of:
- `electron/updater.js` - Main update manager (main process)
- `renderer/js/updater.js` - Update UI manager (renderer process)
- `renderer/js/commands.js` - Update commands integration
- `renderer/js/app.js` - App integration and startup checks
- `renderer/styles.css` - Update dialog styling

## Support

For issues with the update system:
1. Check the console logs for detailed error information
2. Verify GitHub token and repository configuration
3. Test with a simple release first
4. Contact the development team for assistance
