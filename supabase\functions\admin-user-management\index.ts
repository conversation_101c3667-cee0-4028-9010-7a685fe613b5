// Supabase Edge Function for Secure Admin User Management
// Handles all user management operations with proper security and audit logging

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

interface AdminUser {
  id: string;
  email: string;
  user_metadata?: {
    role?: string;
    full_name?: string;
  };
  app_metadata?: {
    role?: string;
  };
  created_at: string;
  last_sign_in_at?: string;
}

interface CreateUserRequest {
  email: string;
  password: string;
  full_name?: string;
  role?: 'iharc_staff' | 'iharc_admin' | 'iharc_supervisor' | 'iharc_volunteer';
  iharc_staff_id?: string;
  ranger_id?: string;
}

interface UpdateUserRequest {
  full_name?: string;
  role?: string; // Allow any role name now that we have custom roles
  iharc_staff_id?: string;
  ranger_id?: string;
}

interface CreateRoleRequest {
  name: string;
  display_name: string;
  description?: string;
  permissions: string[];
}

interface UpdateRoleRequest {
  display_name?: string;
  description?: string;
  permissions?: string[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase clients
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    )

    // Verify authentication and admin role
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid token' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check admin permissions using new permission system
    const { data: hasPermission, error: permissionError } = await supabaseClient
      .rpc('has_permission_single', { permission_name: 'users.read' });

    if (permissionError || !hasPermission) {
      return new Response(
        JSON.stringify({ error: 'Permission denied: users.read required' }),
        {
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse URL and method
    const url = new URL(req.url)
    const pathParts = url.pathname.split('/').filter(Boolean)
    const method = req.method

    // Helper function to log admin actions
    const logAdminAction = async (action: string, targetUserId?: string, details?: any) => {
      try {
        await supabaseAdmin
          .from('audit.admin_actions')
          .insert({
            admin_user_id: user.id,
            action: action,
            target_user_id: targetUserId,
            details: details,
            ip_address: req.headers.get('x-forwarded-for') || 'unknown'
          })
      } catch (error) {
        console.error('Failed to log admin action:', error)
      }
    }

    // Helper function to get user roles from core schema
    const getUserRoles = async (user: AdminUser): Promise<string[]> => {
      try {
        // Get user roles from new permission system
        const { data: rolesData, error: rolesError } = await supabaseClient
          .rpc('get_user_roles', { user_uuid: user.id });

        if (!rolesError && rolesData) {
          return rolesData;
        }

        return [];
      } catch (error) {
        console.error('Error getting user roles:', error);
        return [];
      }
    }

    // Helper function to get user role (primary role)
    const getUserRole = async (user: AdminUser): Promise<string> => {
      const roles = await getUserRoles(user);
      return roles.length > 0 ? roles[0] : 'unknown';
    }

    // Helper function to check if user has IHARC access
    const hasIharcAccess = async (user: AdminUser): Promise<boolean> => {
      const roles = await getUserRoles(user);
      return roles.some(role => role.startsWith('iharc_'));
    }

    // Route handling
    if (method === 'GET' && pathParts.length === 0) {
      // GET /admin-user-management - List all IHARC users
      const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers()
      
      if (error) {
        throw error
      }

      // Filter to only IHARC users
      const iharcUsers = [];
      for (const user of users) {
        const hasAccess = await hasIharcAccess(user);
        if (hasAccess) {
          iharcUsers.push(user);
        }
      }

      await logAdminAction('LIST_USERS', undefined, { count: iharcUsers.length })

      return new Response(
        JSON.stringify({ users: iharcUsers }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (method === 'POST' && pathParts.length === 0) {
      // POST /admin-user-management - Create new user
      const body: CreateUserRequest = await req.json()
      const { email, password, full_name, iharc_staff_id } = body

      // Default role to iharc_staff if not specified (for regular staff users)
      const role = body.role || 'iharc_staff';

      // Validate input
      if (!email || !password) {
        return new Response(
          JSON.stringify({ error: 'Email and password are required' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Validate role
      const validRoles = ['iharc_staff', 'iharc_admin', 'iharc_supervisor', 'iharc_volunteer'];
      if (!validRoles.includes(role)) {
        return new Response(
          JSON.stringify({ error: `Invalid role. Must be one of: ${validRoles.join(', ')}` }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Check if user has permission to create users
      const { data: canCreateUsers, error: createPermError } = await supabaseClient
        .rpc('has_permission_single', { permission_name: 'users.create' });

      if (createPermError || !canCreateUsers) {
        return new Response(
          JSON.stringify({ error: 'Permission denied: users.create required' }),
          {
            status: 403,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return new Response(
          JSON.stringify({ error: 'Invalid email format' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      const { data, error } = await supabaseAdmin.auth.admin.createUser({
        email: email,
        password: password,
        user_metadata: {
          full_name: full_name || '',
          role: role,
          iharc_staff_id: iharc_staff_id || '',
          ranger_id: body.ranger_id || ''
        },
        email_confirm: true
      })

      if (error) {
        throw error
      }

      // Assign role in core.user_roles table using the database function
      const { error: roleError } = await supabaseAdmin.rpc('assign_user_role', {
        target_user_id: data.user.id,
        role_name: role,
        granted_by: user.id
      });

      if (roleError) {
        console.error('Failed to assign role in core.user_roles table:', roleError);

        // This is critical - if role assignment fails, we should clean up the user
        try {
          await supabaseAdmin.auth.admin.deleteUser(data.user.id);
          console.log('Cleaned up user after role assignment failure');
        } catch (cleanupError) {
          console.error('Failed to cleanup user after role assignment failure:', cleanupError);
        }

        await logAdminAction('CREATE_USER_ROLE_ERROR', data.user.id, {
          email: email,
          role: role,
          error: roleError.message
        });

        return new Response(
          JSON.stringify({ error: 'Failed to assign user role. User creation cancelled.' }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      } else {
        console.log(`Successfully assigned role ${role} to user ${data.user.id}`);

        // Verify role assignment was successful
        const { data: verifyRole, error: verifyError } = await supabaseAdmin
          .from('core.user_roles')
          .select('role_id')
          .eq('user_id', data.user.id)
          .single();

        if (verifyError || !verifyRole) {
          console.error('Role verification failed after assignment');
          await logAdminAction('CREATE_USER_ROLE_VERIFY_ERROR', data.user.id, {
            email: email,
            expected_role: role
          });
        }
      }

      await logAdminAction('CREATE_USER', data.user.id, {
        email: email,
        role: role,
        full_name: full_name
      })

      return new Response(
        JSON.stringify({ user: data.user }),
        { 
          status: 201, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (method === 'PUT' && pathParts.length === 1) {
      // PUT /admin-user-management/{userId} - Update user
      const userId = pathParts[0]
      const body: UpdateUserRequest = await req.json()
      const { full_name, role } = body

      // Check if user has permission to update users
      const { data: canUpdateUsers, error: updatePermError } = await supabaseClient
        .rpc('has_permission_single', { permission_name: 'users.update' });

      if (updatePermError || !canUpdateUsers) {
        return new Response(
          JSON.stringify({ error: 'Permission denied: users.update required' }),
          {
            status: 403,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // SECURITY CHECK: Verify user exists and has IHARC role before updating
      const { data: { user: existingUser } } = await supabaseAdmin.auth.admin.getUserById(userId)
      const existingRole = await getUserRole(existingUser)
      if (existingRole !== 'iharc_staff' && existingRole !== 'iharc_admin' && existingRole !== 'iharc_supervisor' && existingRole !== 'iharc_volunteer') {
        return new Response(
          JSON.stringify({ error: 'Cannot update user: User does not have IHARC role' }),
          {
            status: 403,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (role && !['iharc_staff', 'iharc_admin', 'iharc_supervisor', 'iharc_volunteer'].includes(role)) {
        return new Response(
          JSON.stringify({ error: 'Invalid role' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      const updateData = {
        user_metadata: {
          full_name: full_name || '',
          role: role,
          iharc_staff_id: body.iharc_staff_id || '',
          ranger_id: body.ranger_id || ''
        }
      }

      const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, updateData)

      if (error) {
        throw error
      }

      // Update role in core.user_roles table if role is being changed
      if (role) {
        // First remove existing roles
        const { error: removeError } = await supabaseAdmin.rpc('remove_user_role', {
          target_user_id: userId,
          role_name: existingRole
        });

        if (removeError) {
          console.error('Failed to remove existing role:', removeError);
        }

        // Then assign new role
        const { error: roleError } = await supabaseAdmin.rpc('assign_user_role', {
          target_user_id: userId,
          role_name: role,
          assigned_by: user.id
        });

        if (roleError) {
          console.error('Failed to update role in core.user_roles table:', roleError);
          await logAdminAction('UPDATE_USER_ROLE_ERROR', userId, {
            role: role,
            error: roleError.message
          });
        } else {
          console.log(`Successfully updated role to ${role} for user ${userId}`);
        }
      }

      await logAdminAction('UPDATE_USER', userId, {
        full_name: full_name,
        role: role,
        iharc_staff_id: body.iharc_staff_id
      })

      return new Response(
        JSON.stringify({ user: data.user }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (method === 'DELETE' && pathParts.length === 1) {
      // DELETE /admin-user-management/{userId} - Delete user
      const userId = pathParts[0]

      // Get user info before deletion for logging and verification
      const { data: { user: userToDelete } } = await supabaseAdmin.auth.admin.getUserById(userId)

      // SECURITY CHECK: Only allow deletion of users with IHARC roles
      const userRole = await getUserRole(userToDelete)
      if (!['iharc_staff', 'iharc_admin', 'iharc_supervisor', 'iharc_volunteer'].includes(userRole)) {
        return new Response(
          JSON.stringify({ error: 'Cannot delete user: User does not have IHARC role' }),
          {
            status: 403,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      const { error } = await supabaseAdmin.auth.admin.deleteUser(userId)

      if (error) {
        throw error
      }

      await logAdminAction('DELETE_USER', userId, {
        email: userToDelete?.email,
        role: await getUserRole(userToDelete)
      })

      return new Response(
        JSON.stringify({ success: true }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (method === 'POST' && pathParts.length === 2 && pathParts[1] === 'reset-password') {
      // POST /admin-user-management/{userId}/reset-password - Reset user password
      const userId = pathParts[0]

      // Get user email
      const { data: { user: targetUser } } = await supabaseAdmin.auth.admin.getUserById(userId)
      
      if (!targetUser) {
        return new Response(
          JSON.stringify({ error: 'User not found' }),
          { 
            status: 404, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      // Send password reset email
      const { error } = await supabaseAdmin.auth.resetPasswordForEmail(targetUser.email)

      if (error) {
        throw error
      }

      await logAdminAction('RESET_PASSWORD', userId, { 
        email: targetUser.email 
      })

      return new Response(
        JSON.stringify({ success: true }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Route not found
    return new Response(
      JSON.stringify({ error: 'Route not found' }),
      { 
        status: 404, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
