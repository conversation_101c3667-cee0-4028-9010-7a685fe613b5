// Property Manager for S.T.E.V.I Retro - Stolen/Discarded Property Return System
export class PropertyManager {
    constructor(dataManager, authManager) {
        this.data = dataManager;
        this.auth = authManager;
        this.currentUser = null;
    }

    // Generate unique property number
    generatePropertyNumber() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const time = now.getTime().toString().slice(-4);
        return `PROP-${year}${month}${day}-${time}`;
    }

    // Note: IDs are auto-generated by Supabase using BIGSERIAL, so we don't need generateId

    // Log new property find
    async logProperty(propertyData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const now = new Date();

            const propertyRecord = {
                property_number: this.generatePropertyNumber(),
                incident_id: propertyData.incident_id || null,
                property_type: propertyData.property_type,
                category: propertyData.category || 'found',
                description: propertyData.description,
                brand: propertyData.brand || null,
                model: propertyData.model || null,
                serial_number: propertyData.serial_number || null,
                color: propertyData.color || null,
                estimated_value: propertyData.estimated_value || null,
                condition: propertyData.condition || 'unknown',
                found_location: propertyData.found_location,
                found_coordinates: propertyData.found_coordinates || null,
                found_date: propertyData.found_date || now.toISOString().split('T')[0],
                found_time: propertyData.found_time || now.toTimeString().split(' ')[0],
                found_by: propertyData.found_by || currentUser?.email || 'Unknown',
                status: 'found',
                investigation_notes: propertyData.investigation_notes || null,
                photos: propertyData.photos ? JSON.stringify(propertyData.photos) : null,
                created_at: now.toISOString(),
                created_by: currentUser?.email || 'System',
                updated_at: now.toISOString(),
                updated_by: currentUser?.email || 'System'
            };

            // Save property record
            const result = await this.data.insert('property_records', propertyRecord);

            // Log the action (using the returned ID from the insert)
            if (result && result.id) {
                await this.logAction(result.id, 'found', 'Property logged into system', null, 'found', propertyData.investigation_notes);
            }

            return result;
        } catch (error) {
            console.error('Error logging property:', error);
            throw error;
        }
    }

    // Update property status
    async updateStatus(propertyId, newStatus, notes = null) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const property = await this.data.get('property_records', propertyId);
            
            if (!property) {
                throw new Error('Property not found');
            }

            const oldStatus = property.status;
            const updateData = {
                status: newStatus,
                updated_at: new Date().toISOString(),
                updated_by: currentUser?.email || 'System'
            };

            // Add status-specific fields
            if (newStatus === 'returned') {
                updateData.returned_date = new Date().toISOString().split('T')[0];
                updateData.returned_time = new Date().toTimeString().split(' ')[0];
            } else if (newStatus === 'handed_to_police') {
                updateData.handed_to_police_date = new Date().toISOString().split('T')[0];
            }

            await this.data.update('property_records', propertyId, updateData);

            // Log the action
            await this.logAction(propertyId, 'status_changed', `Status changed from ${oldStatus} to ${newStatus}`, oldStatus, newStatus, notes);

            return true;
        } catch (error) {
            console.error('Error updating property status:', error);
            throw error;
        }
    }

    // Update investigation notes
    async updateInvestigation(propertyId, notes) {
        try {
            const currentUser = this.auth.getCurrentUser();
            
            await this.data.update('property_records', propertyId, {
                investigation_notes: notes,
                updated_at: new Date().toISOString(),
                updated_by: currentUser?.email || 'System'
            });

            // Log the action
            await this.logAction(propertyId, 'investigation_updated', 'Investigation notes updated', null, null, notes);

            return true;
        } catch (error) {
            console.error('Error updating investigation:', error);
            throw error;
        }
    }

    // Mark property as returned
    async markAsReturned(propertyId, returnData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const now = new Date();

            const updateData = {
                status: 'returned',
                return_location: returnData.return_location,
                returned_to: returnData.returned_to,
                returned_date: returnData.returned_date || now.toISOString().split('T')[0],
                returned_time: returnData.returned_time || now.toTimeString().split(' ')[0],
                updated_at: now.toISOString(),
                updated_by: currentUser?.email || 'System'
            };

            await this.data.update('property_records', propertyId, updateData);

            // Log the action
            await this.logAction(propertyId, 'returned', `Property returned to ${returnData.returned_to} at ${returnData.return_location}`, null, 'returned', returnData.notes);

            return true;
        } catch (error) {
            console.error('Error marking property as returned:', error);
            throw error;
        }
    }

    // Hand property to police
    async handToPolice(propertyId, policeData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const now = new Date();

            const updateData = {
                status: 'handed_to_police',
                police_file_number: policeData.police_file_number,
                police_officer: policeData.police_officer,
                handed_to_police_date: policeData.handed_date || now.toISOString().split('T')[0],
                updated_at: now.toISOString(),
                updated_by: currentUser?.email || 'System'
            };

            await this.data.update('property_records', propertyId, updateData);

            // Log the action
            await this.logAction(propertyId, 'handed_to_police', `Property handed to police officer ${policeData.police_officer}`, null, 'handed_to_police', policeData.notes);

            return true;
        } catch (error) {
            console.error('Error handing property to police:', error);
            throw error;
        }
    }

    // Log action for audit trail
    async logAction(propertyId, actionType, description, oldStatus = null, newStatus = null, notes = null) {
        try {
            const currentUser = this.auth.getCurrentUser();
            
            const actionRecord = {
                property_id: propertyId,
                action_type: actionType,
                action_description: description,
                old_status: oldStatus,
                new_status: newStatus,
                notes: notes,
                performed_by: currentUser?.email || 'System',
                performed_at: new Date().toISOString()
            };

            await this.data.insert('property_actions', actionRecord);
            return true;
        } catch (error) {
            console.error('Error logging action:', error);
            throw error;
        }
    }

    // Get all properties with optional filtering
    async getAllProperties(filters = {}) {
        try {
            let properties = await this.data.getAll('property_records');
            
            if (!properties) return [];

            // Apply filters
            if (filters.status) {
                properties = properties.filter(p => p.status === filters.status);
            }
            
            if (filters.property_type) {
                properties = properties.filter(p => p.property_type === filters.property_type);
            }
            
            if (filters.date_from) {
                properties = properties.filter(p => p.found_date >= filters.date_from);
            }
            
            if (filters.date_to) {
                properties = properties.filter(p => p.found_date <= filters.date_to);
            }

            // Sort by found date (newest first)
            properties.sort((a, b) => new Date(b.found_date) - new Date(a.found_date));

            return properties;
        } catch (error) {
            console.error('Error getting properties:', error);
            throw error;
        }
    }

    // Get property by ID
    async getProperty(propertyId) {
        try {
            return await this.data.get('property_records', propertyId);
        } catch (error) {
            console.error('Error getting property:', error);
            throw error;
        }
    }

    // Get property actions (audit trail)
    async getPropertyActions(propertyId) {
        try {
            const actions = await this.data.query('property_actions', { property_id: propertyId });
            
            if (!actions) return [];

            // Sort by date (newest first)
            actions.sort((a, b) => new Date(b.performed_at) - new Date(a.performed_at));

            return actions;
        } catch (error) {
            console.error('Error getting property actions:', error);
            throw error;
        }
    }

    // Get property statistics
    async getPropertyStats() {
        try {
            const properties = await this.data.getAll('property_records');
            
            if (!properties) return {};

            const stats = {
                total: properties.length,
                found: properties.filter(p => p.status === 'found').length,
                investigating: properties.filter(p => p.status === 'investigating').length,
                returned: properties.filter(p => p.status === 'returned').length,
                handed_to_police: properties.filter(p => p.status === 'handed_to_police').length,
                pending: properties.filter(p => ['found', 'investigating', 'owner_identified'].includes(p.status)).length
            };

            return stats;
        } catch (error) {
            console.error('Error getting property stats:', error);
            return {};
        }
    }

    // Search properties
    async searchProperties(searchTerm) {
        try {
            const properties = await this.data.getAll('property_records');
            
            if (!properties || !searchTerm) return properties || [];

            const term = searchTerm.toLowerCase();
            
            return properties.filter(property => 
                property.property_number.toLowerCase().includes(term) ||
                property.description.toLowerCase().includes(term) ||
                (property.brand && property.brand.toLowerCase().includes(term)) ||
                (property.model && property.model.toLowerCase().includes(term)) ||
                (property.serial_number && property.serial_number.toLowerCase().includes(term)) ||
                property.found_location.toLowerCase().includes(term) ||
                (property.owner_name && property.owner_name.toLowerCase().includes(term))
            );
        } catch (error) {
            console.error('Error searching properties:', error);
            throw error;
        }
    }

    // Advanced reporting and analytics
    async getPropertyReport(dateFrom, dateTo, filters = {}) {
        try {
            const properties = await this.getAllProperties(filters);

            // Filter by date range if provided
            let filteredProperties = properties;
            if (dateFrom) {
                filteredProperties = filteredProperties.filter(p => p.found_date >= dateFrom);
            }
            if (dateTo) {
                filteredProperties = filteredProperties.filter(p => p.found_date <= dateTo);
            }

            // Generate report statistics
            const report = {
                totalProperties: filteredProperties.length,
                byStatus: {},
                byType: {},
                byCategory: {},
                averageProcessingTime: 0,
                overdueProperties: [],
                recentReturns: [],
                topLocations: {}
            };

            // Group by status
            filteredProperties.forEach(property => {
                report.byStatus[property.status] = (report.byStatus[property.status] || 0) + 1;
                report.byType[property.property_type] = (report.byType[property.property_type] || 0) + 1;
                report.byCategory[property.category] = (report.byCategory[property.category] || 0) + 1;

                // Count locations
                report.topLocations[property.found_location] = (report.topLocations[property.found_location] || 0) + 1;
            });

            // Find overdue properties (found more than 30 days ago and still pending)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0];

            report.overdueProperties = filteredProperties.filter(property =>
                property.found_date < thirtyDaysAgoStr &&
                ['found', 'investigating', 'owner_identified'].includes(property.status)
            );

            // Recent returns (last 7 days)
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            const sevenDaysAgoStr = sevenDaysAgo.toISOString().split('T')[0];

            report.recentReturns = filteredProperties.filter(property =>
                property.status === 'returned' &&
                property.returned_date >= sevenDaysAgoStr
            );

            // Calculate average processing time for returned items
            const returnedProperties = filteredProperties.filter(p => p.status === 'returned' && p.returned_date);
            if (returnedProperties.length > 0) {
                const totalDays = returnedProperties.reduce((sum, property) => {
                    const foundDate = new Date(property.found_date);
                    const returnedDate = new Date(property.returned_date);
                    const diffTime = Math.abs(returnedDate - foundDate);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return sum + diffDays;
                }, 0);
                report.averageProcessingTime = Math.round(totalDays / returnedProperties.length);
            }

            return report;
        } catch (error) {
            console.error('Error generating property report:', error);
            throw error;
        }
    }

    async getOverdueProperties() {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0];

            const properties = await this.getAllProperties();

            return properties.filter(property =>
                property.found_date < thirtyDaysAgoStr &&
                ['found', 'investigating', 'owner_identified'].includes(property.status)
            );
        } catch (error) {
            console.error('Error getting overdue properties:', error);
            throw error;
        }
    }

    async getPropertyTrends(days = 30) {
        try {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);

            const properties = await this.getAllProperties();

            // Filter properties within date range
            const filteredProperties = properties.filter(property => {
                const foundDate = new Date(property.found_date);
                return foundDate >= startDate && foundDate <= endDate;
            });

            // Group by date
            const trends = {};
            filteredProperties.forEach(property => {
                const date = property.found_date;
                if (!trends[date]) {
                    trends[date] = {
                        found: 0,
                        returned: 0,
                        handed_to_police: 0
                    };
                }
                trends[date].found++;

                if (property.status === 'returned' && property.returned_date) {
                    const returnDate = property.returned_date;
                    if (!trends[returnDate]) {
                        trends[returnDate] = { found: 0, returned: 0, handed_to_police: 0 };
                    }
                    trends[returnDate].returned++;
                }

                if (property.status === 'handed_to_police' && property.handed_to_police_date) {
                    const policeDate = property.handed_to_police_date;
                    if (!trends[policeDate]) {
                        trends[policeDate] = { found: 0, returned: 0, handed_to_police: 0 };
                    }
                    trends[policeDate].handed_to_police++;
                }
            });

            return trends;
        } catch (error) {
            console.error('Error getting property trends:', error);
            throw error;
        }
    }

    async getLocationAnalytics() {
        try {
            const properties = await this.getAllProperties();

            const locationStats = {};
            properties.forEach(property => {
                const location = property.found_location;
                if (!locationStats[location]) {
                    locationStats[location] = {
                        total: 0,
                        byType: {},
                        byStatus: {}
                    };
                }

                locationStats[location].total++;
                locationStats[location].byType[property.property_type] =
                    (locationStats[location].byType[property.property_type] || 0) + 1;
                locationStats[location].byStatus[property.status] =
                    (locationStats[location].byStatus[property.status] || 0) + 1;
            });

            // Sort by total count
            const sortedLocations = Object.entries(locationStats)
                .sort(([,a], [,b]) => b.total - a.total)
                .slice(0, 10); // Top 10 locations

            return Object.fromEntries(sortedLocations);
        } catch (error) {
            console.error('Error getting location analytics:', error);
            throw error;
        }
    }
}
