````markdown
# S.T.E.V.I DOS – Offline CLI Specification

**Supportive Technology to Enable Vulnerable Individuals**  
An offline, terminal‑style application for IHARC field staff to record and query outreach data.

---

## 1. Overview

- **Platform**: Windows (e.g. Panasonic Toughbook)  
- **Runtime**: Node.js packaged as a standalone binary (e.g. via `pkg` or `nexe`)  
- **Data backend**: Supabase (PostgreSQL + Auth + Role‑Level Security)  
- **Offline support**:  
  - Cache authentication token (JWT) and recent lookup results locally  
  - Allow full functionality offline; sync when connectivity returns  

---

## 2. Authentication & Security

1. **First‑run login**  
   ```bash
   $ stev i dos
   Username: <email>
   Password: ••••••
````

2. **Role enforcement**

   * Must belong to Supabase role `Iharc_staff`
   * On login, verify role before any command is allowed
3. **Local token cache**

   * Store JWT in OS‑protected storage
   * Expire or prompt re‑login when token is invalid
4. **Config file** (`~/.stevidosrc`)

   ```ini
   SUPABASE_URL=https://<your‑project>.supabase.co
   SUPABASE_ANON_KEY=<anon‑key>
   ```
5. **Offline mode**

   * Use cached JWT and data for all CLI operations
   * Gracefully degrade and mark records as “pending sync”

---

## 3. Command Reference

| Command                                   | Description                                                                  |       |             |                                                    |
| ----------------------------------------- | ---------------------------------------------------------------------------- | ----- | ----------- | -------------------------------------------------- |
| `login`                                   | Authenticate against Supabase and cache JWT                                  |       |             |                                                    |
| `report incident`                         | Guided wizard to file a new incident (date, location, narrative, tags)       |       |             |                                                    |
| \`lookup person                           | address                                                                      | plate | bike <id>\` | Display history and notes for the specified record |
| \`add person                              | address                                                                      | plate | bike\`      | Interactive prompts to create a new record         |
| `view image <media_id>`                   | Open associated image in the OS’s default viewer                             |       |             |                                                    |
| `export incident <id> --pdf [--template]` | Generate a PDF report using the chosen template; saves to `reports/` folder  |       |             |                                                    |
| `list templates`                          | Show all available PDF templates                                             |       |             |                                                    |
| `edit template <name>`                    | Open the template file in the user’s default text editor                     |       |             |                                                    |
| `history [--page n]`                      | Show past commands, with optional paging                                     |       |             |                                                    |
| `help [command]`                          | Display usage information for all or a specific command                      |       |             |                                                    |

---

## 4. PDF Report & Template System

### 4.1 Template Files

* Located in `~/.stevidos/templates/`
* JSON format defines layout, fields, and sections
* Example (`default.json`):

  ```json
  {
    "name": "default",
    "header": {
      "logo": "path/to/iharclogo.png",
      "title": "IHARC Incident Report",
      "fields": ["id", "created_at", "reporter_id"]
    },
    "sections": [
      { "type": "narrative", "label": "Summary" },
      { "type": "table",     "label": "Persons Involved", "columns": ["name","dob","notes"] },
      { "type": "media",     "label": "Photos", "maxPerRow": 2 }
    ],
    "footer": {
      "text": "Generated by S.T.E.V.I DOS on {{date}}"
    }
  }
  ```

### 4.2 Template Commands

```bash
# List all templates
stevidos> list templates

# Edit a template in your default editor
stevidos> edit template default
```

### 4.3 PDF Generation

* Uses [PDFKit](https://pdfkit.org/) under the hood
* Populates header, data tables, narrative, and embeds images
* Output saved to `reports/incident-<id>.pdf`
* Optional `--email` flag to send via configured SMTP

---

## 5. Data Model

```sql
-- incidents
CREATE TABLE incidents (
  id          BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at  TIMESTAMPTZ NOT NULL DEFAULT now(),
  reporter_id UUID REFERENCES auth.users(id),
  location    TEXT NOT NULL,
  narrative   TEXT NOT NULL
);

-- people, addresses, license_plates, bikes (standard PK + relevant fields)…

-- media attachments
CREATE TABLE media (
  id           BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  record_type  TEXT    NOT NULL,    -- e.g. 'incident', 'person'
  record_id    BIGINT  NOT NULL,    -- FK to the corresponding table
  filename     TEXT    NOT NULL,
  stored_at    TEXT    NOT NULL,    -- local path or Supabase Storage URL
  description  TEXT
);
```

* **RLS policies** restrict `SELECT/INSERT/UPDATE` to role `Iharc_staff`

---

## 6. Notifications

1. **In‑CLI Banner**

   * Displays at top when a new `--urgent` incident is filed
   * Remains until acknowledged (e.g. press `n` to jump to it)

2. **Windows Toast** (optional)

   * Uses PowerShell to trigger native notification
   * Requires Windows 10+ and proper script execution policy

---

## 7. UI/UX & Terminal Behavior

* **Full‑screen redraw** on each command using ANSI sequences:

  ```ansi
  \u001b[2J\u001b[H   # clear screen & reset cursor
  \u001b[31m... \u001b[39m  # red text
  \u001b[1m\u001b[37m... \u001b[0m  # bold white text
  ```
* **Fixed viewport** and limited scrollback (last 50 lines) via a TUI library (e.g. [blessed](https://github.com/chjj/blessed))
* **Interactive forms** for data entry, with clear prompts and validation feedback

---

## 8. Optional LLM Integration

* **Command**: `llm query "<natural language>"`
* **Function**: Translates free‑text requests into parameterized SQL
* **Use‑cases**:

  * “Show me all overdoses in Cobourg this month”
  * “List persons born before 1990 with a bike seizure”

---

## 9. Next Steps for AI Coding Agent

1. **Scaffold project structure**

   * Core modules: `auth/`, `commands/`, `data/`, `ui/`, `pdf/`, `media/`, `notifications/`
2. **Implement auth flow** with Supabase JS client
3. **Build TUI core** using your chosen library
4. **Create data‑access layer** for all tables + offline cache
5. **Add PDF export** with template loader & renderer
6. **Wire up image viewing** via OS shell commands
7. **Integrate notification hooks** for urgent incidents
8. **Optional**: LLM adapter layer for natural‑language queries

---

*End of specification.*

```
```
